import bftree from '@/utils/bftree'

export default {
  props: {
    treeId: {
      type: String,
      default: 'bfdx-tree__' + Date.now(),
    },
  },
  methods: {
    updateDeviceNodeTitle(device) {
      bftree.updateDeviceNodeTitle(this.treeId, device)
    },
    addOneDeviceNode(device) {
      bftree.addOneDeviceNode(this.treeId, device)
    },
    updateOneDeviceNode(device) {
      bftree.updateOneDeviceNode(this.treeId, device)
    },
    updateDbOrgNode(data) {
      bftree.updateOneOrgNode(this.treeId, data)
    },
    addOneOrgNode(data) {
      bftree.addOneOrgNode(this.treeId, data)
    },
    removeNode(data) {
      const key = typeof data === 'string'? data: data.rid
      bftree.removeNode(this.treeId, key)
    },
  },
  mounted() {
    bfglob.on('vorgs_table_add_data', this.addOneOrgNode)
    bfglob.on('vorgs_table_update_data', this.updateDbOrgNode)
    bfglob.on('vorgs_table_delete_data', this.removeNode)

    bfglob.on('updateDeviceNodeTitle', this.updateDeviceNodeTitle)
    bfglob.on('vdevices_table_add_data', this.addOneDeviceNode)
    bfglob.on('vdevices_table_update_data', this.updateOneDeviceNode)
    bfglob.on('vdevices_table_delete_data', this.removeNode)
  },
  beforeDestroy() {
    bfglob.off('vorgs_table_add_data', this.addOneOrgNode)
    bfglob.off('vorgs_table_update_data', this.updateDbOrgNode)
    bfglob.off('vorgs_table_delete_data', this.removeNode)

    bfglob.off('updateDeviceNodeTitle', this.updateDeviceNodeTitle)
    bfglob.off('vdevices_table_add_data', this.addOneDeviceNode)
    bfglob.off('vdevices_table_update_data', this.updateOneDeviceNode)
    bfglob.off('vdevices_table_delete_data', this.removeNode)
  },
}
