import { addDynamicGroupNode, removeNode, updateOneOrgNodeTitle } from '@/utils/bftree'

// 动态组节点操作
export default {
  props: {
    treeId: {
      type: String,
      default: 'bfdx-tree-' + Date.now(),
    },
  },
  methods: {
    // 动态组及成员操作
    addDynamicGroupNode(source) {
      addDynamicGroupNode(this.treeId, source)
    },
    updateDynamicGroupNode(source) {
      updateOneOrgNodeTitle(this.treeId, source)
    },
    deleteDynamicGroupNode(source) {
      removeNode(this.treeId, source.rid)
    },
  },
  mounted() {
    // 订阅动态组数据请求完毕事件，加载树节点
    bfglob.on('add_one_dynamic_group', this.addDynamicGroupNode)
    bfglob.on('update_one_dynamic_group', this.updateDynamicGroupNode)
    bfglob.on('delete_one_dynamic_group', this.deleteDynamicGroupNode)
  },
  beforeD<PERSON>roy() {
    bfglob.off('add_one_dynamic_group', this.addDynamicGroupNode)
    bfglob.off('update_one_dynamic_group', this.updateDynamicGroupNode)
    bfglob.off('delete_one_dynamic_group', this.deleteDynamicGroupNode)
  },
}
