import {
  addDynamicGroupDeviceNode,
  addDynamicGroupOrgNode,
  deleteDynamicGroupDeviceNode,
  deleteDynamicGroupOrgNode,
  updateDynamicGroupDeviceNode,
  updateDynamicGroupOrgNode,
} from '@/utils/bftree'

// 动态组详情节点操作
export default {
  props: {
    treeId: {
      type: String,
      default: 'bfdx-tree-' + Date.now(),
    },
  },
  methods: {
    addOneDynamicGroupDetail(detail) {
      if (detail.isDeviceGroup === 2) {
        addDynamicGroupOrgNode(this.treeId, detail)
      } else {
        addDynamicGroupDeviceNode(this.treeId, detail)
      }
    },
    updateOneDynamicGroupDetail(detail) {
      if (detail.isDeviceGroup === 2) {
        updateDynamicGroupOrgNode(this.treeId, detail)
      } else {
        updateDynamicGroupDeviceNode(this.treeId, detail)
      }
    },
    deleteOneDynamicGroupDetail(detail) {
      if (detail.isDeviceGroup === 2) {
        deleteDynamicGroupOrgNode(this.treeId, detail)
      } else {
        deleteDynamicGroupDeviceNode(this.treeId, detail)
      }
    },
  },
  mounted() {
    bfglob.on('add_one_dynamic_group_detail', this.addOneDynamicGroupDetail)
    bfglob.on('update_one_dynamic_group_detail', this.updateOneDynamicGroupDetail)
    bfglob.on('delete_one_dynamic_group_detail', this.deleteOneDynamicGroupDetail)
  },
  beforeDestroy() {
    bfglob.off('add_one_dynamic_group_detail', this.addOneDynamicGroupDetail)
    bfglob.off('update_one_dynamic_group_detail', this.updateOneDynamicGroupDetail)
    bfglob.off('delete_one_dynamic_group_detail', this.deleteOneDynamicGroupDetail)
  },
}
