// 全局缓存索引前缀
import { DateMask, nowLocalTime } from './time'

const StorageKeyPrefix = 'bf8100'

/**
 * 包装缓存的key,添加特定的前缀描述
 * @param {string} key 待包装的key
 * @returns {string} 包装后key
 */
export function wrapStorageKey(key) {
  return `${StorageKeyPrefix}::${key}`
}

/**
 * 读取localStorage中指定key的数据
 * @param key
 * @returns {*}
 */
export function getItem(key) {
  return window.localStorage.getItem(wrapStorageKey(key))
}

/**
 * 向localStorage中写入一个数据
 * @param {string} key
 * @param {*} value
 */
export function setItem(key, value) {
  if (typeof value === 'object') {
    value = JSON.stringify(value)
  }
  return window.localStorage.setItem(wrapStorageKey(key), value)
}

/**
 * 删除localStorage中指定的数据
 * @param {string} key
 */
export function removeItem(key) {
  window.localStorage.removeItem(wrapStorageKey(key))
}

/**
 * 生成检查授权快到期提示的kit
 * @returns {{
 *  loadAuthCheckTime: () => string|undefined,
 *  saveAuthCheckTime: () => void
 * }}
 */
export function useAuthChecker() {
  const key = wrapStorageKey('authCheckDate')
  const loadAuthCheckTime = () => {
    return window.localStorage.getItem(key)
  }
  const saveAuthCheckTime = () => {
    window.localStorage.setItem(key, nowLocalTime(DateMask))
  }

  return {
    loadAuthCheckTime,
    saveAuthCheckTime,
  }
}

/**
 * 缓存当前使用的语言数据
 * @param {string} lang
 */
export function saveLang(lang) {
  if (!lang) { return }

  const key = 'bfdx_account'
  let account = this.getItem(key)
  if (!account) { return }

  account = JSON.parse(account)
  account.locale = lang

  this.setItem(key, account)
  bfglob.emit('change_lang', lang)
}

export default {
  getItem,
  setItem,
  removeItem,
  saveLang,
}
