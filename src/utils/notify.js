import screenfull from 'screenfull'
import i18n from '@/modules/i18n'
import { h } from 'vue'
import bfutil from './bfutil'

const Notify = {}
export const Types = {
  success: 'success',
  info: 'info',
  warning: 'warning',
  error: 'error',
}
const Options = {
  type: Types.info,
  dangerouslyUseHTMLString: false,
  closeOnClickModal: false,
  closeOnPressEscape: false,
}
const MessageOptions = {
  ...Options,
  message: '',
  showClose: true,
  duration: 4500,

  // 自定义属性
  isVnode: false,
  detailMessage: '',
}
const NotifyOptions = {
  ...Options,
  title: '',
  message: '',
  type: Types.info,
  duration: 4500,
  offset: 0,
  position: 'bottom-right',
}
const PromptOptions = {
  ...Options,
}

function createErrorMessageVnode(opts) {
  opts = Object.assign({
    message: '',
    detailMessage: '',
  }, opts || {})

  let idx = 1
  const display_values = ['none', 'block']
  const icon_class = ['mdi mdi-menu-right', 'mdi mdi-menu-down']
  let isOpen = false
  const title_ref = 'message_title'
  const detail_ref = 'detail_message'

  const title = h('p', {
    ref: title_ref,
    class: {
      [icon_class[0]]: !isOpen,
      [icon_class[1]]: isOpen,
    },
    style: {
      cursor: 'pointer',
    },
    on: {
      click: () => {
        isOpen = idx % 2 === 0
        title.elm.classList.remove(icon_class[isOpen ? 1 : 0])
        title.elm.classList.add(icon_class[isOpen ? 0 : 1])

        detail.elm.style.display = display_values[idx % 2]
        idx++
      },
    },

  }, opts.message)

  const detail = h('div', {
    ref: detail_ref,
    style: {
      display: 'none',
      fontStyle: 'italic',
    },
  }, opts.detailMessage)

  const vnode = h('div', {
    class: {
      'bf-message-error': true,
    },
    style: {
      'padding-right': '24px',
    },
  }, [
    title,
    detail,
  ])

  return vnode
}

export function warningBox(msg = '', type = Types.warning, className = '') {
  return ElMessageBox.alert(msg, {
    ...Options,
    title: i18n.global.t('dialog.alertTitle'),
    type,
    confirmButtonText: i18n.global.t('dialog.confirm'),
    confirmButtonClass: `confirm_style ${className}`,
  })
}

export function warningBoxWithOption(msg, options = {}) {
  return ElMessageBox.alert(msg, {
    ...Options,
    title: i18n.global.t('dialog.alertTitle'),
    confirmButtonText: i18n.global.t('dialog.confirm'),
    confirmButtonClass: `confirm_style ${options.className}`,
    ...options,
  })
}

export function messageBox(msg = '', type = Types.info, opts = {}) {
  return new Promise(function(resolve, reject) {
    try {
      const options = {
        ...MessageOptions,
        ...opts,
        message: msg,
        type,
      }
      if (options.isVnode) {
        options.dangerouslyUseHTMLString = true
        options.message = createErrorMessageVnode({
          message: options.message,
          detailMessage: options.detailMessage,
        })
      }

      resolve(ElMessage(options))
    } catch (err) {
      reject(err)
    }
  })
}

export function notifyBox(opts = {}) {
  return new Promise((resolve, reject) => {
    try {
      // 同一个customClass的通知，应只现出一次。
      // 将通知实例缓存，生成新的通知前先将旧的关闭
      const options = {
        ...NotifyOptions,
        customClass: `${Date.now()}`,
        ...opts,
      }
      const prevNotify = Notify[options.customClass]
      if (prevNotify) {
        prevNotify.close()
        delete Notify[options.customClass]
      }

      const newNotify = ElNotification(options)
      Notify[options.customClass] = newNotify

      // 封装关闭通知事件，清除缓存的实例
      const originClose = newNotify.close
      newNotify.close = () => {
        originClose()
        delete Notify[options.customClass]
      }

      resolve(newNotify)
    } catch (err) {
      reject(err)
    }
  })
}

export function promptBox(opts) {
  const options = {
    ...PromptOptions,
    title: i18n.global.t('dialog.alertTitle'),
    cancelButtonText: i18n.global.t('dialog.cancel'),
    confirmButtonText: i18n.global.t('dialog.confirm'),
    inputErrorMessage: i18n.global.t('msgbox.processMoreRecordsErrMsg'),
    ...opts,
  }
  return ElMessageBox.prompt(options.message, options)
}

export function reqMaximumPrompt() {
  const message = `${i18n.global.t('msgbox.upperLimit')}${bfglob.reqMaximum}${i18n.global.t('dialog.records')},
  ${i18n.global.t('msgbox.processMoreRecords')}${i18n.global.t('msgbox.nextTimeEffect')}`
  const inputValidator = function(val) {
    if (isNaN(val)) {
      return i18n.global.t('msgbox.mustNumber')
    }
    if (bfglob.reqMaximum >= val) {
      return i18n.global.t('msgbox.processDataUpperLimitVal')
    }
    return true
  }
  const inputValue = bfglob.reqMaximum + 5000

  promptBox({
    message,
    inputValue,
    inputValidator,
  })
    .then(({
      value,
      action,
    }) => {
      if (action === 'confirm') { return { value: parseInt(value) } }
    })
    .then(({ value }) => {
      // 将用户输入的值转成数字
      bfglob.reqMaximum = value
    })
}

export default {
  warningBox,
  messageBox,
  notifyBox,
  promptBox,
  reqMaximumPrompt,
}

function isObject(o) {
  return Object.prototype.toString.call(o) === '[object Object]'
}

function onSiteNotify(options) {
  const icon = options.icon || bfglob.systemSetting.clientLogo || bfutil.default_user()
  const message = `<div class='siteNotify-content'>
    <img src='${icon}' class='siteNotify-image'>
    <span class='siteNotify-text'>${options.body}</span>
  </div>`

  notifyBox({
    title: options.title,
    dangerouslyUseHTMLString: true,
    message: message,
    type: 'info',
    duration: 8 * 1000,
    // offset: 25,
    onClick: typeof options.onClick === 'function' ? options.onClick : () => { },
    customClass: options.tag,
  })
}

function onDesktopNotify(options) {
  const opts = {
    body: options.body,
    data: options.data,
    tag: options.tag,
    icon: options.icon || bfglob.systemSetting.clientLogo || `/logo.${bfglob.siteConfig?.logoExt || 'jpg'}`
  }

  const notification = new Notification(options.title, opts)
  notification.onshow = function() {
    setTimeout(notification.close.bind(notification), 10 * 1000)
  }
  notification.onerror = function() {
    bfglob.console.error('notification error', notification)
    onSiteNotify(options)
  }
  if (typeof options.onClick === 'function') {
    notification.onclick = options.onClick
  }
}

export function createNotification(options) {
  if (!isObject(options)) {
    return
  }
  // 检测浏览器是否有权限显示桌面通知，没有则申请
  if (!window.Notification || screenfull.isFullscreen) {
    bfglob.console.error('浏览器不支持notification或全屏中被屏蔽')
    onSiteNotify(options)
    return
  }

  if (!window.location.hostname.includes('https')) {
    onSiteNotify(options)
    return
  }

  if (window.Notification && Notification.permission !== 'granted') {
    Notification.requestPermission()
      .then((permission) => {
        if (permission === 'granted') {
          onDesktopNotify(options)
        } else {
          onSiteNotify(options)
        }
      })
  } else {
    // 拥有权限
    onDesktopNotify(options)
  }
}
