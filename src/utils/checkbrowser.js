import { detect } from 'detect-browser'

const Browser = detect()

export function supportWebGL() {
  const cvs = document.createElement('canvas')
  const contextNames = ['webgl', 'experimental-webgl', 'moz-webgl', 'webkit-3d']
  let ctx

  try {
    if (navigator.userAgent.indexOf('MSIE') >= 0) {
      /* global WebGLHelper */
      ctx = WebGLHelper.CreateGLContext(cvs, 'canvas')
    } else {
      for (let i = 0; i < contextNames.length; i++) {
        ctx = cvs.getContext(contextNames[i])
        if (ctx) {
          break
        }
      }
    }
    // let WebGL = ctx ? 'Yay' : 'Nay'
    return !!ctx
  } catch (e) {
    console.warn('supportWebGL:', e)
    return false
  }
}

// declare type Browser = 'aol' | 'edge' | 'yandexbrowser' | 'vivaldi' | 'kakaotalk' | 'samsung' | 'silk' | 'miui' | 'beaker' | 'edge-chromium' | 'chrome' | 'chromium-webview' | 'phantomjs' | 'crios' | 'firefox' | 'fxios' | 'opera-mini' | 'opera' | 'ie' | 'bb10' | 'android' | 'ios' | 'safari' | 'facebook' | 'instagram' | 'ios-webview' | 'searchbot';
/**
 * 比较两个字符串版本号大小
 * version1 > version2: 1, version1 < version2: -1, version1 == version2: 0
 * @param version1 {string}
 * @param version2 {string}
 * @returns {number}
 */
function compareVersion(version1, version2) {
  const arr1 = version1.split('.')
  const arr2 = version2.split('.')
  const length1 = arr1.length
  const length2 = arr2.length
  const minlength = Math.min(length1, length2)
  let i = 0
  for (; i < minlength; i++) {
    const a = parseInt(arr1[i])
    const b = parseInt(arr2[i])
    if (a > b) {
      return 1
    } else if (a < b) {
      return -1
    }
  }

  if (length1 > length2) {
    for (let j = i; j < length1; j++) {
      if (parseInt(arr1[j]) !== 0) {
        return 1
      }
    }
    return 0
  } else if (length1 < length2) {
    for (let j = i; j < length2; j++) {
      if (parseInt(arr2[j]) !== 0) {
        return -1
      }
    }
    return 0
  }

  return 0
}

function checkVersion(version) {
  return compareVersion(version, Browser.version) >= 0
}

export function browserVersionIsTooLow() {
  switch (Browser.name) {
    case 'ie':
      // not support ie
      return true
    case 'chrome':
      return checkVersion('58')
    case 'firefox':
      return checkVersion('52')
    case 'opera':
      return checkVersion('45')
    case 'safari':
      return checkVersion('9.3')
    default:
      return false
  }
}

const lowVersion = browserVersionIsTooLow()

export function needUpgrade() {
  if (!supportWebGL()) {
    return true
  }

  // return lowVersion
  return false
}

export function upgradeType() {
  return lowVersion ? 1 : 2
}

window.location.hostname.startsWith('localhost') && console.log('detect', Browser)
