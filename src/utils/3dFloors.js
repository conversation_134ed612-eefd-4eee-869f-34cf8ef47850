import maputil from './map'
import { getGeo<PERSON><PERSON> } from '@/modules/geojson'

export function load3dFloorsLayers() {
  const F1Wall = getGeoJson('f1_wall')
  const F1Room = getGeoJson('f1_room')
  Promise.all([F1Wall, F1Room])
    .then(([f1_wall, f1_room]) => {
      const floors = {
        F1: {
          name: {
            id: 'room_floor_name',
            type: 'symbol',
            source: f1_room,
            paint: {
              'text-opacity': 1,
              'text-color': '#000',
            },
            layout: {
              'text-field': '{name}',
              'text-size': [
                'interpolate',
                ['linear'],
                ['zoom'],
                18, 10,
                19, 14,
                20, 20
              ],
            }

          },
          room: {
            id: 'room_floor',
            type: 'fill-extrusion',
            source: f1_room,
            paint: {
              'fill-extrusion-color': ['get', 'color'],
              'fill-extrusion-height': 0.5,
              'fill-extrusion-base': 0,
              'fill-extrusion-opacity': 1
            }
          },
          wall: {
            id: 'room_floor_wall',
            type: 'fill-extrusion',
            source: f1_wall,
            paint: {
              'fill-extrusion-color': '#bbb',
              'fill-extrusion-height': 1,
              'fill-extrusion-base': 0,
              'fill-extrusion-opacity': 1
            }
          }
        }
      }
      const getFloorLayerInfo = (floor) => {
        return floors[floor] || null
      }
      // 根据楼层不同绘制对应楼层的室内3d图层
      const floor1 = getFloorLayerInfo('F1')

      const loadRoom3dLayer = () => {
        maputil.add3dLayer(floor1.room)
        maputil.add3dLayer(floor1.name)
        maputil.add3dLayer(floor1.wall)
      }
      if (bfglob.map.isStyleLoaded()) {
        loadRoom3dLayer()
      } else {
        bfglob.map.on('load', loadRoom3dLayer)
      }
    })
}

export default {
  load3dFloorsLayers
}
