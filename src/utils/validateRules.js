import i18n from '@/modules/i18n'
import { requestDbDataByDmrId } from '@/utils/bfprocess'
import { debounce } from 'lodash'

// 手机号正则
const mobilePhoneStr = '^1[3-8]\\d{9}$'
const mobilePhoneReg = new RegExp(mobilePhoneStr)

// 固话正则
const landlinePhoneStr = '^((0\\d{2,3})-?)?(\\d{7,8})(-?(\\d{1,4}))?$'
const landlinePhoneReg = new RegExp(landlinePhoneStr)

// 特殊电话号码，一般3到5位，如120,10086,95566,12306
const specialPhoneStr = '^[0-9]{3,6}$'
// const specialPhoneReg = new RegExp(specialPhoneStr)

// 电话号码正则
const PhoneStr = `${mobilePhoneStr}|${landlinePhoneStr}|${specialPhoneStr}`
const PhoneReg = new RegExp(PhoneStr)

// 黑白名单号码正则，不能连续输入多个"*"，其他不限制
const blackWhiteListReg = /^(?!.*\*\*)[\d?*]*$/ig
const hexReg = /^([0-9A-Fa-f]{1,2})+$/i

// 域名和IP正则
// eslint-disable-next-line no-useless-escape
const domainReg = /^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)+([A-Za-z]|[A-Za-z][A-Za-z0-9\-]*[A-Za-z0-9])$/
const ipReg = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

export default {
  required(trigger = 'blur', message = i18n.global.t('dialog.requiredRule')) {
    return {
      required: true,
      message: message,
      trigger: trigger,
    }
  },
  number(trigger = 'blur', message = i18n.global.t('msgbox.mustNumber')) {
    return {
      type: 'number',
      message: message,
      trigger: trigger,
    }
  },
  mustNumber(trigger = 'blur', message = i18n.global.t('msgbox.mustNumber')) {
    return {
      validator: (rule, value, callback) => {
        if (isNaN(value)) {
          callback(new Error(message))
        } else {
          callback()
        }
      },
      trigger: trigger,
    }
  },
  mustLength(trigger = 'blur', len = 6, message) {
    return {
      validator: (rule, value, callback) => {
        if (typeof value !== 'string' || (value.length > 0 && value.length !== len)) {
          if (!message) {
            message = i18n.global.t('msgbox.mustLength', { len })
          }
          callback(new Error(message))
        } else {
          callback()
        }
      },
      trigger: trigger,
    }
  },
  maxLen(trigger = 'blur', value = 16, message) {
    return {
      max: value,
      message: message || (i18n.global.t('msgbox.maxLen') + value + i18n.global.t('msgbox.character')),
      trigger: trigger,
    }
  },
  minLen(trigger = 'blur', value = 0, message) {
    return {
      max: value,
      message: message || (i18n.global.t('msgbox.minLen') + value + i18n.global.t('msgbox.character')),
      trigger: trigger,
    }
  },
  // 验证手机和固定电话号码
  telephoneNumber(trigger = 'blur', message) {
    return {
      type: 'string',
      pattern: PhoneReg,
      message: message || i18n.global.t('msgbox.enterCorrectPhoneNumber'),
      trigger: trigger,
    }
  },
  // 验证手机号码
  mobilePhoneNumber(trigger = 'blur', message) {
    return {
      type: 'string',
      pattern: mobilePhoneReg,
      message: message || i18n.global.t('msgbox.enterCorrectMobilePhoneNumber'),
      trigger: trigger,
    }
  },
  // 验证固定电话号码
  landlinePhoneNumber(trigger = 'blur', message) {
    return {
      type: 'string',
      pattern: landlinePhoneReg,
      message: message || i18n.global.t('msgbox.enterCorrectLandlinePhoneNumber'),
      trigger: trigger,
    }
  },
  // 黑白名单输入验证
  blackWhiteListRule(trigger = 'blur', message) {
    return {
      type: 'string',
      pattern: blackWhiteListReg,
      message: message || i18n.global.t('msgbox.inputErrAndSeeHelpInfo'),
      trigger: trigger,
    }
  },
  range(trigger = 'blur', min = 0, max = 1000, message) {
    return {
      validator: (rule, value, callback) => {
        if (isNaN(value)) {
          callback(message || new Error(i18n.global.t('msgbox.mustNumber')))
        } else if (value < min || value > max) {
          callback(message || new Error(min + ' ~ ' + max))
        } else {
          callback()
        }
      },
      trigger: trigger,
    }
  },
  hex(trigger = 'blur', message) {
    return {
      validator: (rule, value, callback) => {
        message = message || i18n.global.t('msgbox.mustBeHex')
        if (typeof value === 'string' && hexReg.test(value)) {
          callback()
        } else {
          callback(message)
        }
      },
      trigger: trigger,
    }
  },
  checkIp(trigger = 'blur', message) {
    return {
      validator: (rule, value, callback) => {
        message = message || i18n.global.t('msgbox.validIp')
        if (ipReg.test(value)) {
          callback()
        } else {
          callback(message)
        }
      },
      trigger: trigger,
    }
  },
  checkDomain(trigger = 'blur', message) {
    return {
      validator: (rule, value, callback) => {
        message = message || i18n.global.t('msgbox.validDomain')
        if (domainReg.test(value)) {
          callback()
        } else {
          callback(message)
        }
      },
      trigger: trigger,
    }
  },
  checkHost(trigger = 'blur', message) {
    return {
      validator: (rule, value, callback) => {
        message = message || i18n.global.t('msgbox.validHost')
        if (ipReg.test(value)) {
          callback()
        } else if (domainReg.test(value)) {
          callback()
        } else {
          callback(message)
        }
      },
      trigger: trigger,
    }
  },
  validateDmrId({ encodeMsgType, decodeMsgType, command, dataManager, }) {
    return debounce(async (rule, value, callback) => {
      const dbData = dataManager.getDataByIndex(value)
      if (dbData) {
        callback(new Error(i18n.global.t('dialog.deviceDmrIdIsExisted')))
        return
      }
      const dbDataByNetWork = await requestDbDataByDmrId(value, encodeMsgType, decodeMsgType, command)
      if (dbDataByNetWork) {
        if (dbDataByNetWork.dmrId !== value) {
          return
        }
        callback(new Error(i18n.global.t('dialog.deviceDmrIdIsExisted')))
      } else {
        callback()
      }
    }, 500)
  },
}
