import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import i18n from '@/modules/i18n'
import bfutil, { formatDmrIdLabel, getDynamicGroupOrgType } from './bfutil'
import bfTime from './time'

let loadFlag = Date.now()
const onlineFilterReg = /device_status_green|device_status_yellow|device_status_red|device_status_other_red|device_status_icon_th|device_status_emergency_yellow|device_status_emergency_green/i
const nodeOpeationOption = {
  redraw: true, force: true,
}

export const LocalTree = {}
export const defaultTreeId = 'bftree'
export const defaultOrgNodeOption = {
  title: '',
  folder: true,
  expanded: true,
  key: Date.now().toString(),
  icon: false,
  selected: true,

  // 自定义属性
  isOrg: true,
  showCounter: true,
}
export const defaultDeviceNodeOption = {
  title: '',
  folder: false,
  key: Date.now().toString(),
  icon: false,
  selected: true,

  // 自定义属性
  isOrg: false,
}

function mergeOption(...options) {
  let option = {
    ...nodeOpeationOption,
  }

  for (let i = 0; i < options.length; i++) {
    const opt = options[i]
    option = Object.assign(option, opt)
  }

  return option
}

// 树型查找节点相关方法
export function getTree(treeId = defaultTreeId) {
  return LocalTree[treeId]
}

export function getRootNode(treeId = defaultTreeId) {
  const tree = getTree(treeId)
  if (!tree) {
    return
  }
  return tree.getRootNode()
}

export function getNodeByKey(treeId = defaultTreeId, key) {
  const tree = getTree(treeId)
  if (!tree) {
    return
  }
  return tree.getNodeByKey(key)
}

// 自定义 fancytree 节点排序比较方法
const fullCallDmrIdStr = bfglob.fullCallDmrId.toString()

export function defaultSortMethod(a, b) {
  // 全呼目标不参与排序
  if (a.key === fullCallDmrIdStr || b.key === fullCallDmrIdStr) {
    return 0
  }
  const sortType = bfglob.userInfo.setting.fancytreeSortType
  // sortString 为 fancytree node 自定义属性
  const x = a.data.sortString + ''
  const y = b.data.sortString + ''
  // 将单位节点排在设备节点后面,sortType 为 false 则将单位和设备排序反转
  if (a.isFolder() && !b.isFolder()) {
    return sortType ? 1 : -1
  }
  if (!a.isFolder() && b.isFolder()) {
    return sortType ? -1 : 1
  }

  // 单位节点排序，必须按排序值排序
  if (a.isFolder() && b.isFolder()) {
    return x - y
  }

  return x.localeCompare(y)
}

export function sortChildren(treeId = defaultTreeId, sortMethod, deep = true, option) {
  // 重置option参数
  option = mergeOption(option)

  const rootNode = getRootNode(treeId)
  if (typeof sortMethod !== 'function') {
    sortMethod = defaultSortMethod
  }
  rootNode && rootNode.sortChildren(sortMethod, deep)
  if (option.redraw) {
    redrawViewport(treeId, option.force)
  }
}

export function removeNode(treeId = defaultTreeId, key, option) {
  // 重置option参数
  const opts = mergeOption(option)

  const node = getNodeByKey(treeId, key)
  if (!node) {
    return
  }
  node.remove()
  opts.redraw && redrawViewport(treeId, opts.force)
}

export function removeNodeChildren(treeId = defaultTreeId, key, option) {
  // 重置option参数
  option = mergeOption(option)

  const node = getNodeByKey(treeId, key)
  if (!node) {
    return
  }
  node.removeChildren()
  option.redraw && redrawViewport(treeId, option.force)
}

export function moveNode(treeId, source, option) {
  // 重置option参数
  option = mergeOption(option)

  const parentKey = source[option.parentKey || 'parentOrgId']
  const targetNode = getNodeByKey(treeId, parentKey)
  const currentNode = getNodeByKey(treeId, source.rid)
  if (!targetNode || !currentNode) {
    return
  }

  if (!option.mode) {
    option.mode = 'child'
  }
  currentNode.moveTo(targetNode, option.mode)
  if (!targetNode.isExpanded()) {
    targetNode.setExpanded(true)
  }
  targetNode.sortChildren(option.sortMethod || defaultSortMethod, true)
  option.redraw && redrawViewport(treeId, option.force)
}

export function addFullCallNode(treeId = defaultTreeId, option = {}) {
  const fullCallData = {
    dmrId: bfglob.fullCallDmrId,
    rid: bfglob.fullCallDmrId,
    orgShortName: i18n.global.t('dialog.fullCall'),
    folder: true,
    sortString: 1,
  }
  const rootNode = getRootNode(treeId)
  if (!rootNode) {
    return
  }
  const opts = {
    selected: false, ...option,
  }
  rootNode.addChildren(createOrgNodeData(fullCallData, opts), 0)
}

export function selectAll(treeId = defaultTreeId, status = true) {
  const tree = getTree(treeId)
  if (!tree) {
    return
  }
  tree.selectAll(status)
}

export function expandAll(treeId = defaultTreeId, flag = true, opts = {}) {
  const tree = getTree(treeId)
  if (!tree) {
    return
  }
  tree.expandAll(flag, opts)
}

export function setSelected(treeId = defaultTreeId, key = '', status = true) {
  const node = getNodeByKey(treeId, key)
  if (!node) {
    return
  }
  node.setSelected(status)
}

export function adjustViewportSize(treeId = defaultTreeId) {
  if (!checkTreeIsNeedRedrawViewport(treeId)) {
    return
  }

  const tree = getTree(treeId)
  if (!tree) {
    return
  }

  tree.adjustViewportSize()
}

// 检测树结构是否需要重绘viewport，需要重绘则返回tree
export function checkTreeIsNeedRedrawViewport(treeId = defaultTreeId) {
  const tree = getTree(treeId)
  if (!tree) {
    return false
  }
  return !!(tree.ext && tree.ext.grid)
}

export function redrawViewport(treeId = defaultTreeId, force = false) {
  if (!checkTreeIsNeedRedrawViewport(treeId)) {
    return
  }

  const tree = getTree(treeId)
  if (!tree) {
    return
  }

  tree.redrawViewport(force)
}

export function clearTree(treeId = defaultTreeId) {
  const tree = getTree(treeId)
  if (!tree) {
    return
  }
  tree.clear()
}

export function clearFilter(treeId = defaultTreeId) {
  const tree = getTree(treeId)
  if (!tree) {
    return
  }
  tree.clearFilter()
}

export function filterNodes(treeId = defaultTreeId, filterMethod) {
  const tree = getTree(treeId)
  if (!tree) {
    return
  }

  if (typeof filterMethod !== 'function') {
    filterMethod = (node) => {
      return !!node.title
    }
  }
  tree.clearFilter()
  tree.filterNodes(filterMethod, tree.options.filter)
}

// 清除指定节点的选中及禁用状态，不指定节点的key，则默认为根节点
export function clearNodeUnselectable(treeId = defaultTreeId, key = '', option = defaultDeviceNodeOption) {
  const initNode = key ? getNodeByKey(treeId, key) : getRootNode(treeId)
  if (!initNode) {
    return
  }

  // 如果key不存在，则默认为根节点，深度处理子节点
  if (!key) {
    option.deep = true
  }
  // 重置配置项参数
  option.selected = !!option.selected

  // 使用队列形式循环处理节点和子节点
  const queue = [initNode]
  while (queue.length) {
    const node = queue.shift()
    if (!node) {
      continue
    }

    node.setSelected(option.selected)
    node.unselectable = undefined
    node.unselectableStatus = undefined

    if (typeof option.reset === 'function') {
      option.reset(node)
    }

    node.render()

    // 深度处理子节点
    if (option.deep) {
      const children = node.getChildren()
      if (children) {
        Array.prototype.push.apply(queue, children)
      }
    }
  }

  option.redraw && redrawViewport(treeId, option.force)
}

export function resetNodeUnselectable(treeId = defaultTreeId, key, option = {}) {
  const opts = {
    reset(node) {
      // 系统外数据，保持禁止选择状态
      node.unselectable = node.data.otherOwner || undefined
      node.unselectableStatus = node.data.otherOwner || undefined
    },
    ...option,
  }
  clearNodeUnselectable(treeId, key, opts)
}

export function nodeUnselectable(treeId = defaultTreeId, key = '', option) {
  const node = getNodeByKey(treeId, key)
  if (!node) {
    return
  }

  const opts = {
    unselectable: undefined, unselectableStatus: undefined, ...option,
  }

  node.unselectable = opts.unselectable
  node.unselectableStatus = opts.unselectableStatus

  node.render()
}

export function addNodeChildren(treeId, nodeKey, children = [], pos = null) {
  const node = getNodeByKey(treeId, nodeKey)
  if (!node) {
    return
  }
  return node.addChildren(children, pos)
}

export function isRootNode(node) {
  return node?.key === 'root_1'
}

export function toDictTree(desTreeId, srcTreeId, toDictCb) {
  return new Promise((resolve, reject) => {
    const desRootNode = getRootNode(desTreeId)
    if (!desRootNode) {
      return reject()
    }
    const srcTree = getTree(srcTreeId)
    if (!srcTree) {
      return reject()
    }

    const toDictTree = srcTree.toDict(false, (dict) => {
      // 默认逻辑
      dict.partsel = false
      dict.selected = false
      dict.expanded = true

      return toDictCb(dict)
    })
    if (typeof toDictTree === 'undefined') {
      return reject()
    }

    desRootNode.removeChildren()
    desRootNode.addChildren(toDictTree)
    sortChildren(desTreeId)
    setTimeout(() => {
      resolve()
    }, 0)
  })
}

function getOrgNodeExtraData(org_item) {
  const orgIsVirtual = org_item.orgIsVirtual === 1

  return {
    sortString: org_item.orgSortValue,
    dmrId: org_item.dmrId,
    virtual: orgIsVirtual,
    parentOrgId: org_item.parentOrgId,
    orgIsVirtual: org_item.orgIsVirtual,
  }
}

function syncOrgNodeExtraData(node, org) {
  if (!node.data) {
    node.data = {}
  }

  Object.assign(node.data, getOrgNodeExtraData(org))
}

const dynamicGroupOrgType = getDynamicGroupOrgType()
const dynamicGroupStatusClasses = {
  // 0: 'init',
  1: 'valid', 10: 'invalid', // 3: 'deldete',
}

export function getOrgTaskGroupStatus(orgData) {
  if (!orgData) {
    return ''
  }

  // 不是动态组，没有状态
  if (!dynamicGroupOrgType.includes(orgData.orgIsVirtual)) {
    return ''
  }

  // dynamic_group_state  1:正常 10:失效 / 删除中
  return dynamicGroupStatusClasses[orgData.dynamicGroupState] || dynamicGroupStatusClasses[10]
}

const orgNodeIconNames = {
  1: 'icon-virtual-org', 100: 'icon-temp-group', 102: 'icon-temp-group', 101: 'icon-task-group',
}

export function getOrgNodeIcon(orgData) {
  if (!orgData) {
    return false
  }
  // 1：虚拟组，2：真实组，101：任务组，100：临时组
  return orgNodeIconNames[orgData.orgIsVirtual] || false
}

// 单位数据挂载、更新等方法
export function getOrgNodeTitle(org_item, option) {
  option = mergeOption(defaultOrgNodeOption, option)
  const iconClass = getOrgNodeIcon(org_item)
  let title = ''
  let cls = 'org-title'
  if (iconClass) {
    title += `<i class='iconfont ${iconClass} ${getOrgTaskGroupStatus(org_item)}'></i>`
    cls += (' ' + iconClass.replace('icon-', 'is-'))
  }
  title += `<span class='${cls}'>${org_item.orgShortName}</span>`
  if (option.showCounter) {
    title += org_item.sumDeviceCount ? `<span class='allDevCount'>${org_item.sumDeviceCount}</span>` : ''
  }
  if (option.userPerm && org_item.orgIsVirtual === 2) {
    title += `<span class="user-perm-flag ${(option.includeChildrenIconDisabled ?? false) ? 'disabled' : ''}" data-include-children="${option.includeChildren ?? 1}">
    <i class="iconfont ${option.includeChildrenIcon ?? 'icon-select-all'}"></i>
    </span>`
  }
  return title
}

export function getOrgNodeTooltip(org_item) {
  return `${formatDmrIdLabel(org_item.dmrId)} (${`0x${org_item.dmrId}` & 0x7FFFF})`
  // return `${org_item.dmrId} / ${`0x${org_item.dmrId}` & 0x7FFFF}`
}

export function createOrgNodeData(org_item, option = {}) {
  if (!org_item) {
    return {
      ...defaultOrgNodeOption, children: [], ...option,
    }
  }
  // 1为虚拟组，2为真实组
  const orgIsVirtual = org_item.orgIsVirtual === 1
  return {
    ...defaultOrgNodeOption,
    title: getOrgNodeTitle(org_item, option),
    tooltip: getOrgNodeTooltip(org_item),
    folder: true,
    expanded: !orgIsVirtual,
    key: org_item.rid,
    icon: false,
    children: [],
    ...getOrgNodeExtraData(org_item),
    ...option,
  }
}

export function createTreeOrgSource(orgList = [], option) {
  const result = []

  if (orgList.length === 0) {
    return result
  }
  const processCache = {}
  // 先生成数据在数组中的索引
  const indexMap = orgList.map((d, i) => {
    return { [d.rid]: i }
  }).reduce((p, c) => Object.assign(p, c), {})

  function addNode(parentNode, orgItem, itemPos) {
    if (orgItem.rid === bfutil.DefOrgRid) return null

    const currentNode = createOrgNodeData(orgItem, option)
    processCache[orgItem.rid] = currentNode

    if (parentNode) {
      parentNode.children.push(currentNode)
    } else {
      result.push(currentNode)
    }
    orgList[itemPos] = null

    return currentNode
  }

  //返回currentData的父级Node,可以为null
  function addParent(currentPos, currentData) {
    // 过滤root节点数据
    if (currentData.parentOrgId === bfutil.DefOrgRid) {
      return null
    }

    //可能前面已经加入了
    let parentNode = processCache[currentData.parentOrgId]

    if (parentNode) {
      return parentNode
    }

    const parentPos = indexMap[currentData.parentOrgId] ?? 0
    const parentItem = orgList[parentPos]

    // 不能在数组后面查找，上级可能在前面
    // //在后面的找一遍
    // let parentPos = 0
    // let parentItem = null
    // for (let i = currentPos + 1; i < orgList.length; i++) {
    //   const item = orgList[i]
    //
    //   if (!item) continue
    //
    //   if (item.rid === currentData.parentOrgId) {
    //     parentPos = i
    //     parentItem = item
    //     break
    //   }
    // }

    //没有parent
    if (parentPos === 0) return null
    //循环添加上级
    parentNode = addParent(parentPos, parentItem)
    //返回currentData的上级
    return addNode(parentNode, parentItem, parentPos)
  }

  for (let i = 0; i < orgList.length; i++) {
    const idata = orgList[i]
    // 已经处理过了
    if (!idata) continue
    // 忽略root
    if (idata.rid === bfutil.DefOrgRid) continue
    // 添加parent
    const parentNode = addParent(i, idata)
    //添加自己
    addNode(parentNode, idata, i)
  }

  return result
}

export function addOrgNodeToTree(treeId, nodes) {
  if (!treeId || !nodes) {
    return
  }
  const rootNode = getRootNode(treeId)
  if (!rootNode) {
    return
  }
  rootNode.addChildren(nodes)
  // 排序
  sortChildren(treeId)
}

export function addOneOrgNode(treeId, source, option) {
  // 重置option参数
  option = mergeOption(option)

  const parentNode = source.parentOrgId === bfutil.DefOrgRid ? getRootNode(treeId) : getNodeByKey(treeId, source.parentOrgId)
  if (!parentNode) {
    return
  }
  const node = parentNode.addChildren(createOrgNodeData(source, option))
  if (!parentNode.isExpanded()) {
    parentNode.setExpanded(true)
  }
  parentNode.sortChildren(option.sortMethod || defaultSortMethod, true)
  option.redraw && redrawViewport(treeId, option.force)
  return node
}

export function updateOneOrgNode(treeId, source, option) {
  // 重置option参数
  option = mergeOption(option)

  moveNode(treeId, source, option)

  const node = getNodeByKey(treeId, source.rid)
  if (!node) {
    return
  }

  // node.data.sortString = source.orgSortValue
  node.tooltip = getOrgNodeTooltip(source)
  syncOrgNodeExtraData(node, source)
  node.setTitle(getOrgNodeTitle(source, option))

  const parentNode = getNodeByKey(treeId, source.parentOrgId)
  if (parentNode) {
    parentNode.sortChildren(option.sortMethod || defaultSortMethod, true)
  }

  option.redraw && redrawViewport(treeId, option.force)
}

export function delOneOrgNode(treeId, key, option) {
  // 重置option参数
  option = mergeOption(option)
  removeNode(treeId, key, option)
}

export function updateOneOrgNodeTitle(treeId, source, option) {
  // 重置option参数
  option = mergeOption(option)

  const node = getNodeByKey(treeId, source.rid)
  if (!node) {
    return
  }
  node.tooltip = getOrgNodeTooltip(source)
  syncOrgNodeExtraData(node, source)
  node.setTitle(getOrgNodeTitle(source, option))
  option.redraw && redrawViewport(treeId, option.force)
}

// 重新计算单位下终端数量
export function clearTreeOrgDeviceCount() {
  const g_org = bfglob.gorgData.getAll()
  for (const i in g_org) {
    g_org[i].ownDeviceCount = 0
    g_org[i].sumDeviceCount = -1
  }
}

export function calculateTreeOrgSubOrgDeviceCount(org_item) {
  if (org_item.orgIsVirtual === 1) {
    return org_item.ownDeviceCount
  }
  const sub_org = []
  const g_org = bfglob.gorgData.getAll()
  for (const i in g_org) {
    if (g_org[i].parentOrgId === org_item.rid) {
      if (org_item.rid !== g_org[i].rid) {
        sub_org.push(g_org[i])
      }
    }
  }

  let result = org_item.ownDeviceCount
  for (const j in sub_org) {
    if (sub_org[j].orgIsVirtual === 1) {
      continue
    }

    if (sub_org[j].sumDeviceCount >= 0) {
      result += sub_org[j].sumDeviceCount
    } else {
      result += calculateTreeOrgSubOrgDeviceCount(sub_org[j])
    }
  }

  return result
}

export function updateTreeOrgDeviceCount() {
  clearTreeOrgDeviceCount()
  const g_org = bfglob.gorgData.getAll()
  const g_device = bfglob.gdevices.getAll()
  for (const i in g_org) {
    for (const j in g_device) {
      if (g_device[j].orgId === g_org[i].rid) {
        g_org[i].ownDeviceCount++
      }
      if (g_device[j].virOrgs.includes(g_org[i].rid)) {
        g_org[i].ownDeviceCount++
      }
    }
  }

  // sum all the device of the org
  for (const k in g_org) {
    if (g_org[k].sumDeviceCount >= 0) {
      continue
    }
    g_org[k].sumDeviceCount = calculateTreeOrgSubOrgDeviceCount(g_org[k])
  }
}

export function refreshTreeOrgDeviceCount(treeId = defaultTreeId, option) {
  updateTreeOrgDeviceCount()
  renderOrgCounter(treeId, option)
}

export function renderOrgCounter(treeId = defaultTreeId, option) {
  option = mergeOption(option, {
    redraw: false, force: true,
  })
  const g_org = bfglob.gorgData.getAll()
  for (const k in g_org) {
    updateOneOrgNodeTitle(treeId, g_org[k], option)
  }
  sortChildren(treeId)
  redrawViewport(treeId, option.force)
}

// 终端节点操作方法
// 判断对讲机最后数据时间，如果在半小时内则视为在线
export function checked_device_is_online(device) {
  let lastDataTime = device.lastDataTime ?? ''
  lastDataTime = bfTime.utcTimeToLocalTime(lastDataTime)
  const time = bfglob.sysConfig.maxAgeOfOnline || 30
  // 在线则返回true
  return dayjs(lastDataTime).add(time, 'm').isAfter(dayjs())
}

// 获取对讲机状态 className
export function get_device_status_className(device) {
  // 最后数据时间在半小时外，视为关机，不显示状态
  if (!checked_device_is_online(device)) {
    return 'device_status_none'
  }

  // 先判断是否关机，如果最后数据时间、最后开机时间都在最后关机是前，则视为关机状态
  const lastPoweroffTime = new Date(device.lastPoweroffTime).getTime()
  const lastPoweronTime = new Date(device.lastPoweronTime).getTime()
  const lastDataTime = new Date(device.lastDataTime).getTime()
  if (lastPoweroffTime >= lastDataTime && lastPoweroffTime >= lastPoweronTime) {
    // 关机超过10分钟，显示浅灰色
    if (new Date(bfTime.nowUtcTime()).getTime() - lastPoweroffTime > 10 * 60 * 1000) {
      return 'device_status_light_gray'
    }
    return 'device_status_gray'
  }

  // 当前为开机状态，需要处理在线的各种状态
  // speaking
  if (bfutil.deviceIsInCalling(device)) {
    return 'device_status_icon_th'
  }

  if (typeof device.msStatusBin === 'undefined') {
    device.msStatusBin = [0, 0, 0, 0, 0, 0]
  }
  // emergency
  if (device.msStatusBin[3] & 0x01) {
    if (device.av === 1 && bfutil.checkedDeviceLastLonValid(device)) {
      return 'device_status_emergency_green'
    }
    return 'device_status_emergency_yellow'
  }

  // other alarm
  if (device.msStatusBin[3] & 0xFE) {
    if (device.av === 1 && bfutil.checkedDeviceLastLonValid(device)) {
      return 'device_status_green device_status_other_red'
    }
    return 'device_status_yellow device_status_other_red'
  }

  // locate
  if (device.av === 1 && bfutil.checkedDeviceLastLonValid(device)) {
    return 'device_status_green'
  }

  // gps not valid,but online
  return 'device_status_yellow'
}

export function deviceIsOnlineFromClassName(cls) {
  // 手台状态classname包含'none','gray'，则为不在线，返回false
  return !(cls.includes('none') || cls.includes('gray'))
}

export function deviceIsLocateFromClassName(cls) {
  // 手台状态classname包含'green'，则为已定位，返回true
  return cls.includes('green')
}

export function deviceIsOnline(device) {
  const cls = get_device_status_className(device)
  return deviceIsOnlineFromClassName(cls)
}

export function getDeviceChannel(device) {
  if (!checked_device_is_online(device)) {
    return ''
  }

  if (device.msStatusBin.length < 6) {
    device.msStatus = '000000000000'
    device.msStatusBin = [0, 0, 0, 0, 0, 0]
    return ''
  }
  let channel = (device.msStatusBin[4] & 0x1f)
  channel += (device.msStatusBin[5] & 0x7f) << 5
  if (!channel) {
    channel = ''
  }

  return channel
}

export function getDeviceUserName(device) {
  if (device.lastRfidPerson === bfutil.DefOrgRid || !device.lastRfidPerson) {
    return device.deviceUserName ?? ''
  } else {
    return device.userName ?? ''
  }
}

export function getDeviceNodeTitle(device, options) {
  const opts = {
    showStatus: true, showChannel: true, showUser: true, ...options,
  }

  let title = ''
  if (opts.showStatus) {
    title += `<i class='device_status ${get_device_status_className(device)}'></i>`
  }
  title += `<span class='device_selfId'>${device.selfId}</span>`

  if (opts.showChannel) {
    const channel = getDeviceChannel(device)
    if (channel) {
      title += `<span class='device_channel'> ${channel} </span>`
    }
  }

  if (opts.showUser) {
    title += `<span class='device_user_name'>${getDeviceUserName(device)}</span>`
  }

  return title
}

export function getDeviceNodeTooltip(device) {
  const dmrIdLabel = `${formatDmrIdLabel(device.dmrId)} (${`0x${device.dmrId}` & 0x7FFFF})`

  let userRid = device.deviceUser
  // 如果有终端读卡，则使用最后读卡的人员
  if (device.lastRfidPerson && device.lastRfidPerson !== bfutil.DefOrgRid) {
    userRid = device.lastRfidPerson
  }
  const userData = bfglob.guserData.get(userRid)
  const userPhone = userData?.userPhone ?? ''
  if (userPhone) {
    return `${dmrIdLabel} ${userPhone}`
  }

  return dmrIdLabel
}

function getDeviceNodeExtraData(device) {
  return {
    // 应工程部廖工建议，改由dmrId进行排序
    sortString: device.dmrId, dmrId: device.dmrId, deviceType: device.deviceType,
  }
}

function syncDeviceNodeExtraData(node, device) {
  if (!node.data) {
    node.data = {}
  }

  Object.assign(node.data, getDeviceNodeExtraData(device))
}

export function updateDeviceNodeTitle(treeId, device, options) {
  const node = getNodeByKey(treeId, device.rid)
  if (!node) {
    return
  }

  const title = getDeviceNodeTitle(device, options)
  const tooltip = getDeviceNodeTooltip(device)
  node.tooltip = tooltip
  syncDeviceNodeExtraData(node, device)
  node.setTitle(title)

  // 更新虚拟组织下的对讲机 title
  if (node.data.virNodeKey) {
    for (let k = 0; k < node.data.virNodeKey.length; ++k) {
      const key = node.data.virNodeKey[k]
      const vNode = getNodeByKey(treeId, key)
      if (!vNode) {
        continue
      }
      vNode.tooltip = tooltip
      syncDeviceNodeExtraData(vNode, device)
      vNode.setTitle(title)
    }
  }
}

export function getDeviceNodeData(device, options) {
  const opts = {
    selected: true, ...options,
  }
  return {
    title: getDeviceNodeTitle(device, options),
    tooltip: getDeviceNodeTooltip(device),
    key: device.rid,
    icon: false,
    selected: opts.selected,
    folder: false,
    ...getDeviceNodeExtraData(device),
  }
}

export function addOneDeviceNode(treeId, device, option) {
  const parentNode = getNodeByKey(treeId, device.orgId)
  if (!parentNode) {
    return
  }

  // 重置option参数
  option = mergeOption(option)
  // 生成终端节点
  const node = getDeviceNodeData(device, option)
  node.virNodeKey = []

  // 生成虚拟单位下终端节点
  for (let i = 0; i < device.virOrgsArr.length; i++) {
    const vParentKey = device.virOrgsArr[i]
    const vParentNode = getNodeByKey(treeId, vParentKey)
    // 跳过不存在的虚拟单位节点
    if (!vParentNode) {
      continue
    }

    // 生成虚拟单位下的节点
    const vNode = cloneDeep(node)
    const vKey = device.rid + i
    vNode.key = vKey
    node.virNodeKey.push(vKey)
    vNode.virtual = true
    vParentNode.addChildren(vNode)
    vParentNode.sortChildren(option.sortMethod || defaultSortMethod, true)
  }

  // 渲染终端父节点
  parentNode.addChildren(node)
  parentNode.sortChildren(option.sortMethod || defaultSortMethod, true)
  if (!parentNode.isExpanded()) {
    parentNode.setExpanded(true)
  }
  option.redraw && redrawViewport(treeId, option.force)

  return node
}

export function updateOneDeviceNode(treeId, device, option) {
  const node = getNodeByKey(treeId, device.rid)
  if (!node) {
    return
  }

  // 重置option参数
  option = mergeOption(option)

  // 清空旧的虚拟单位下节点
  if (node.data.virNodeKey) {
    for (let i = 0; i < node.data.virNodeKey.length; i++) {
      const vKey = node.data.virNodeKey[i]
      removeNode(treeId, vKey, { redraw: false })
    }
  }

  // 删除旧节点
  removeNode(treeId, device.rid, { redraw: false })

  // 添加新节点
  option.selected = node.isSelected()
  addOneDeviceNode(treeId, device, option)
}

export function delDeviceVirtualNodes(treeId, key, option) {
  const node = getNodeByKey(treeId, key)
  if (!node || !node.data.virNodeKey) {
    return
  }

  // 重置option参数
  option = mergeOption(option)

  for (let i = 0; i < node.data.virNodeKey.length; i++) {
    const key = node.data.virNodeKey[i]
    const vNode = getNodeByKey(treeId, key)
    if (!vNode) {
      continue
    }
    vNode.remove()
  }

  option.redraw && redrawViewport(treeId, option.force)
}

export function delOneDeviceNode(treeId, device, option) {
  // 重置option参数
  option = mergeOption(option)

  delDeviceVirtualNodes(treeId, device.rid, option)
  removeNode(treeId, device.rid, option)
}

export function sliceLoadingDeviceNodes(treeId, deviceList, options) {
  const _now = loadFlag = Date.now()
  window.setTimeout(() => {
    // 生成终端节点和对应虚拟单位下节点数据
    const childrenMap = {}
    for (let i = 0; i < deviceList.length; i++) {
      const device = deviceList[i]
      if (!childrenMap[device.orgId]) {
        childrenMap[device.orgId] = []
      }

      const node = getDeviceNodeData(device, options)
      childrenMap[device.orgId].push(node)

      // 处理终端虚拟单位下节点
      node.virNodeKey = []
      for (let j = 0; j < device.virOrgsArr.length; j++) {
        const virKey = device.virOrgsArr[j]
        if (!virKey) {
          continue
        }

        if (!childrenMap[virKey]) {
          childrenMap[virKey] = []
        }
        const vnodeKey = device.rid + j
        node.virNodeKey.push(vnodeKey)
        const vnode = cloneDeep(node)
        vnode.key = vnodeKey
        vnode.virtual = true
        childrenMap[virKey].push(vnode)
      }
    }

    // 遍历当前终端的上级节点数据，同时添加多个终端子节点数据
    for (const k in childrenMap) {
      const children = childrenMap[k]
      addNodeChildren(treeId, k, children)
    }

    // 在下次重绘前，计算单位下的终端数量并重新排序
    window.setTimeout(() => {
      if (_now === loadFlag) {
        refreshTreeOrgDeviceCount()
      }
    }, 50)
  }, 0)
}

export function gotoNodeLocation(treeId, key, option) {
  const node = getNodeByKey(treeId, key)
  // 跳过不存在的节点
  if (!node) {
    return
  }
  const opts = {
    isActive: false, ...option,
  }
  // 设置节点为活动状态，自动跳转到该节点
  node.setActive(opts.isActive)

  // ext-grid树结构，挂载DOM元素到node.tr下
  if (!node.tr) {
    return
  }

  node.tr.click()
}

// 动态组单位成员节点操作
const dynamicGroupOrgMemberStatusClasses = {
  1: i18n.global.t('dynamicGroup.normal'),
  2: i18n.global.t('dynamicGroup.preempted'),
  4: i18n.global.t('dynamicGroup.joinWithoutAnswer'),
  5: i18n.global.t('dynamicGroup.answeredExit'),
  6: i18n.global.t('dynamicGroup.exitWithoutAnswer'),
  10: i18n.global.t('dynamicGroup.expired'),
}

export function getDynamicGroupMemberStatus(detail) {
  if (!detail) {
    return ''
  }

  // 临时组：1：正常 2：被优先级高的抢占 10:已失效
  // 任务组：1:正常/已应答加入，2：被优先级高的抢占， 4:未应答加入 5:已应答退出 6:未应答退出
  return dynamicGroupOrgMemberStatusClasses[detail.memberState] || dynamicGroupOrgMemberStatusClasses[4]
}

const dynamicGroupUnCheckboxTree = []

function cancelDynamicGroupMemberSelectedFunc(node) {
  node.selected = false
  node.checkbox = false

  node.unselectable = true
  node.unselectableStatus = true
}

export function getDynamicGroupOrgNodeTitle(detail, orgData) {
  return `<span class='org-title'>${orgData.orgShortName}</span>
          <span class='dynamic-group-detail-status'>${getDynamicGroupMemberStatus(detail)}</span>`
}

export function addDynamicGroupOrgNode(treeId, detail, options) {
  const source = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)
  if (!source) {
    return
  }

  const opts = mergeOption(options)
  const nodeData = createOrgNodeData(source, opts)

  // 重置节点的key和title，节点源数据可能在多个组里面
  nodeData.key = detail.rid
  nodeData.title = getDynamicGroupOrgNodeTitle(detail, source)
  syncOrgNodeExtraData(nodeData, source)
  // 标记为动态组成员
  nodeData.isDynamicGroupMember = true
  // 禁用checkbox
  dynamicGroupUnCheckboxTree.includes(treeId) && cancelDynamicGroupMemberSelectedFunc(nodeData)

  const parentNode = getNodeByKey(treeId, detail.orgId)
  if (!parentNode) {
    return
  }

  parentNode.addChildren(nodeData)
  opts.redraw && redrawViewport(treeId, opts.force)
}

// 动态组单位成员节点更新
export function updateDynamicGroupOrgNode(treeId, detail, options) {
  const source = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)
  if (!source) {
    return
  }

  const opts = mergeOption(options)
  const node = getNodeByKey(treeId, detail.rid)
  if (!node) {
    return
  }

  node.title = getDynamicGroupOrgNodeTitle(detail, source)
  node.tooltip = getOrgNodeTooltip(source)
  syncOrgNodeExtraData(node, source)
  // 禁用checkbox
  dynamicGroupUnCheckboxTree.includes(treeId) && cancelDynamicGroupMemberSelectedFunc(node)

  node.render()
  opts.redraw && redrawViewport(treeId, opts.force)
}

// 动态组单位成员节点删除
export function deleteDynamicGroupOrgNode(treeId, detail, options) {
  removeNode(treeId, detail.rid, options)
}

// 动态组终端成员节点操作
export function getDynamicGroupDeviceNodeTitle(detail, device) {
  return `<i class='device_status device_status_none'></i>
          <span class='device_selfId'>${device.selfId}</span>
          <span class='dynamic-group-detail-status'>${getDynamicGroupMemberStatus(detail)}</span>`
}

export function getListGroupDetail(deviceDetail) {
  const isListenGroupDevice = deviceDetail.isDeviceGroup === 3
  if (!isListenGroupDevice) {
    return
  }

  const details = bfglob.gdynamicGroupDetail.getDataByGroupRid(deviceDetail.orgId)
  return details.find((detail) => detail.groupRid === deviceDetail.groupRid)
}

export function addDynamicGroupDeviceNode(treeId, detail, options) {
  const source = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)
  if (!source) {
    return
  }

  const opts = mergeOption(options)
  const nodeData = getDeviceNodeData(source, opts)
  // 重置节点的key和title，节点源数据可能在多个组里面
  nodeData.key = detail.rid
  nodeData.title = getDynamicGroupDeviceNodeTitle(detail, source)
  syncDeviceNodeExtraData(nodeData, source)
  // 标记为动态组成员
  nodeData.isDynamicGroupMember = true
  // 禁用checkbox
  dynamicGroupUnCheckboxTree.includes(treeId) && cancelDynamicGroupMemberSelectedFunc(nodeData)

  // 如果是因接收组加入的动态组成员，需要处理上级节点的key
  const isListenGroupDevice = detail.isDeviceGroup === 3
  const listGroupDetail = getListGroupDetail(detail)
  const parentKey = isListenGroupDevice ? listGroupDetail?.rid ?? detail.orgId : detail.orgId
  const parentNode = getNodeByKey(treeId, parentKey)
  if (!parentNode) {
    return
  }
  parentNode.addChildren(nodeData)
  sortChildren(treeId, undefined, true, opts)
}

export function updateDynamicGroupDeviceNode(treeId, detail, options) {
  const opts = mergeOption(options)
  const source = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)
  if (!source) {
    return
  }

  const node = getNodeByKey(treeId, detail.rid)
  if (!node) {
    return
  }

  node.title = getDynamicGroupDeviceNodeTitle(detail, source)
  node.tooltip = getDeviceNodeTooltip(source)
  syncDeviceNodeExtraData(node, source)
  // 禁用checkbox
  dynamicGroupUnCheckboxTree.includes(treeId) && cancelDynamicGroupMemberSelectedFunc(node)

  node.render()
  sortChildren(treeId, undefined, true, opts)
}

export function deleteDynamicGroupDeviceNode(treeId, detail, options = {}) {
  removeNode(treeId, detail.rid, options)
}

export function addDynamicGroupNode(treeId, source, options = {}) {
  const parentNode = getNodeByKey(treeId, source.parentOrgId) || getRootNode(treeId)
  const nodeData = createOrgNodeData(source, options)
  syncOrgNodeExtraData(nodeData, source)
  // 禁用checkbox
  dynamicGroupUnCheckboxTree.includes(treeId) && cancelDynamicGroupMemberSelectedFunc(nodeData)

  parentNode && parentNode.addChildren(nodeData)
  sortChildren(treeId)
}

export default {
  gotoNodeLocation,
  defaultTreeId,
  onlineFilterReg,

  getTree,
  getRootNode,
  getNodeByKey,
  sortChildren,
  removeNode,
  removeNodeChildren,
  moveNode,
  addFullCallNode,
  expandAll,
  selectAll,
  setSelected,
  adjustViewportSize,
  checkTreeIsNeedRedrawViewport,
  redrawViewport,
  clearTree,
  clearFilter,
  filterNodes,
  clearNodeUnselectable,
  resetNodeUnselectable,
  nodeUnselectable,
  addNodeChildren,
  treeExpandAll(treeId, bool) {
    const tree = getTree(treeId)
    if (!tree) {
      return
    }
    tree.visit(function (node) {
      if (!node.hasChildren()) {
        return 'skip'
      }
      node.setExpanded(node.data.virtual ? false : bool)
    })
  },

  getOrgNodeTitle,
  getOrgNodeTooltip,
  createOrgNodeData,
  createTreeOrgSource,
  addOrgNodeToTree,
  addOneOrgNode,
  updateOneOrgNode,
  delOneOrgNode,
  updateOneOrgNodeTitle,
  syncAddOrgNode(treeId, source, option) {
    addOneOrgNode(treeId, source, option)
    // 统计单位下辖终端数量
    refreshTreeOrgDeviceCount(treeId, option)

    setTimeout(() => {
      bfglob.emit('addOneOrgNode', source)
    }, 0)
  },
  syncUpdateOneOrgNode(treeId, source, option) {
    updateOneOrgNode(treeId, source, option)
    // 统计单位下辖终端数量
    refreshTreeOrgDeviceCount(treeId, option)

    setTimeout(() => {
      bfglob.emit('updateOneOrgNode', source)
    }, 0)
  },
  syncDelOneOrgNode(treeId, source, option) {
    delOneOrgNode(treeId, source, option)
    // 统计单位下辖终端数量
    refreshTreeOrgDeviceCount(treeId, option)

    setTimeout(() => {
      bfglob.emit('delOneOrgNode', source)
    }, 0)
  },

  clearTreeOrgDeviceCount,
  calculateTreeOrgSubOrgDeviceCount,
  updateTreeOrgDeviceCount,
  refreshTreeOrgDeviceCount,
  renderOrgCounter,

  updateDeviceNodeTitle,
  get_device_status_className,
  getDeviceChannel,
  updateOneDeviceNode,
  delOneDeviceNode,
  addOneDeviceNode,
  getDeviceNodeData,
  getDeviceNodeTitle,
  getDeviceNodeTooltip,

  sliceLoadingDeviceNodes,
  getTreeNodeByRid(treeId, key) {
    return getNodeByKey(treeId, key)
  },
  add_one_device_to_tree(treeId, device, option) {
    addOneDeviceNode(treeId, device, option)
    // 统计单位下辖终端数量
    refreshTreeOrgDeviceCount(treeId, option)

    setTimeout(() => {
      bfglob.emit('addOneDeviceNode', device)
    }, 0)
  },
  delete_device_node_of_tree(treeId, device, option) {
    delOneDeviceNode(treeId, device, option)
    // 统计单位下辖终端数量
    refreshTreeOrgDeviceCount(treeId, option)

    setTimeout(() => {
      bfglob.emit('delOneDeviceNode', device)
    }, 0)
  }, // 更新 fancytree 对讲机节点
  update_device_node_of_tree(treeId, device, option) {
    updateOneDeviceNode(treeId, device, option)
    // 统计单位下辖终端数量
    refreshTreeOrgDeviceCount(treeId, option)

    setTimeout(() => {
      bfglob.emit('updateOneDeviceNode', device)
    }, 0)
  },
  showOnlineDevices(treeId, reg = onlineFilterReg) {
    filterNodes(treeId, (node) => {
      return (reg.test(node.title) && !node.data.virtual)
    })
  },
  showAllDevices(treeId) {
    clearFilter(treeId)
  },
}
