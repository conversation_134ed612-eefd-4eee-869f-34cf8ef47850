/**
 * 李腾蛟(泉州)提供的序列号算法，当前序列号最新算法，向下兼容旧的算法
 * v3版本算法c#版本：doc/SnNumber.cs
 * 2023-04-18
 * <AUTHOR> <EMAIL>
 */

const Characters = (() => {
  // A-Z, 0-9字符中需要排除的列表
  const excludes = [
    '1', '3', '5', 'A', 'G', 'H', 'K', 'P', 'Q', 'R', 'U', 'V', //旧的商品码的头两个数字
    'I', 'O', 'Z', //阅读易混淆的
    'E', 'U', 'B', 'D', 'J', 'Z', 'M', 'N', //发音易混淆的
    '4', //其他
  ]

  let tmp = ''
  // 0-9
  for (let i = 0x30; i <= 0x39; i++) {
    const c = String.fromCharCode(i)
    if (!excludes.includes(c)) {
      tmp += c
    }
  }
  // A-Z
  for (let j = 0x41; j <= 0x5A; j++) {
    const d = String.fromCharCode(j)
    if (!excludes.includes(d)) {
      tmp += d
    }
  }

  return tmp
})()

export class SnNumber {
  // SN的标准长度
  static get SNLenght() { return 32 }

  // 显示SN8位字符串
  static get RealSNLenght() { return 8 }

  // C#版本为静态构造函数生成，在Javascript中转换为getter属性
  static get Characters() {
    return Characters
  }

  static get BaseLength() {
    return SnNumber.Characters.length
  }

  static MaxValue(length) {
    const pow = Math.pow(SnNumber.BaseLength, length)
    if (pow > Number.MAX_VALUE) {
      throw new Error('ArgumentOutOfRangeException')
    }
    return pow
  }

  /**
   * 十进制数字转换为指定的进制形式字符串
   * @param {number} num 需要转换的数字
   * @param {number} len 字符串长度
   * @returns {string} 返回指定长度的字符串
   */
  static ToString(num, len = 0) {
    const Characters = SnNumber.Characters
    let value
    if (num === 0) {
      value = Characters[0] + ''
    } else {
      const BaseLength = SnNumber.BaseLength
      const result = []
      let t = num

      while (t > 0) {
        const mod = t % BaseLength
        t = Math.abs(t / BaseLength)
        const character = Characters[mod] + ''
        result.unshift(character)
      }

      value = result.join('')
    }

    if (len > 0) {
      return value.padStart(len, Characters[0])
    } else {
      return value
    }
  }

  /**
   * 指定字符串转换为十进制的数字形式
   * @param {string} str 需要转换的字符串
   * @returns {number}
   */
  static FromString(str) {
    let result = 0
    let j = 0
    const list = str.split('').reverse()
    const Characters = SnNumber.Characters
    const BaseLength = SnNumber.BaseLength

    for (const ch of list) {
      if (Characters.includes(ch)) {
        result += Characters.indexOf(ch) * Math.pow(BaseLength, j)
        j++
      } else {
        throw new Error(`ArgumentOutOfRangeException('${ch}' Not a valid character!)`)
      }
    }
    return result
  }

  static IsSnNumber(ch) {
    return SnNumber.Characters.includes(ch + '')
  }

  /**
   * 将8个字符的序列号转为16个10进制的数字。
   * @param {string} sn 字符串序列号
   * @returns {string}
   */
  static CovertToDecString(sn) {
    let hex = ''
    const snArr = sn.split('')
    for (const ch of snArr) {
      hex += ch.toString().padStart(2, '0')
    }
    return hex
  }

  /**
   * 判断参数是否为空，或由空格组成的字符串，模拟C#的IsNullOrWhiteSpace方法
   * https://learn.microsoft.com/en-us/dotnet/api/system.string.isnullorwhitespace?view=net-8.0
   * @param {null|undefined|string} str
   * @returns {boolean}
   * @example [null, undefined, '', ' ', ' \t ', '\u2000'], the result is true
   */
  static IsNullOrWhiteSpace(str) {
    if (!str) return true
    return (str.trim() === '' || decodeURI(str).trim() === '')
  }

  /**
   * 将32个10进制字符的序列号转为8个SnNumber字符。
   * @param {string} sn
   * @return {string | null}
   */
  static CovertToSnNumberString(sn) {
    if (SnNumber.IsNullOrWhiteSpace(sn) || sn.length !== SnNumber.SNLenght) {
      throw new Error(`ArgumentException(sn length must be ${SnNumber.SNLenght})`)
    }

    try {
      const temp = sn.substring(11, 11 + 4) + sn.substring(18, 18 + 6) + sn.substring(26, 26 + 6)
      let real = ''
      for (let i = 0; i < temp.length; i += 2) {
        const ch = String.fromCharCode(parseInt(temp.substring(i, i + 2), 10))
        if (SnNumber.IsSnNumber(ch)) {
          real += ch
        } else {
          return null
        }
      }

      if (real.length === SnNumber.RealSNLenght) {
        return real
      } else {
        return null
      }
    } catch (err) {
      return null
    }
  }

  /**
   * 尝试将32个10进制字符的序列号转为8个SnNumber字符,如果失败则返回原值。
   * @param {string} sn
   * @returns {string}
   */
  static TryCovertToSnNumberString(sn) {
    if (SnNumber.IsNullOrWhiteSpace(sn) || sn.length !== SnNumber.SNLenght) {
      return sn
    }

    const real = SnNumber.CovertToSnNumberString(sn)
    if (!SnNumber.IsNullOrWhiteSpace(real)) {
      return real
    } else {
      return sn
    }
  }

  /**
   * 向下兼容v2, v1版本sn解码
   * 判断是v2或v1版本，先解码成字符串，再尝试转换最新版本sn
   * @param {Uint8Array|number[]} snList
   * @retures {string}
   */
  static TryDecodeSnNumber(snList) {
    const isV2 = isV2Sn(snList)
    const sn = isV2 ? snDecodeV2(snList) : snDecodeV1(snList)
    return SnNumber.TryCovertToSnNumberString(sn)
  }
}

function fillNumberString(length, code = 0xFF) {
  return new Array(length).fill(code).toString()
}

/**
 * 判断是否为v2版本的sn，后8位填充0xFF或0x00
 * @param {Uint8Array|number[]} snList
 * @returns {boolean}
 */
function isV2Sn(snList) {
  if (!snList || snList.length === 0) {
    return false
  }

  if (snList instanceof Uint8Array) {
    snList = Array.from(snList)
  }

  const codeList = Array.from(snList).splice(-8)
  return (
    codeList.toString() === fillNumberString(8, 0xFF) ||
    codeList.toString() === fillNumberString(8, 0x00)
  )
}

/**
 * v1版本序列号算法
 * 序列号规则是16位ASCII码，0-9、A-Z
 * @param {Uint8Array|number[]} snList
 * @returns {string}
 */
export function snDecodeV1(snList) {
  if (!snList || snList.length === 0) {
    return ''
  }

  if (snList instanceof Uint8Array) {
    snList = Array.from(snList)
  }

  if (
    snList.toString() === fillNumberString(snList.length, 0xFF) ||
    snList.toString() === fillNumberString(snList.length, 0x00)
  ) {
    return ''
  }

  return String.fromCharCode.apply(null, snList)
}

/**
 * v2版本序列号算法
 * 序列号规则是8位ASCII码，0-9、A-Z，为原16字节的前8位，后8位填充0xFF或0x00
 * @param {Uint8Array|number[]} snList
 * @returns {string}
 */
export function snDecodeV2(snList) {
  const sn = snDecodeV1(snList).slice(0, 8)
  if (
    sn === fillNumberString(8, 0xFF) ||
    sn === fillNumberString(8, 0x00)
  ) {
    return ''
  }

  return sn
}
