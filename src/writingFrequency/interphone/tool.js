import bfNotify from '@/utils/notify'
import {
  v1 as uuid
} from 'uuid'
import {
  encryptedDataTo16Byte
} from './aesEncrypt'
import {
  AnswerCode,
  CmdIO,
  DataTypes,
  getBase64AuthCode
} from './cmdIO'
import {
  base642buf,
  base642hex,
  buf2base64,
  checkDeviceModel
} from './common'
import {
  decodeSubsonic,
  encodeSubsonic,
  encodeSubsonic2String
} from './subtoneCode'
import { TD920Model } from '@/writingFrequency/interphone/models'

export default {
  data() {
    return {
      readCount: 0,
      rxGroupList: [],
      // 读取机型配置时，记录各结构配置的结构ID和协议版本号
      // struct id => {version, structLen}
      structInfo: {},
    }
  },
  methods: {
    // 自定义输入亚音，数字亚音范围为：D002[NI]-D777[NI]
    // 模拟亚音范围为：60.0-260.0
    decodeSubsonic,
    encodeSubsonic,
    encodeSubsonic2String,

    finishWrite() {
      this.setWriteEndStatus()

      // 写完数据后，显示提示消息
      bfNotify.messageBox(this.$t('msgbox.writeInSuccess'), 'success')
      bfglob.console.log('write in success')

      // 写频完成，更新对应手台的写频时间
      this.updateDeviceLastRfWriteTime()
    },

    // 设置读取状态
    resetReadWriteState(mode = DataTypes.read, isStart = true) {
      if (mode === DataTypes.read) {
        this.setReadState(isStart)
      } else if (mode === DataTypes.write) {
        this.setWriteState(isStart)
      } else if (mode === DataTypes.V) {
        this.setReadState(isStart)
      }
    },
    setTimeoutFunc(mode = DataTypes.read) {
      const flag = this.readWriteFlag = uuid()
      setTimeout(() => {
        if (this.readWriteFlag !== flag) {
          return
        }
        if (mode === DataTypes.read) {
          // 读取数据超时
          bfNotify.messageBox(this.$t('msgbox.readDataFailed'), 'warning')
          this.setReadState(false)
        } else {
          // 写入响应超时
          bfNotify.messageBox(this.$t('msgbox.writeInFail'), 'warning')
          this.setWriteState(false)
        }
      }, 10000)
    },
    setWriteEndStatus() {
      this.setWriteState(false)
      this.answerEOT()
    },

    // 循环读取数据操作
    read(need_decode = true, isDecryptBytes = false) {
      return new Promise((resolve, reject) => {
        if (!this.canRead()) {
          reject('not found sl_read_device_usb_data api')
          return
        }

        this.QWebServer.sl_read_device_usb_data((read_data) => {
          this.readCount++
          let hexStr = ''
          // base64长度小于等于4,则是NCK等操作码
          if (read_data) {
            if (!need_decode) {
              hexStr = base642buf(read_data)
            } else if (read_data.length <= 4) {
              hexStr = base642hex(read_data)
            } else if (isDecryptBytes) {
              hexStr = CmdIO.readDecryptBytes(read_data, this.Model, this.ioPassword)
            } else if (need_decode) {
              hexStr = CmdIO.decode(read_data, this.Model, this.ioPassword)
            } else {
              hexStr = read_data
            }
          }

          // 第一次可能读取不成功，需要多读取几次
          if ((!hexStr || hexStr.length === 0) && this.readCount <= 10) {
            setTimeout(() => {
              this.read(need_decode, isDecryptBytes).then((secRes) => {
                this.readCount = 0
                resolve(secRes)
              })
            }, 100)
            return
          }
          this.readCount = 0
          resolve(hexStr)
        })
      })
    },
    async processConfig(res) {
      const type = res.data[0]
      const version = res.data[1]
      const deviceProtoCls = this.getClassInstance({
        type,
        version,
      })

      if (deviceProtoCls) {
        // 缓存当前结构的版本与结构长度信息，以便写入时，能正常找到对应的协议
        // 主要兼容结构协议变更问题
        const structLengthInfo = deviceProtoCls.structLengthInfo ?? {}
        this.structInfo[type] = {
          version: version,
          structLen: structLengthInfo[version] ?? deviceProtoCls.structLen,
        }

        const data = deviceProtoCls.decode(res.data)
        bfglob.console.log('processConfig decode:', data)
        this.asyncLocalConfig(data)
      } else {
        bfglob.console.error('processConfig error:', res)
      }
    },
    async whileReadAllConfig() {
      // 判断是否拔出USB或者不在读取数据状态中，则停止循环读取数据
      if (this.noDevice || !this.isReading) {
        this.isReading = false
        this.showReadDataFailedMessage()
        return
      }

      this.setTimeoutFunc(DataTypes.read)

      this.read().then((res) => {
        if (!res || res.length === 0) {
          bfglob.console.warn('readDeviceAllConfig warn: can not read any data')
          this.setReadState(false)
          // 数据读取异常消息提示
          bfNotify.messageBox(this.$t('msgbox.deviceNotReturnData'), 'warning')
          return
        }
        if (this.checkResDataIsEOT(res)) {
          bfglob.console.log('whileReadAllConfig end', res)
          // 指令结束帧，接收完所有数据，解码所有类型数据
          this.setReadState(false)
          // 数据读取成功消息提示
          bfNotify.messageBox(this.$t('msgbox.readSuccess'), 'success')
          // 同步编程密码，以便写入密码数据
          this.passwordInfo.md5Key = this.ioPassword
          return
        }
        if (this.checkResDataIsNCK(res)) {
          // 返回NCK指令，结束
          this.setReadState(false)
          // 数据读取异常消息提示
          bfNotify.messageBox(this.$t('msgbox.deviceTerminateReadOpt'), 'warning')
          return
        }

        // 应答收到数据
        this.answerACK()
        this.processConfig(res)
        this.whileReadAllConfig()
      }).catch(e => {
        this.setReadState(false)
      })
    },
    readDeviceAllConfig() {
      this.write(this.getCmdIoBase64String([], DataTypes.read))
      this.whileReadAllConfig()
    },
    readDeviceInfoAndIdentityAndResourceVersion(bytes, type) {
      return new Promise((resolve, reject) => {
        const base64Str = this.getCmdIoBase64String(bytes, type)
        this.write(base64Str)

        this.read().then((res) => {
          // 可能返回NCK
          if (this.checkResDataIsNCK(res)) {
            reject(res)
            return
          }

          resolve(res)
        })
      })
    },
    readInfo(mode, type, msg) {
      this.setTimeoutFunc(mode)

      const Info = this.getClassInstance({
        type: type,
        model: this.Model,
      })
      const bytes = Info.encode()

      return this.readDeviceInfoAndIdentityAndResourceVersion(bytes, DataTypes.read)
        .then(res => {
          return Info.decode(res.data)
        }).catch(e => {
          this.showWriteInFailInfo(msg, e)
          return {}
        })
    },
    readIdentityInfo(mode = DataTypes.read) {
      return this.readInfo(mode, 2, this.$t('msgbox.readIdentityInfoFailed'))
    },
    readDeviceInfo(mode = DataTypes.read) {
      return this.readInfo(mode, 1, this.$t('msgbox.readDeviceInfoFailed'))
    },
    readResourceVersionInfo(mode = DataTypes.read) {
      return this.readInfo(mode, this.tableIndex.ResourceVersion, this.$t('msgbox.readResourceVersionInfoFailed'))
    },
    async readRecordInfo(Info, msg) {
      const res = await this.read()
      // 可能返回NCK
      if (this.checkResDataIsNCK(res)) {
        bfglob.console.warn('NCK:', res)
        // 返回NCK指令，结束
        this.setReadState(false)
        // 数据读取异常消息提示
        bfNotify.messageBox(this.$t('msgbox.readRecordListFailed'), 'warning')
        return
      }
      // 结束返回EOT
      if (this.checkResDataIsEOT(res)) {
        bfglob.console.log('whileReadAllConfig end', res)
        this.setReadState(false)
        // 数据读取成功消息提示
        bfNotify.messageBox(this.$t('msgbox.readSuccess'), 'success')
        return
      }
      this.answerACK()
      const data = Info.decode(res.data)
      bfglob.console.log('readRecordInfo：', data)
      this.asyncLocalConfig(data)
      await this.readRecordInfo(Info, msg)
    },
    writeReadMode() {
      const buf = encryptedDataTo16Byte([DataTypes.read])
      const base64Str = CmdIO.aesEncrypt(buf, this.Model, this.ioPassword)

      this.setTimeoutFunc(DataTypes.read)

      this.write(base64Str)
      this.read().then(async (data) => {
        if (this.checkResDataIsACK(data)) {
          const deviceInfo = await this.readDeviceInfo()
          bfglob.console.log('device info:', deviceInfo, this.Model)
          if (!this.checkDeviceModel(deviceInfo.result[0])) {
            this.setReadState(false)
            this.answerEOT()
            return
          }
          this.asyncLocalConfig(deviceInfo)
          const IdentityInfoStruct = await this.readIdentityInfo()
          bfglob.console.log('device identity:', IdentityInfoStruct)
          // 如果身份信息无效，则需要发送NCK
          const info = IdentityInfoStruct.result?.[0]
          if (!info) {
            this.answerNCK()
            this.setReadState(false)
            this.showWriteInFailInfo(this.$t('msgbox.invalidIdentityInfo'))
            return
          }

          this.asyncLocalConfig(IdentityInfoStruct)
          // TD920机型读取资源版本信息
          if (this.Model === TD920Model) {
            const ResourceVersionInfoStruct = await this.readResourceVersionInfo()
            bfglob.console.log('device resource version:', ResourceVersionInfoStruct)
            this.asyncLocalConfig(ResourceVersionInfoStruct)
          }
          this.readDeviceAllConfig()
        } else {
          this.setReadState(false)
          this.showWriteInFailInfo(this.$t('msgbox.programmingPwdError'), data)
        }
      }).catch(() => {
        this.answerEOT()
      })
    },
    writeDownloadRecordMode() {
      const buf = encryptedDataTo16Byte([DataTypes.V])
      const base64Str = CmdIO.aesEncrypt(buf, this.Model, this.ioPassword)

      this.setTimeoutFunc(DataTypes.read)

      this.write(base64Str)
      this.read().then(async (data) => {
        if (this.checkResDataIsACK(data)) {
          const deviceInfo = await this.readDeviceInfo()
          bfglob.console.log('device info:', deviceInfo, this.Model)
          if (!this.checkDeviceModel(deviceInfo.result[0])) {
            this.setReadState(false)
            return
          }
          // this.asyncLocalConfig(deviceInfo)
          // 进入录音文件传输模式
          this.downloadRecord()
        }
      })
    },
    async writeGetRecordListMode() {
      const buf = encryptedDataTo16Byte([DataTypes.read])
      const base64Str = CmdIO.aesEncrypt(buf, this.Model, this.ioPassword)

      // this.setTimeoutFunc(DataTypes.read)
      this.write(base64Str)
      this.read().then(async (data) => {
        if (this.checkResDataIsACK(data)) {
          const Info = this.getClassInstance({
            type: 31,
            model: this.Model,
          })
          const bytes = Info.encode()
          const base64Str = this.getCmdIoBase64String(bytes, DataTypes.read)
          this.write(base64Str)
          await this.readRecordInfo(Info, this.$t('msgbox.readDeviceInfoFailed'))
        }
      })
    },
    reopenUSBDevice() {
      return new Promise((resolve, reject) => {
        // 旧版本的TC918写频程序没有sl_reopenBfdxUSBDevice方法，不需要重新打开USB设备连接
        if (!this.QWebServer.sl_reopenBfdxUSBDevice) {
          return resolve(true)
        }
        this.QWebServer.sl_reopenBfdxUSBDevice((data) => {
          if (data) {
            resolve(data)
          } else {
            bfglob.console.warn('reopenUSBDevice failed:', data)
            reject(data)
          }
        })
      })
    },
    async sendAuthentication(mode = DataTypes.read, getRecordList = false) {
      // 每次读、写操作前都先重新打开设备连接，如果失败则停止后续操作
      const isOpen = await this.reopenUSBDevice()
      if (!isOpen) {
        bfNotify.messageBox(this.$t('msgbox.disconnectDevice'), 'error')
        this.resetReadWriteState(mode, false)
        return
      }

      // 设置读取状态，以便自动计算读写按钮互斥
      this.resetReadWriteState(mode, true)
      this.setTimeoutFunc(mode)

      const base64Str = getBase64AuthCode(this.Model)
      this.write(base64Str)
      this.write('')

      this.read(false)
        .then((data) => {
          // 鉴权成功会返回128个字节的uint8Array,即256个字节的hex字符串
          if (data.length === 128) {
            this.resetReadWriteFlag()
            if (getRecordList) {
              // 单独读取录音文件列表
              this.writeGetRecordListMode()
            } else if (mode === DataTypes.read) {
              this.writeReadMode()
            } else if (mode === DataTypes.write) {
              this.writeWriteMode()
            } else if (mode === DataTypes.V) {
              this.writeDownloadRecordMode()
            }
          } else {
            // 无法通过鉴权，提示机型错误
            bfNotify.messageBox(this.$t('msgbox.disconnectDevice'), 'error')
            this.resetReadWriteState(mode, false)
          }
        })
        .catch(e => {
          this.resetReadWriteState(mode, false)
        })
    },

    // 写入数据操作
    write(base64Str) {
      if (!this.canWrite()) {
        return
      }

      this.QWebServer.sl_write_device_usb_data(base64Str)
    },
    getCmdIoBase64String(bytes, type = DataTypes.write) {
      const cmdIO = new CmdIO()
      cmdIO.type = type
      cmdIO.data = bytes
      return cmdIO.encode(this.Model, this.ioPassword)
    },
    writeOneData(bytes = [], writeSpace = 0) {
      return new Promise((resolve, reject) => {
        const base64String = this.getCmdIoBase64String(bytes)
        this.write(base64String)
        if (writeSpace) {
          this.write('')
        }

        this.read().then((res) => {
          bfglob.console.log('writeOneData res:', res, '---end---')
          if (this.checkResDataIsACK(res)) {
            resolve(res)
            return
          }

          this.showWriteInFailInfo(this.$t('msgbox.writeInFail'), res)
          this.setWriteState(false)
          reject(res)
        }).catch(e => {
          this.showWriteInFailInfo(this.$t('msgbox.writeInFail'), e)
          this.setWriteState(false)
          reject(e)
        })
      })
    },
    writeIteratorArrayData(dataList, writeSpace = true) {
      return new Promise((resolve, reject) => {
        const iterator = dataList[Symbol.iterator]()
        const writeInData = (item) => {
          if (!item) {
            this.setWriteState(false)
            reject()
            return
          }
          if (item.done) {
            resolve()
            return
          }

          this.setTimeoutFunc(DataTypes.write)
          this.writeOneData(item.value, writeSpace)
            .then(res => {
              writeInData(iterator.next())
            })
            .catch(reject)
        }
        writeInData(iterator.next())
      })
    },
    encodeIteratorData(ClassInstance, dataList, limit) {
      const result = []
      if (dataList.length) {
        while (dataList.length) {
          const data = dataList.splice(0, limit)
          result.push(ClassInstance.encode(data))
        }
      } else {
        result.push(ClassInstance.encode([]))
      }
      return result
    },
    getBytesByType(type, option) {
      if (typeof type !== 'number') {
        return []
      }
      const opt = {
        iteration: true,
        limit: this.wfCountLimit || 3,
        ...option,
      }
      const ClassInstance = this.getClassInstance({
        type,
        version: option?.version,
        structLen: option?.structLen
      })
      const data = this.getBeforeEncodeData(ClassInstance.type)

      if (opt.iteration) {
        let list = data
        if (!Array.isArray(data)) {
          list = [data]
        }
        return this.encodeIteratorData(ClassInstance, list, opt.limit)
      }

      try {
        return ClassInstance.encode(data)
      } catch (e) {
        return ClassInstance.encode([])
      }
    },
    writeInData() {
      return new Promise((resolve, reject) => {
        if (!Array.isArray(this.writeDataOption)) {
          reject()
          return
        }

        const iterator = this.writeDataOption[Symbol.iterator]()
        const write = (item) => {
          if (item.done) {
            resolve(item.done)
            return
          }
          const config = item.value
          if (!config) {
            reject(false)
            return
          }

          // 找到读取数据时缓存的结构信息配置，默认以缓存的结构配置进行数据编码
          // 不使用Object.assign，主要是0无法覆盖已经存在的属性，导致参数合并异常
          const structInfo = this.structInfo[config.type] ?? {}
          const structKeys = Object.keys(structInfo)
          config.option = config.option ?? {}
          structKeys.forEach(key => {
            config.option[key] = structInfo[key]
          })

          const bytes = this.getBytesByType(config.type, config.option)
          this.writeIteratorArrayData(bytes)
            .then((res) => {
              write(iterator.next())
            })
            .catch(e => {
              reject(false)
              const message = config.failedMsg || this.$t('msgbox.writeInFail')
              this.showWriteInFailInfo(message, e)
            })
        }
        write(iterator.next())
      })
    },
    checkWriteDeviceModel(deviceInfo) {
      if (!deviceInfo || !Array.isArray(deviceInfo.result)) {
        return false
      }

      const info = deviceInfo.result[0]
      if (!info || !checkDeviceModel(info.model, this.deviceWriteInfo.model)) {
        this.showWriteInFailInfo(this.$t('writeFreq.modelDataError'))
        this.setWriteEndStatus()
        return false
      }
      return true
    },
    checkDeviceFreqValid(deviceInfo) {
      if (!deviceInfo || !Array.isArray(deviceInfo.result)) {
        return false
      }
      const info = deviceInfo.result[0]
      if (!info) {
        return false
      }
      if (info.minFrequency !== this.deviceWriteInfo.minFrequency || info.maxFrequency !==
        this.deviceWriteInfo.maxFrequency) {
        this.showWriteInFailInfo(this.$t('writeFreq.frequencyRangeError'))
        this.setWriteEndStatus()
        return false
      }
      return true
    },
    writeWriteMode() {
      const writeType = DataTypes.write
      const buf = encryptedDataTo16Byte([writeType])
      const base64Str = CmdIO.aesEncrypt(buf, this.Model, this.ioPassword)

      this.setTimeoutFunc(DataTypes.write)

      this.write(base64Str)
      this.read().then(async (data) => {
        if (this.checkResDataIsACK(data)) {
          const deviceInfo = await this.readDeviceInfo(DataTypes.write)
          if (!this.checkWriteDeviceModel(deviceInfo)) {
            this.setWriteState(false)
            this.answerEOT()
            return
          }
          if (!this.checkDeviceFreqValid(deviceInfo)) {
            this.setWriteState(false)
            this.answerEOT()
            return
          }

          await this.writeInData()
          this.finishWrite()
        } else {
          this.setWriteState(false)
          this.showWriteInFailInfo(this.$t('msgbox.programmingPwdError'), data)
        }
      }).catch(() => {
        this.answerEOT()
      })
    },
    writeDataConfig() {
      if (!this.canWrite()) {
        return
      }
      // 已经处理写数据状态，则停止多次触发
      if (this.isWriting) {
        return
      }

      // 表单验证通过后才能写入数据
      this.validateAllRules()
        .then(() => {
          // 开始写入取数据提示
          bfNotify.messageBox(this.$t('msgbox.startWriting'))
          this.sendAuthentication(DataTypes.write)
        })
        .catch((err) => {
          bfNotify.messageBox(err, 'warning')
        })
    },

    // 应答手台方法
    answerACK() {
      this.write(buf2base64([AnswerCode.ACK]))
    },
    answerNCK() {
      this.write(buf2base64([AnswerCode.NCK]))
    },
    answerEOT() {
      this.write(buf2base64([AnswerCode.EOT]))
    },
    answerCAN() {
      this.write(buf2base64([AnswerCode.CAN]))
    },

    // 检测响应码方法
    checkResDataIsNCK(res) {
      // 可能返回NCK
      if (typeof res === 'string' && parseInt(res, 16) === AnswerCode.NCK) {
        bfglob.console.warn('checkResDataIsNCK:', res)
        return true
      }

      return false
    },
    checkResDataIsACK(res) {
      if (typeof res === 'string' && parseInt(res, 16) === AnswerCode.ACK) {
        return true
      }

      return false
    },
    checkResDataIsEOT(res) {
      if (typeof res === 'string' && parseInt(res, 16) === AnswerCode.EOT) {
        return true
      }

      return false
    },
    checkResDataIsCAN(res) {
      if (typeof res === 'string' && parseInt(res, 16) === AnswerCode.CAN) {
        return true
      }

      return false
    },
    checkResDataIsRECORD_EOT(res) {
      if (typeof res === 'string' && parseInt(res, 16) === AnswerCode.RECORD_EOT) {
        return true
      }

      return false
    },
  },
}
