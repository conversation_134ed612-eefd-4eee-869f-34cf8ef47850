import { cloneDeep } from 'lodash'
import {
  Unset,
  bool,
  subTable,
  IdentityInfoProto,
  PasswordProto,
  PhoneBookProto,
  PatrolConfigProto,
  EmergencyAlarmProto,
  DeviceBase,
  DeviceInfo,
  SecondIdentityInfo,
  Password,
  loadProtocol,
  generateEnumObject,
  unmarshalProto,
  marshalProto,
} from './common'
import { TD910SDCModel } from './models'

export const Model = TD910SDCModel
export const BaseOptions = {
  model: Model,
  version: 0,
}

// 设备信息
const DeviceInfoProto = {
  0: {
    // ch: '型号',
    model: {
      type: 'string',
      len: 8,
    },
    frequencyRange: {
      type: 'byteArray',
      offset: 8,
      len: 4,
      repeated: 2,
      subFields: {
        // ch: '最小频率'
        min: {
          type: 'u16',
          offset: 0,
        },
        // ch: '最大频率'
        max: {
          type: 'u16',
          offset: 2,
        },
      },
    },
    // ch: '固件版本'
    firmwareVersion: {
      type: 'byteArray',
      len: 4,
      offset: 16,
    },
  },
}

class TD910DeviceInfo extends DeviceInfo {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 1
    this.structLen = options.structLen || 20
  }
}

// 总体设置
const GeneralSettingsProto = {
  0: {
    // ch: '设备名称',
    deviceName: {
      type: 'stringU16',
      len: 32,
    },
    // ch: '设备ID', [1,16776415]
    intDmrId: {
      type: 'u32',
      offset: 32,
    },
    // ch: '中继id', [1,16777215]
    repeaterId: {
      type: 'u32',
      offset: 36,
    },
    // 发射前导码持续时间 [0,8640] 默认值 960 ms 间隔 240
    sendLeadCodeTime: {
      type: 'u8',
      interval: 240,
      offset: 40,
    },
    // ch: '脱网组呼挂起时间',[0,7000] 默认值 3000 ms  间隔 500
    offlineGroupCallHungTime: {
      type: 'u8',
      interval: 500,
      offset: 41,
    },
    // ch: '脱网单呼挂起时间',[0,7000]  默认值 3000 ms  间隔 500
    offlineSingleCallHungTime: {
      type: 'u8',
      interval: 500,
      offset: 42,
    },
    baseSettings: {
      type: 'u8',
      offset: 43,
      bits: {
        // ch: '语言环境', 0 中文 (默认值) 1 英文
        locale: {
          len: 1,
          offset: 0,
        },
        // ch: '禁用所有LED',
        disabledAllLED: {
          len: 1,
          bool,
          offset: 1,
          default: false,
        },
        // ch: '拒绝陌生呼叫',
        rejectUnfamiliarCall: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
        // ch: '直通模式',
        directMode: {
          len: 1,
          bool,
          offset: 3,
          default: false,
        },
        // 是否PTT独立，default = 00
        pttAlone: {
          len: 1,
          bool,
          offset: 4,
          default: false,
        },
        // 是否允许自毁，default = 1
        allowedSelfDestruct: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 隐蔽模式耳麦是否静音，default = 0
        stealthModeHeadsetMute: {
          len: 1,
          bool,
          offset: 6,
          default: false,
        },
      },
    },
    // ch: '省电模式', 下拉列表 0 1:1, 1 1:2, 2 1:3 (默认值), 3 1:4
    savePowerMode: {
      type: 'u8',
      offset: 44,
    },
    // ch: '声控等级', [关,1-9] 默认值 0
    soundCtrlLevel: {
      type: 'u8',
      offset: 45,
    },
    // ch: '声控延迟',[500,10000]  默认值 500 ms 间隔 500
    soundCtrlDelay: {
      type: 'u8',
      interval: 500,
      offset: 46,
    },
    soundSettings: {
      type: 'u8',
      offset: 47,
      bits: {
        // ch: '全部静音',
        muteAll: {
          len: 1,
          bool,
          offset: 0,
        },
        // ch: '语音提示',
        voiceNotice: {
          len: 1,
          bool,
          offset: 1,
        },
        // ch: '信道空闲提示',
        channelFreeNotice: {
          len: 1,
          bool,
          offset: 2,
        },
        // ch: '呼叫允许指示',000 无  001 模拟  010 模拟和数字 011 数字
        allowCallInstruction: {
          len: 3,
          offset: 3,
        },
      },
    },
    // ch: '接收低电池电量提示间隔',[0,635] 间隔 5
    powerInfoAlert: {
      type: 'u8',
      interval: 5,
      offset: 48,
    },
    // ch: 时区小时 [-12,12] 默认值 8
    timeZoneHour: {
      type: 'int8',
      offset: 49,
    },
    // ch: 时区分钟 [0,59]  默认值 0
    timeZoneMinute: {
      type: 'u8',
      offset: 50,
    },
    // 同步时间 0 不同步(默认值) 1 同步
    syncTime: {
      type: 'u8',
      offset: 51,
    },
    // ch: 年
    year: {
      type: 'u16',
      offset: 52,
    },
    // ch: 月
    month: {
      type: 'u8',
      offset: 54,
    },
    // ch: 日
    day: {
      type: 'u8',
      offset: 55,
    },
    // ch: 时
    hour: {
      type: 'u8',
      offset: 56,
    },
    // ch: 分
    minute: {
      type: 'u8',
      offset: 57,
    },
    // ch: 秒
    second: {
      type: 'u8',
      offset: 58,
    },
    recordSettings: {
      type: 'u8',
      offset: 59,
      bits: {
        // ch: '录音使能',默认值 0
        enable: {
          len: 1,
          bool,
          offset: 0,
        },
        // ch: '录音压缩比',0 不压缩, 1 3.5倍  当录音使能==0时不可用
        compressionRatio: {
          len: 1,
          offset: 1,
        },
        // ch: '允许擦除',
        allowErasing: {
          len: 1,
          bool,
          offset: 2,
        },
      },
    },
    powerTransfer: {
      type: 'u8',
      offset: 60,
      bits: {
        // 功率自动转换使能	0 禁用(默认值) 1 启用
        enable: {
          len: 1,
          bool,
          offset: 0,
        },
      },
    },
    // 切换到高功率门限	[-115,-75]dBm 绝对值传输
    highPowerLimit: {
      type: 'u8',
      offset: 61,
    },
    // 切换到中功率门限	[-105,-70]dBm 绝对值传输
    middlePowerLimit: {
      type: 'u8',
      offset: 62,
    },
    // 切换到低功率门限	[-95,-65]dBm 绝对值传输
    lowPowerLimit: {
      type: 'u8',
      offset: 63,
    },
  },
}

class GeneralSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 4
    this.structLen = options.structLen || 64
  }

  decode(structData) {
    const data = super.decode(structData)
    data.result = data.result.map((item) => {
      item.highPowerLimit = item.highPowerLimit * -1
      item.middlePowerLimit = item.middlePowerLimit * -1
      item.lowPowerLimit = item.lowPowerLimit * -1
      return item
    })
    return data
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }

    const list = data.map((item) => {
      item.highPowerLimit = Math.abs(item.highPowerLimit)
      item.middlePowerLimit = Math.abs(item.middlePowerLimit)
      item.lowPowerLimit = Math.abs(item.lowPowerLimit)
      return item
    })

    return super.encode(list)
  }
}

// 按键设置
export const ButtonKeys = generateEnumObject({
  // 未定义软按键功能
  NONE: 0,
  // 单键功能呼叫1
  SPDCALL1: 1,
  // 单键功能呼叫2
  SPDCALL2: 2,
  // 单键功能呼叫3
  SPDCALL3: 3,
  // 单键功能呼叫4
  SPDCALL4: 4,
  // 单键功能呼叫5
  SPDCALL5: 5,
  // 单键功能呼叫6
  SPDCALL6: 6,
  // // 预设信道1
  // CH_PRESET1: 7,
  // // 预设信道2
  // CH_PRESET2: 8,
  // // 预设信道3
  // CH_PRESET3: 9,
  // // 预设信道4
  // CH_PRESET4: 10,
  // 紧急模式开启
  WARNING_ON: 11,
  // 紧急模式关闭
  WARNING_OFF: 12,
  // 监听
  MONI: 13,
  // 永久监听(短按隐藏)
  LONGMONI: 14,
  // 键盘锁
  KEYLOCK: 15,
  // 电池电量提示
  BATTERY_CHANGE: 16,
  // 当前日期提示
  // DATE_TIME: 17,
  // 区域切换
  ZONE_SWITCH: 18,
  // 信道上调
  // CH_UP: 19,
  // 信道下调
  // CH_DOWN: 20,
  // 音量上调
  // VOLUME_UP: 21,
  // 音量下调
  // VOLUMN_DOWN: 22,
  // 优先打断
  INT_PRI: 23,
  // 数字模拟切换
  // A_D_SWITCH: 24,
  // 功率切换
  PWRMODE_CHG: 25,
  // 中继/脱网
  NETMODE_CHG: 26,
  // 无用信道删除
  DEL_INVALID_CHL: 27,
  // 对讲机激活
  DEV_ACTIVE: 28,
  // 对讲机遥毙
  DEV_DIE: 29,
  // 对讲机检测
  DEV_DETECT: 30,
  // 远程监听
  RMT_MONITOR: 31,
  // 背光灯自动开/关
  // BACKLIGNT_SWITCH: 32,
  // 强光手电开/关
  GLARE_FLASHLIGHT: 33,
  // 所有提示音开关
  TONE_MUTE_SWITCH: 34,
  // 扫描开/关
  SCAN_SWITCH: 35,
  // 声控开关
  VOX_SWITCH: 36,
  // GPS开关
  // GPS_SWITCH: 37,
  // 录音开关
  // RECORD_SWITCH: 38,
  // 单独工作开关
  WORK_ALONE_SWITCH: 39,
  // 倒放开关
  // WORK_DOWN_SWITCH: 40,
  // 漫游开关
  ROAM_SWITCH: 41,
  // 系统状态查询
  SystemStatusQuery: 42,
  // 站点锁定开关，支持漫游功能设备显示
  SITE_LOCK_SWITCH: 43,
  // 手动站点漫游，支持漫游功能设备显示
  ROAM_MANUAL: 44,
  // 对讲机遥晕
  DEV_STUN: 45,
  // 对讲机遥醒
  DEV_WAKEUP: 46,
  // 隐蔽模式开/关
  HIDE_SWITCH: 47,
  // 救援扫描模式开/关
  RESCUE_SCAN_SWITCH: 48,
  // 对讲机遥毁
  DEV_RUIN: 49,

  /* 界面相关 */
  // 手动拨号界面
  // SCALL_DIAL_MENU: 64,
  // 联系人列表
  CONTAS_LIST_MENU: 65,
  // 常用联系人列表
  // TOP_CONTAS_LIST_MENU: 66,
  // 电话联系人列表
  PHONE_LIST_MENU: 67,
  // DTMF键盘
  // DTMF_MENU: 68,
  // 短信
  MSG_MENU: 69,

  /* 以下选项紧限于生产调试 */
  // // 误码率测试接收
  // ERRNUMBER_RX: 128,
  // // 误码率测试发射
  // ERRNUMBER_TX: 129,
  // // 基带固件升级
  // BASEBAND_FIRMWARE: 130,
  // // 发射测试
  // TX_TEST: 131,
})
// 单键呼叫类型定义
export const SoftKeyCallType = generateEnumObject({
  SINGLE: 0,		// 单呼
  GROUP: 1,			// 组呼
  TIP: 2,			// 呼叫提示
  MSG: 3, 			// 短信
})
export const ButtonSettingsProto = {
  0: {
    // ch: '长按持续时间' [250,3750] 间隔 250
    longPressTime: {
      type: 'u8',
      interval: 250,
      offset: 0,
    },
    // ch: '侧键 短按/长按',
    sideKey: {
      type: 'byteArray',
      offset: 1,
      len: 2, // subFields协议字段的实际长度
      repeated: 3,
      subFields: {
        short: {
          type: 'u8',
          offset: 0,
        },
        long: {
          type: 'u8',
          offset: 1,
        },
      },
    },
    // ch: '单键呼叫'
    singleKeyCall: {
      type: 'byteArray',
      offset: 7,
      len: 4,
      repeated: 6,
      subFields: {
        // 呼叫ID	通讯录索引
        callId: {
          type: 'u16',
          offset: 0,
        },
        // 呼叫类型
        callType: {
          type: 'u8',
          offset: 2,
        },
        // 短信ID	短信索引
        smsId: {
          type: 'u8',
          offset: 3,
        },
      },
    },
    // 预设信道
    defaultChannel: {
      type: 'byteArray',
      offset: 31,
      len: 4,
      repeated: 4,
      subFields: {
        // 区域ID 65535表示空
        zoneId: {
          type: 'u16',
          offset: 0,
        },
        // 信道ID 65535表示空
        channelId: {
          type: 'u16',
          offset: 2,
        },
      },
    },
  },
}

class ButtonSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 5
    this.structLen = options.structLen || 47
  }
}

// 短信  最大数量: 100
const SmsProto = {
  0: {
    // ch: '短信id',
    msgId: {
      type: 'u8',
    },
    // ch: '短信内容',
    msgContent: {
      type: 'stringU16',
      len: 280,
      offset: 1,
    },
  },
}

class ShortMessage extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 6
    this.structLen = options.structLen || 281
  }
}

// 加密配置
const EncryptConfigProto = {
  0: {
    config: {
      type: 'u8',
      offset: 0,
      bits: {
        // 加密使能
        encryptEnable: {
          offset: 0,
          bool,
          len: 1,
        },
      },
    },
  },
}

class EncryptSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 7
    this.structLen = options.structLen || 1
  }
}

// 加密列表 最大数量: 32
const EncryptListProto = {
  0: {
    // 密钥ID
    keyId: {
      type: 'u8',
      offset: 0,
    },
    // 密钥值
    keyValue: {
      type: 'string',
      offset: 1,
      len: 10,
    },
    // 密钥名称
    keyName: {
      type: 'stringU16',
      offset: 11,
      len: 32,
    },
  },
}

class EncryptList extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 8
    this.structLen = options.structLen || 43
  }
}

// 菜单配置
const MenuSettingsProto = {
  0: {
    // ch: '菜单挂起时间',[0,30] 默认值 30s
    hangTime: {
      type: 'u8',
    },
    baseSetting: {
      type: 'u8',
      offset: 1,
      bits: {
        // 菜单键关闭 0 禁用 1 可用
        menuOff: {
          len: 1,
          bool,
          offset: 0,
        },
        // 信道显示模式 0 频率显示 1 信道显示 2 频率+信道
        chDisplayMode: {
          len: 2,
          offset: 1,
        },
        // 显示呼叫ID和别名 0 禁用 1 可用 默认值 1
        showAlias: {
          len: 1,
          bool,
          offset: 3,
        },
      },
    },
    // 开机密码
    powerOnPwd: {
      type: 'password',
      offset: 2,
      len: 6,
    },
    // 信道配置密码
    chConfigPwd: {
      type: 'password',
      offset: 8,
      len: 6,
    },
    // 通信录,联系人列表,编辑,手动拨号,个呼拨号,组呼拨号,新建联系人,呼叫提示
    contactConfig: {
      type: 'u8',
      offset: 14,
      bits: {
        // 通信录	0 禁用 1 可用 默认值 1
        contacts: {
          len: 1,
          bool,
          offset: 0,
        },
        // 联系人列表
        contactList: {
          len: 1,
          bool,
          offset: 1,
        },
        // 编辑
        editEnable: {
          len: 1,
          bool,
          offset: 2,
        },
        // 手动拨号
        manualDialing: {
          len: 1,
          bool,
          offset: 3,
        },
        // 个呼拨号
        privateCallDialing: {
          len: 1,
          bool,
          offset: 4,
        },
        // 组呼拨号
        groupCallDialing: {
          len: 1,
          bool,
          offset: 5,
        },
        // 新建联系人
        newContact: {
          len: 1,
          bool,
          offset: 6,
        },
        // 呼叫提示
        callPrompting: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // 设备检测,远程监听,设备激活,设备遥毙
    deviceControl: {
      type: 'u8',
      offset: 15,
      bits: {
        // ch: '设备检测',
        deviceDetect: {
          len: 1,
          bool,
          offset: 0,
        },
        // ch: '远程监听',
        remoteMonitor: {
          len: 1,
          bool,
          offset: 1,
        },
        // ch: '设备激活',
        deviceActive: {
          len: 1,
          bool,
          offset: 2,
        },
        // ch: '设备遥毙',
        deviceRemoteDeath: {
          len: 1,
          bool,
          offset: 3,
        },
        // 对讲机遥毁 1 Enable, 0 Disable. default = 0
        deviceRemoteDestroy: {
          len: 1,
          bool,
          offset: 4,
          default: false,
        },
        // 对讲机遥晕 1 Enable, 0 Disable. default = 0
        deviceStun: {
          len: 1,
          bool,
          offset: 5,
          default: false,
        },
        // 对讲机遥醒 1 Enable, 0 Disable. default = 0
        deviceWokeUp: {
          len: 1,
          bool,
          offset: 6,
          default: false,
        },
      },
    },
    // 电话本,联系人列表,手动拨号
    phoneConfig: {
      type: 'u8',
      offset: 16,
      bits: {
        // 电话本,0 禁用 1 可用 默认值 1
        enable: {
          len: 1,
          bool,
          offset: 0,
        },
        // 联系人列表
        list: {
          len: 1,
          bool,
          offset: 1,
        },
        // 手动拨号
        manualDialing: {
          len: 1,
          bool,
          offset: 2,
        },
      },
    },
    // 区域
    zoneConfig: {
      type: 'u8',
      offset: 17,
      bits: {
        // 区域开关,0 禁用 1 可用 默认值 1
        enable: {
          len: 1,
          bool,
          offset: 0,
        },
      },
    },
    // 扫描/漫游
    scanConfig: {
      type: 'u8',
      offset: 18,
      bits: {
        // 扫描开关,0 禁用 1 可用 默认值 1
        enable: {
          len: 1,
          bool,
          offset: 0,
        },
        // 编辑扫描列表,0 禁用 1 可用 默认值 1
        editList: {
          len: 1,
          bool,
          offset: 1,
        },

        // 以下为漫游配置
        // 漫游开关
        roamEnable: {
          len: 1,
          bool,
          offset: 2,
        },
        // 站点锁定
        roamLockSite: {
          len: 1,
          bool,
          offset: 3,
        },
        // 手动站点漫游
        roamManual: {
          len: 1,
          bool,
          offset: 4,
        },
      },
    },
    // 短信
    smsConfig: {
      type: 'u8',
      offset: 19,
      bits: {
        // 开关,0 禁用 1 可用 默认值 1
        enable: {
          len: 1,
          bool,
          offset: 0,
        },
      },
    },
    // 呼叫记录,未接记录,已接记录,呼出记录
    callConfig: {
      type: 'u8',
      offset: 20,
      bits: {
        // 呼叫记录
        callHistory: {
          len: 1,
          bool,
          offset: 0,
        },
        // 未接记录
        missedCall: {
          len: 1,
          bool,
          offset: 1,
        },
        // 已接记录
        receivedCall: {
          len: 1,
          bool,
          offset: 2,
        },
        // 呼出记录
        outgoingRecord: {
          len: 1,
          bool,
          offset: 3,
        },
      },
    },
    // 设置,对讲机设置,脱网,音调提示,发射功率,背光,开机界面,键盘锁
    deviceConfig: {
      type: 'u8',
      offset: 21,
      bits: {
        // 设置
        setting: {
          len: 1,
          bool,
          offset: 0,
        },
        // 对讲机设置
        deviceSetting: {
          len: 1,
          bool,
          offset: 1,
        },
        // 脱网
        offline: {
          len: 1,
          bool,
          offset: 2,
        },
        // 音调提示
        toneTip: {
          len: 1,
          bool,
          offset: 3,
        },
        // 发射功率
        transmitPower: {
          len: 1,
          bool,
          offset: 4,
        },
        // 背光
        backLight: {
          len: 1,
          bool,
          offset: 5,
        },
        // 开机界面
        bootInterface: {
          len: 1,
          bool,
          offset: 6,
        },
        // 键盘锁
        keyboardLock: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // LED 指示灯,静噪,开机密码,语言环境,声控,时间设置,定位,单独工作
    deviceConfig2: {
      type: 'u8',
      offset: 22,
      bits: {
        // ch: 'LED指示灯',
        ledIndicator: {
          len: 1,
          bool,
          offset: 0,
        },
        // ch: '静噪',
        quieting: {
          len: 1,
          bool,
          offset: 1,
        },
        // ch: '开机密码',
        powerOnPassword: {
          len: 1,
          bool,
          offset: 2,
        },
        // ch: '语言环境',
        locale: {
          len: 1,
          bool,
          offset: 3,
        },
        // ch: '声控',
        soundCtrl: {
          len: 1,
          bool,
          offset: 4,
        },
        // ch: '时间设置',
        timeSetting: {
          len: 1,
          bool,
          offset: 5,
        },
        // ch: '定位',
        locate: {
          len: 1,
          bool,
          offset: 6,
        },
        // ch: '单独工作',
        workAlone: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // 倒放,录音,U盘模式
    recordConfig: {
      type: 'u8',
      offset: 23,
      bits: {
        // 倒放
        upend: {
          len: 1,
          bool,
          offset: 0,
        },
        // ch: '录音',
        recording: {
          len: 1,
          bool,
          offset: 1,
        },
        // ch: 'U盘模式',
        uDiskMode: {
          len: 1,
          bool,
          offset: 2,
        },
        // 求救/救援 1 Enable, 0 Disable. default = 1
        maydayRescue: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 隐蔽模式 1 Enable, 0 Disable. default = 1
        stealthMode: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
      },
    },
    // 对讲机信息
    deviceInfo: {
      type: 'u8',
      offset: 24,
      bits: {
        // ch: '对讲机信息',
        info: {
          len: 1,
          bool,
          offset: 0,
        },
      },
    },
    // 信道配置总开关,接收频率,发射频率,信道名称,发射限时,亚音频设置,发射联系人,彩色码
    channelSetting: {
      type: 'u8',
      offset: 25,
      bits: {
        // ch: '信道配置总开关',
        chConfigEnable: {
          len: 1,
          bool,
          offset: 0,
        },
        // ch: '接收频率',
        receivingFrequency: {
          len: 1,
          bool,
          offset: 1,
        },
        // ch: '发射频率',
        transmittingFrequency: {
          len: 1,
          bool,
          offset: 2,
        },
        // ch: '信道名称',
        channelName: {
          len: 1,
          bool,
          offset: 3,
        },
        // ch: '发射限时',
        roamInterface: {
          len: 1,
          bool,
          offset: 4,
        },
        // ch: '亚音频设置',
        subAudioSetting: {
          len: 1,
          bool,
          offset: 5,
        },
        // ch: '发射联系人',
        launchContact: {
          len: 1,
          bool,
          offset: 6,
        },
        // ch: '彩色码',
        colorCode: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // 时隙,虚拟集群发射时隙,接收列表
    timeSlotSetting: {
      type: 'u8',
      offset: 26,
      bits: {
        // ch: '时隙',
        timeSlot: {
          len: 1,
          bool,
          offset: 0,
        },
        // ch: '虚拟集群发射时隙',
        virtualClusterTimeSlot: {
          len: 1,
          bool,
          offset: 1,
        },
        // ch: '接收列表',
        receivingList: {
          len: 1,
          bool,
          offset: 2,
        },
      },
    },
  },
}

class MenuSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 14
    this.structLen = options.structLen || 27
  }
}

// 信令系统
const SignalingSystemProto = {
  0: {
    remoteConfig: {
      type: 'u8',
      offset: 0,
      bits: {
        // 遥毙解码 0 不允许 1 允许(默认值)
        remoteDeathDecode: {
          len: 1,
          bool,
          offset: 0,
        },
        // 远程监听解码 0 不允许(默认值) 1 允许
        remoteMonitorDecode: {
          len: 1,
          bool,
          offset: 1,
        },
        // 紧急远程监听解码 0 不允许 1 允许
        urgentRemoteMonitorDecode: {
          len: 1,
          bool,
          offset: 2,
        },
        // 远程提示解码 0 不允许 1 允许(默认值)
        remoteNoticeDecode: {
          len: 1,
          bool,
          offset: 3,
        },
        // 远程检测解码 0 不允许 1 允许(默认值)
        remoteDetectDecode: {
          len: 1,
          bool,
          offset: 4,
        },
        // 远程销毁解码 0 不允许 1 允许(默认值)
        remoteEraseDecode: {
          len: 1,
          bool,
          offset: 5,
        },
        // 远程遥晕/遥醒解码 1-允许，0-不允许。default = 1
        remoteStunWakeupDecode: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
      },
    },
    // 远程监听持续时间 [10,120] 界面步进间隔 10
    remoteMonitorDuration: {
      type: 'u8',
      // interval: 10,
      offset: 1,
    },
    // 信令密码
    signalingPwd: {
      type: 'password',
      len: 6,
      offset: 2,
    },
  },
}

class SignalingSystem extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 15
    this.structLen = options.structLen || 8
  }
}

// 警报配置
const AlertConfigProto = {
  0: {
    // 单独工作使能 以下参数当此使能=0时不可用  默认值 0
    aloneWorkEnable: {
      type: 'u8',
      bool,
      offset: 0,
    },
    // 单独工作响应时间 单位 分钟  [0,255]
    aloneWorkTime: {
      type: 'u8',
      offset: 1,
    },
    // 单独工作提醒时间 单位 秒  [0,255]
    aloneWorkRemindTime: {
      type: 'u8',
      offset: 2,
    },
    // 单独工作响应操作 0 按钮  1 语音发射
    aloneWorkResOpt: {
      type: 'u8',
      offset: 3,
    },
    // // 倒放使能 以下参数当此使能位=0时不可用 默认值 0
    // upendEnable: {
    //   type: 'u8',
    //   bool,
    //   offset: 4
    // },
    // // 进入延时 单位 秒  [5,255]  默认值 10s
    // entryDelay: {
    //   type: 'u8',
    //   offset: 5
    // },
    // // 退出延时 单位 秒  [0,254]  255 无限  默认值 10s
    // quitDelay: {
    //   type: 'u8',
    //   offset: 6
    // },
    // // 倒放预提示时间,触发倾斜度,倒放触发方式
    // upendConfig: {
    //   type: 'u8',
    //   offset: 11,
    //   bits: {
    //     // ch: '倒放预提示时间',单位 秒  [0,10] 默认值 5秒
    //     rewindTime: {
    //       len: 4,
    //       offset: 0
    //     },
    //     // ch: '触发倾斜度',倒放触发方式=2时，不可用  0 60度  1 45度 2 30度
    //     triggerTilt: {
    //       len: 2,
    //       offset: 4
    //     },
    //     // ch: '倒放触发方式',0 仅倾斜  1 仅运动检测  2 倾斜或运动检测
    //     triggerMode: {
    //       len: 2,
    //       offset: 6
    //     }
    //   }
    // }
  },
}

class AlertConfig extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 16
    this.structLen = options.structLen || 8
  }
}

// 数字警报 最大数量: 32
const DigitalAlertProto = {
  0: {
    // 报警ID
    id: {
      type: 'u8',
      offset: 0,
    },
    // 警报类型 0 禁止  1 常规 2 静默  3 静默带语音
    type: {
      type: 'u8',
      offset: 1,
    },
    // 警报模式 0 紧急报警 1 紧急报警和呼叫 2 紧急报警和语音
    mode: {
      type: 'u8',
      offset: 2,
    },
    // 回复信道
    replyChannel: {
      type: 'u16',
      offset: 3,
    },
    // 不礼貌重试 [1,15]
    impoliteRetry: {
      type: 'u8',
      offset: 5,
    },
    // 礼貌重试 [0,14]  0xFF: 无限
    politeRetry: {
      type: 'u8',
      offset: 6,
    },
    // Hot Mic 持续时间 [10,120]  间隔 10
    hotMicDuration: {
      type: 'u8',
      offset: 7,
    },
    config: {
      type: 'u8',
      offset: 8,
      bits: {
        // 报警自动发送GPS消息	0 不允许(默认值) 1 允许
        autoSendGps: {
          bool,
          offset: 0,
          len: 1,
        },
      },
    },
    // 数字报警名称
    name: {
      type: 'stringU16',
      len: 32,
      offset: 9,
    },
  },
}

class DigitalAlert extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 18
    this.structLen = options.structLen || 41
  }
}

// 数字通讯录列表  最大数量: 10
const AddressGroupProto = {
  0: {
    // 群组 ID
    id: {
      type: 'u16',
      offset: 0,
    },
    // 群组名字
    name: {
      type: 'stringU16',
      len: 32,
      offset: 2,
    },
  },
}

class AddressGroup extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 19
    this.structLen = options.structLen || 34
  }
}

// 数字通讯录  最大数量: 600
export const CallType = generateEnumObject({
  NONE: 0,
  // 组呼
  GROUP: 1,
  // 单呼
  SINGLE: 2,
  // 全呼
  BROADCAST: 3,
})

const DigitalAddressProto = {
  0: {
    // 通讯录 ID
    id: {
      type: 'u16',
    },
    // 归属哪一个群组
    groupId: {
      type: 'u16',
      offset: 2,
    },
    // ch: '名称',
    name: {
      type: 'stringU16',
      len: 32,
      offset: 4,
    },
    // ch: '号码',
    number: {
      type: 'mulInt',
      len: 3,
      offset: 36,
    },
    // ch: '呼叫类型
    callType: {
      type: 'u8',
      offset: 39,
    },
    // ch: '铃声类型',界面不显示
    ringType: {
      type: 'u8',
      offset: 40,
    },
  },
}

class DigitalAddress extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 20
    this.structLen = options.structLen || 41
  }
}

// 电话通讯录  最大数量: 100
class PhoneBook extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 21
    this.structLen = options.structLen || 54
  }
}

// 接收组列表 最大数量: 32
const RxGroupProto = {
  0: {
    // ch: '接收组 ID',
    groupId: {
      type: 'u8',
    },
    // ch: '成员数量',
    count: {
      type: 'u8',
      offset: 1,
    },
    // ch: '成员列表',
    listenGroup: {
      type: 'u16',
      repeated: 16,
      offset: 2,
    },
    // ch: '名称',
    groupName: {
      type: 'stringU16',
      len: 32,
      offset: 34,
    },
  },
}

class RxGroup extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 22
    this.structLen = options.structLen || 66
  }
}

// 1级区域 最大区域数量: 4
const ZoneRootProto = {
  0: {
    // 区域 ID
    zoneId: {
      type: 'u16',
    },
    // 区域名称
    zoneName: {
      type: 'stringU16',
      len: 32,
      offset: 2,
    },
  },
}

class ZoneRoot extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 23
    this.structLen = options.structLen || 34
  }
}

// 2级区域 最大区域数量: 1000
const ZoneParentProto = {
  0: {
    // 区域 ID
    zoneId: {
      type: 'u16',
    },
    // 所属1级区域ID
    rootId: {
      type: 'u16',
      offset: 2,
    },
    // 区域名称
    zoneName: {
      type: 'stringU16',
      len: 32,
      offset: 4,
    },
  },
}

class ZoneParent extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 24
    this.structLen = options.structLen || 36
  }
}

// 区域数据  最大区域数量: 2000  一个区域最大包含信道: 64
const ZoneLeafProto = {
  0: {
    // 区域 ID
    zoneId: {
      type: 'u16',
    },
    // 所属1级区域ID
    rootId: {
      type: 'u16',
      offset: 2,
    },
    // 所属2级区域ID
    parentId: {
      type: 'u16',
      offset: 4,
    },
    // 区域有效位表示	1 有效 其它值无效
    zoneEffective: {
      type: 'u16',
      offset: 6,
    },
    // 区域名称
    zoneName: {
      type: 'stringU16',
      len: 32,
      offset: 8,
    },
    // 本区域信道使用标志	以bit位表示
    usedFlag: {
      type: 'longInt',
      offset: 40,
    },
    // 本区域信道索引表
    usedList: {
      type: 'u16',
      offset: 48,
      repeated: 64,
    },
  },
}

class ZoneLeaf extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 25
    this.structLen = options.structLen || 176
  }
}

// 模拟信道参数
const analogChannelBits = {
  funcConfig: {
    // 带宽标志	0 宽带 1 窄带 默认值 1
    bandwidthFlag: {
      len: 1,
      offset: 0,
    },
    // 拍频	0 关 1 开 默认值 0
    beatFreq: {
      len: 1,
      bool,
      offset: 1,
    },
    // 预加重	0 关(默认值) 1 开 界面不显示
    preEmphasis: {
      len: 1,
      offset: 2,
    },
    // 压扩	0 关(默认值) 1 开 界面不显示
    companding: {
      len: 1,
      offset: 3,
    },
    // 扰频开关	0 关(默认值) 1 开 界面不显示
    scrambleEnable: {
      len: 1,
      offset: 4,
    },
    // 信令系统	0 两音(默认值) 1 五音 2 DTMF
    signalingSystem: {
      len: 3,
      offset: 5,
    },
    // 尾音消除	0 关 1 开(默认值)
    tailCancellation: {
      len: 1,
      bool,
      offset: 8,
    },
    // 爆破音	0 关亚音 1 标准相位 2 非标准相位 发射亚音为模拟亚音时此项可用
    ctcssTxDps: {
      len: 2,
      offset: 9,
    },
    // 接收尾音	0 无亚音 1 标准相位 2 非标准相位 接收亚音为模拟亚音时此项可用
    ctcssRxDps: {
      len: 2,
      offset: 11,
    },
    // 发射权限	0 关(默认) 1 载波 2 CT/DCS
    bclFlag: {
      len: 2,
      offset: 13,
    },
  },
}
const analogChannelProto = {
  0: {
    // 带宽标志, 拍频, 预加重, 压扩, 扰频开关, 允许脱网标志, 尾音消除, 爆破音, 接收尾音,发射权限(BCL)
    funcConfig: {
      type: 'u16',
      offset: 10,
      bits: analogChannelBits.funcConfig,
    },
    // 扰频值	[0,6000] 默认值 3300
    scramble: {
      type: 'u16',
      offset: 12,
    },
    // 亚音解码  亚音编码规则 支持自定义亚音
    subsonicDecode: {
      type: 'u16',
      offset: 14,
    },
    // 亚音编码 默认值 0xFFFF(无)
    subsonicEncode: {
      type: 'u16',
      offset: 16,
    },
    // 接收信令系统
    rxSignallingSystem: {
      type: 'u8',
      offset: 18,
    },
    // 发射信令系统
    txSignallingSystem: {
      type: 'u8',
      offset: 19,
    },
  },
}
// 数字信道
const digitalChannelBits = {
  timeSlotConfig: {
    // 时隙	0 1(默认值0), 1 2, 2 虚拟集群
    timeSlot: {
      len: 2,
      offset: 0,
    },
    // 虚拟集群发射时隙	0 无(默认值 0),1 1,2 2  当 时隙==2 时 此项才可选
    virtualTimeSlot: {
      len: 2,
      offset: 2,
    },
    // 直通双时隙使能	0 未选择(默认值) 1 选中
    throughEnable: {
      len: 1,
      bool,
      offset: 4,
    },
    // 信道时隙校准器	0 不合格(默认值) 1 合格 2 首选   直通双时隙使能==1时此项才可选
    chSlotAdjust: {
      len: 2,
      offset: 5,
    },
    // 优先打断使能	0 未选择(默认值) 1 选中
    priorityInterrupt: {
      len: 1,
      bool,
      offset: 7,
    },
  },
  alertConfig: {
    // 语音优先级	[0,3] 默认值 0 隐藏
    voicePriority: {
      len: 2,
      offset: 0,
    },
    // 紧急警报指示	0 未选择 1 选中 默认值 0
    emergencyAlertTip: {
      len: 1,
      bool,
      offset: 2,
    },
    // 紧急警报确认
    emergencyAlertConfirm: {
      len: 1,
      bool,
      offset: 3,
    },
    // 紧急呼叫指示	0 未选择 1 选中 默认值 0
    emergencyCallTip: {
      len: 1,
      bool,
      offset: 4,
    },
    // 准许条件	0 可用彩色码 1 始终 2 信道空闲
    permitConditions: {
      len: 2,
      offset: 5,
    },
    // 单呼应答	0 未选择 1 选中 默认值 0
    singleCallRes: {
      len: 1,
      bool,
      offset: 7,
    },
  },
  networkConfig: {
    // 联网: 1-允许，0-不允许	default=0，数模信道不存在联网功能
    networking: {
      len: 1,
      bool,
      offset: 0,
    },
    // 本地呼叫
    localCall: {
      len: 1,
      bool,
      offset: 1,
    },
  },
  encryptConfig: {
    // 加密使能	0 禁用 1 启用 默认值 0
    enable: {
      bool,
      offset: 0,
      len: 1,
    },
    // 加密类型	0 基础 1 高级 默认值 0
    type: {
      offset: 1,
      len: 1,
    },
    // 加密算法	0 异或(默认值 0) 1 增强异或 2 ARC4 3 AES256
    // 加密类型==高级，0和1不可选
    algorithm: {
      offset: 2,
      len: 3,
    },
    // 随机密钥	0 未选择(默认值) 1 选中
    // 加密类型==基础，此项不可选
    randomKey: {
      bool,
      offset: 5,
      len: 1,
    },
    // 随机算法	0 禁用(默认值) 1 启用
    // 加密类型==基础，此项不可选
    randomAlg: {
      bool,
      offset: 6,
      len: 1,
    },
    // 短信加密	0 未选择(默认值) 1 选中
    smsEncrypt: {
      bool,
      offset: 7,
      len: 1,
    },
  },
}
const digitalChannelProto = {
  0: {
    // 彩色码	[0,15]
    colorCode: {
      type: 'u8',
      offset: 0,
    },
    timeSlotConfig: {
      type: 'u8',
      offset: 1,
      bits: digitalChannelBits.timeSlotConfig,
    },
    alertConfig: {
      type: 'u8',
      offset: 2,
      bits: digitalChannelBits.alertConfig,
    },
    networkConfig: {
      type: 'u8',
      offset: 3,
      bits: digitalChannelBits.networkConfig,
    },
    encryptConfig: {
      type: 'u8',
      offset: 4,
      bits: digitalChannelBits.encryptConfig,
    },
    // 密钥列表	随机密钥开启时，不可选
    encryptKeyList: {
      type: 'u8',
      offset: 5,
    },
    // 接收组	默认值 MAX_RECEIVE_GROUP_LIST(无)
    receiveGroup: {
      type: 'u8',
      offset: 6,
    },
    // 默认通讯录地址 默认值 65535(无)
    defaultAddress: {
      type: 'u16',
      offset: 7,
    },
    // 紧急系统ID值	默认值 255(无)
    emergencySysId: {
      type: 'u8',
      offset: 9,
    },
  },
}
// 数模兼容
const compatibleChannelProto = {
  0: {
    ...digitalChannelProto[0],
    ...analogChannelProto[0],
  },
}
// 信道数据
const ChannelProto = {
  0: {
    // 信道 ID
    chId: {
      type: 'u16',
      offset: 0,
    },
    // 所属区域
    zoneId: {
      type: 'u16',
      offset: 2,
    },
    // 信道类型 0 数字 1 模拟 2 数字兼容 3 模拟兼容
    chType: {
      type: 'u8',
      offset: 4,
    },
    // 扫描列表/漫游列表	默认值 255(无)
    scanList: {
      type: 'u8',
      offset: 5,
    },
    // 列表类型,自动扫描标志, 只接收标志, IP站点连接,自动漫游标识,允许脱网标志
    scanConfig: {
      type: 'u8',
      offset: 6,
      bits: {
        // 列表类型	0 扫描列表 1 漫游列表(只支持数字信道)
        listType: {
          len: 1,
          offset: 0,
        },
        // 自动扫描标志
        autoScan: {
          len: 1,
          bool,
          offset: 1,
        },
        // 只接收标志	0 正常 1 只接收 默认值 0
        onlyReceive: {
          len: 1,
          bool,
          offset: 2,
        },
        // IP站点连接	0 关闭 1 使能 只支持数据信道
        ipSiteConnect: {
          bool,
          len: 1,
          offset: 3,
        },
        // 自动漫游标识
        autoRoam: {
          bool,
          len: 1,
          offset: 4,
        },
        // 允许脱网标志 0 不允许(默认值) 1 允许
        allowOfflineSign: {
          bool,
          len: 1,
          offset: 5,
        },
        // 直通模式，默认关闭，只在含数字信道模式下显示
        directMode: {
          bool,
          len: 1,
          offset: 6,
          default: false,
        },
      },
    },
    // 高低功率标志,静噪等级
    powerConfig: {
      type: 'u8',
      offset: 7,
      bits: {
        // 高低功率标志	0 低功率 1 高功率 2 中功率 默认值 1
        powerType: {
          len: 2,
          offset: 0,
        },
        // 静噪等级	[0,9] 默认值 3
        squelchLevel: {
          len: 4,
          offset: 2,
        },
      },
    },
    // 接收频率 单位Hz，小端存储
    rxFreq: {
      type: 'u32',
      offset: 8,
    },
    // 发射频率 单位Hz，小端存储
    txFreq: {
      type: 'u32',
      offset: 12,
    },
    // 模拟信道参数 / 数字信道参数 / 数模兼容信道参数	根据信道类型采用不同的数据结构
    subChannelData: {
      type: 'byteArray',
      len: 18,
      subTable,
      subProto: {
        dependOn: 'chType',
        0: digitalChannelProto,
        1: analogChannelProto,
        2: compatibleChannelProto,
        3: compatibleChannelProto,
      },
      offset: 16,
    },
    // 发射限时 取值范围 [1,33] 表示范围 [15,495]秒 间隔 15秒 默认值 300秒
    transmissionLimit: {
      type: 'u8',
      interval: 15,
      offset: 36,
    },
    // TOT密钥更新延迟	[0,255]秒
    totKeyUpdateDelay: {
      type: 'u8',
      offset: 37,
    },
    // 信道名称
    chName: {
      type: 'stringU16',
      len: 32,
      offset: 38,
    },
  },
}

function getChannelParamProto(chType = 0, version = 0) {
  switch (chType) {
    case 0:
      return digitalChannelProto[version]
    case 1:
      return analogChannelProto[version]
    default:
      return compatibleChannelProto[version]
  }
}

class Channel extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 26
    this.structLen = options.structLen || 70
  }

  getSubTableField() {
    const proto = this.getProtoInfo(this.type, this.version)
    if (!proto) {
      return undefined
    }

    for (const key in proto) {
      const item = proto[key]
      if (item === Unset || !item.subTable) {
        continue
      }

      return {
        ...item,
        __name: key,
      }
    }

    return undefined
  }

  decode(structData) {
    const res = super.decode(structData)
    const subTable = this.getSubTableField()
    const subTableName = subTable.__name
    let protoInfo, lastChType
    res.result = res.result.map((channel) => {
      if (subTableName) {
        if (lastChType !== channel.chType) {
          protoInfo = getChannelParamProto(channel.chType, this.version)
          lastChType = channel.chType
        }
        channel[subTableName] = unmarshalProto(channel[subTableName], protoInfo)
      }
      return channel
    })
    return res
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }
    const subTable = this.getSubTableField()
    const subTableName = subTable.__name
    let protoInfo, lastChType
    const result = data.map((channel) => {
      if (subTableName) {
        if (lastChType !== channel.chType) {
          protoInfo = getChannelParamProto(channel.chType, this.version)
          lastChType = channel.chType
        }
        channel[subTableName] = marshalProto(channel[subTableName], protoInfo, subTable.len)
      }
      return channel
    })

    return super.encode(result)
  }
}

// 扫描列表   最大数量: 32
const ScanProto = {
  0: {
    // 扫描组 ID
    scanId: {
      type: 'u8',
      offset: 0,
    },
    // 成员数量
    memberCount: {
      type: 'u8',
      offset: 1,
    },
    // 成员列表	信道ID索引  最大信道数: 16
    membersList: {
      type: 'u16',
      repeated: 16,
      offset: 2,
    },
    // 第一优先信道	信道 ID 索引
    priority1Ch: {
      type: 'u16',
      offset: 34,
    },
    // 第二优先信道	信道 ID 索引
    priority2Ch: {
      type: 'u16',
      offset: 36,
    },
    // 优先采样时间	 [500,7500] 间隔 250毫秒 默认值 2000 ms 0为无
    priSampleTime: {
      type: 'u8',
      interval: 250,
      offset: 38,
    },
    // 停留时间	[0.5-10]s 步进 0.5s 默认值 3s
    stayTime: {
      type: 'u8',
      offset: 39,
    },
    config: {
      type: 'u8',
      offset: 40,
      bits: {
        // 亚音扫描模式 0 不检测亚音 1 非优先信道检测 2 优先信道检测 3 所有信道检测(默认值)
        subScanMode: {
          offset: 0,
          len: 2,
        },
        // 回讲	0 不允许 1 允许
        reply: {
          bool,
          offset: 2,
          len: 1,
        },
        // 扫描发射模式	0 当前信道(默认值) 1 最后活动信道 2 指定信道
        scanTxMode: {
          offset: 3,
          len: 2,
        },
      },
    },
    // 指定的发送信道ID值 扫描发射模式==2时，此项有效
    appointTxCh: {
      type: 'u16',
      offset: 41,
    },
    // 名称
    name: {
      type: 'stringU16',
      len: 32,
      offset: 43,
    },
  },
}

class Scan extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 27
    this.structLen = options.structLen || 75
  }

  decode(structData) {
    const data = super.decode(structData)
    data.result = data.result.map((item) => {
      item.stayTime = Math.floor(item.stayTime / 2)
      return item
    })
    return data
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }

    const list = data.map((item) => {
      item.stayTime = Math.floor(item.stayTime * 2)
      return item
    })

    return super.encode(list)
  }
}

// 漫游列表  最大数量: 32
const RoamProto = {
  0: {
    // 扫描组 ID
    roamId: {
      type: 'u8',
      offset: 0,
    },
    config: {
      type: 'u8',
      offset: 1,
      bits: {
        // 主动站点漫游使能	0 禁用(默认值) 1 启用
        mainSiteRoaming: {
          bool,
          offset: 0,
          len: 1,
        },
        // 跟随主站点设置	0 禁用(默认值) 1 启用
        followMainSite: {
          bool,
          offset: 1,
          len: 1,
        },
        // 信道区域信道作为扫描列表	0 禁用(默认值) 1 启用 启用时列表不可选
        channelAsScanList: {
          bool,
          offset: 2,
          len: 1,
        },
      },
    },
    // 站点搜索计时器	[0,255]s 默认 60s 步进 1
    siteSearchTimer: {
      type: 'u8',
      offset: 2,
    },
    // RSSI阈值	[-120,-80] 间隔 1 绝对值传输
    rssi: {
      type: 'u8',
      offset: 3,
    },
    // 成员数量
    roamChCount: {
      type: 'u8',
      offset: 4,
    },
    // 成员列表	信道ID索引  最大信道数: 16
    roamChList: {
      type: 'u16',
      repeated: 16,
      offset: 5,
    },
    // 名称
    name: {
      type: 'stringU16',
      len: 32,
      offset: 37,
    },
  },
}

class Roam extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 28
    this.structLen = options.structLen || 69
  }

  decode(structData) {
    const data = super.decode(structData)
    data.result = data.result.map((item) => {
      // 解RSSI阈值绝对值
      item.rssi = item.rssi * -1
      return item
    })
    return data
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }

    const list = data.map((item) => {
      //  RSSI阈值，绝对值传输
      item.rssi = Math.abs(item.rssi)
      return item
    })

    return super.encode(list)
  }
}

// 巡逻系统
export class PatrolConfig extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 29
    this.structLen = options.structLen || 3
  }
}

// 紧急报警
export class EmergencyAlarm extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 31
    this.structLen = options.structLen || 6
  }
}

// 救援/求救配置
const SosProto = {
  0: {
    config: {
      type: 'u8',
      offset: 0,
      bits: {
        // 救援/求救使能， default=1
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 以下参数，enable为false时为不可选状态，设置时默认发送0
        // 间隔性鸣笛使能，默认0
        intervalHonking: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 闪烁手电筒使能，默认0
        flashlight: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // 发送卫星定位使能，默认0，隐藏
        sendGps: {
          len: 1,
          offset: 3,
          // bool,
          // default: false,
        },
      },
    },
    // 间隔性鸣笛时间，0=5秒，间隔5秒，范围5-60秒，默认0
    intervalHonkingTime: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 间隔性闪灯时间，0=500毫秒，间隔100毫秒，范围500-2000毫秒，默认1
    flashlightTime: {
      type: 'u8',
      offset: 2,
      default: 1,
    },
    // 救援扫描挂起时间，范围0-20s，间隔500ms，默认10(5s)
    rescueScanHandUpTime: {
      type: 'u8',
      offset: 3,
      default: 1,
      interval: 500,
    },
    // 电量高求救发送间隔，0=3分钟，间隔1分钟，范围3-10分钟，默认0
    highPowerSosTime: {
      type: 'u8',
      offset: 4,
      default: 0,
    },
    // 电量中求救发送间隔，0=5分钟，间隔1分钟，范围5-15分钟，默认0
    middlePowerSosTime: {
      type: 'u8',
      offset: 5,
      default: 0,
    },
    // 电量低求救发送间隔，0=10分钟，间隔1分钟，范围10-30分钟，默认0
    lowPowerSosTime: {
      type: 'u8',
      offset: 6,
      default: 0,
    },
    // 求救信息
    sosInfo: {
      type: 'stringU16',
      offset: 7,
      len: 48,
      default: '',
    },
  },
}

export class SosCfg extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 38
    this.structLen = options.structLen || 55
  }
}

// 救援/求救信道
// 1、求救/救援信道数据结构跟信道数据结构一致
// 2、求救/救援信道数据只在工厂写频软件可见、可编辑；客户写频软件不写入此数据
// 3、信道数据默认数字信道，只保留如下功能可编辑：彩色码、优先打断使能、时隙、虚拟机群发射时隙、语音优先级、直通模式
//    收发频率、发射功率、发射限时器、TOT更新延时、准许条件；其余参数不显示；
// 4、两信道默认频率分别为145.5MHz、435.5MHz;
export class SosChData extends Channel {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 39
    this.structLen = options.structLen || 70
  }
}

loadProtocol(Model, {
  // 设备信息
  1: cloneDeep(DeviceInfoProto),
  // 身份信息
  2: cloneDeep(IdentityInfoProto),
  // 通信密码
  3: cloneDeep(PasswordProto),
  // 总体设置
  4: cloneDeep(GeneralSettingsProto),
  // 按键设置
  5: cloneDeep(ButtonSettingsProto),
  // 短信
  6: cloneDeep(SmsProto),
  // 加密配置
  7: cloneDeep(EncryptConfigProto),
  // 加密列表
  8: cloneDeep(EncryptListProto),
  // 菜单配置
  14: cloneDeep(MenuSettingsProto),
  // 信令系统
  15: cloneDeep(SignalingSystemProto),
  // 警报配置
  16: cloneDeep(AlertConfigProto),
  // 数字警报
  18: cloneDeep(DigitalAlertProto),
  // 数字通讯录列表
  19: cloneDeep(AddressGroupProto),
  // 数字通讯录
  20: cloneDeep(DigitalAddressProto),
  // 电话通讯录
  21: cloneDeep(PhoneBookProto),
  // 接收组列表
  22: cloneDeep(RxGroupProto),
  // 1级区域
  23: cloneDeep(ZoneRootProto),
  // 2级区域
  24: cloneDeep(ZoneParentProto),
  // 区域
  25: cloneDeep(ZoneLeafProto),
  // 信道
  26: cloneDeep(ChannelProto),
  // 扫描列表
  27: cloneDeep(ScanProto),
  // 漫游列表
  28: cloneDeep(RoamProto),
  // 巡逻系统配置
  29: cloneDeep(PatrolConfigProto),
  // 紧急报警
  31: cloneDeep(EmergencyAlarmProto),
  // 救援/求救
  38: cloneDeep(SosProto),
  // 救援/求救信道
  39: cloneDeep(ChannelProto),
})

const StructIndex = {
  // 设备信息
  1: TD910DeviceInfo,
  // 身份信息, TD-910 机型的序列号采用二代序列号
  2: SecondIdentityInfo,
  // 通信密码
  3: Password,
  // 总体设置
  4: GeneralSettings,
  // 按键设置
  5: ButtonSettings,
  // 短信
  6: ShortMessage,
  // 加密配置
  7: EncryptSettings,
  // 加密列表
  8: EncryptList,
  // 菜单配置
  14: MenuSettings,
  // 信令系统
  15: SignalingSystem,
  // 警报配置
  16: AlertConfig,
  // 数字警报
  18: DigitalAlert,
  // 数字通讯录列表
  19: AddressGroup,
  // 数字通讯录
  20: DigitalAddress,
  // 电话通讯录
  21: PhoneBook,
  // 接收组列表
  22: RxGroup,
  // 1级区域
  23: ZoneRoot,
  // 2级区域
  24: ZoneParent,
  // 区域
  25: ZoneLeaf,
  // 信道
  26: Channel,
  // 扫描列表
  27: Scan,
  // 漫游列表
  28: Roam,
  // 巡逻系统配置
  29: PatrolConfig,
  // 紧急报警
  31: EmergencyAlarm,
  // 自动定位监控 32
  // 区域电子围栏监控 33
  // 岗哨监控 34
  // 移动监控 35
  // GPS巡更 36
  // 有源RFID 37
  // 救援/求救配置
  38: SosCfg,
  // 救援/求救信道
  39: SosChData,
  // 设备能力表 38
}

// 系统参数表ID及结构体定义
export const TableIndex = generateEnumObject({
  // 生产信息	生产相关的设备信息，包括 型号，频段，版本信息
  TD910DeviceInfo: 0x01,
  // 身份信息, TD-910 机型的序列号采用二代序列号
  SecondIdentityInfo: 0x02,
  // 通信密码
  Password: 0x03,
  // 常规设置
  GeneralSettings: 0x04,
  // 按键设置
  ButtonSettings: 0x05,
  // 预制短信
  ShortMessage: 0x06,
  // 加密配置
  EncryptSettings: 0x07,
  // 加密列表
  EncryptList: 0x08,
  // 卫星定位
  GpsSettings: 11,
  // 菜单配置
  MenuSettings: 14,
  // 信令系统
  SignalingSystem: 15,
  // 警报配置
  AlertConfig: 16,
  // 数字警报
  DigitalAlert: 18,
  // 数字通讯录列表
  AddressGroup: 19,
  // 数字通讯录
  DigitalAddress: 20,
  // 电话通讯录
  PhoneBook: 21,
  // 接收组列表
  RxGroup: 22,
  // 1级区域
  ZoneRoot: 23,
  // 2级区域
  ZoneParent: 24,
  // 区域
  ZoneLeaf: 25,
  // 信道
  Channel: 26,
  // 扫描列表
  Scan: 27,
  // 漫游列表
  Roam: 28,
  // 巡逻系统配置
  PatrolConfig: 29,
  // 紧急报警
  EmergencyAlarm: 31,
  // 自动定位监控 32
  // 区域电子围栏监控 33
  // 岗哨监控 34
  // 移动监控 35
  // GPS巡更 36
  // 有源RFID 37
  // 救援/求救配置
  SosCfg: 38,
  // 救援/求救信道
  SosChData: 39,
})

export function getClassInstance(options) {
  const opts = {
    ...BaseOptions,
    ...options,
  }
  const DataClass = StructIndex[opts.type]
  if (!DataClass) {
    return null
  }

  return new DataClass(opts)
}
