import {
  cloneDeep,
} from 'lodash'
import {
  getModelName,
} from '../modelInfo'
import {
  bool,
  DeviceBase,
  DeviceInfo,
  DeviceInfoProto, EmergencyAlarmProto,
  generateEnumObject,
  IdentityInfoProto,
  loadProtocol,
  Password,
  PasswordProto, PatrolConfigProto,
  PhoneBookProto,
  SecondIdentityInfo,
} from './common'
import {
  BP610Model,
} from './models'

export const Model = BP610Model
const BaseOptions = {
  model: Model,
  version: 0,
  // structLen: 0,
}

function covertBitToBoolean(val, radix = 0) {
  return ((val >> radix) & 0x01) === 1
}

export class BP610DeviceInfo extends DeviceInfo {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 1
    this.structLen = options.structLen || 16
  }

  // 机型解析， 最后2字节前4位为机型拥有的功能表。基础数值0x30
  // 高字节: bit0:漫游功能，bit1:蓝牙功能，bit2:降噪功能
  // 低字节: bit0: 定位功能，bit1: 录音功能，bit2: 倒地报警， bit3: 单独工作
  // bit4: SDC, bit5: SVT
  // 根据选配的功能形成机型号码
  decodeModel(model) {
    const byte1 = model.charCodeAt(6) & 0xff // 高字节
    const byte2 = model.charCodeAt(7) & 0xff // 低字节
    return {
      roam: covertBitToBoolean(byte1, 0),
      bluetooth: covertBitToBoolean(byte1, 1),
      denoise: covertBitToBoolean(byte1, 2),

      locate: covertBitToBoolean(byte2, 0),
      recording: covertBitToBoolean(byte2, 1),
      upendAlarm: covertBitToBoolean(byte2, 2),
      workAlone: covertBitToBoolean(byte2, 3),
      sdc: covertBitToBoolean(byte2, 4),
      svt: covertBitToBoolean(byte2, 5),
    }
  }

  decode(structData) {
    const decodeData = super.decode(structData)
    const config = decodeData.result[0]
    if (config) {
      decodeData.result[0].config = this.decodeModel(config.model)
      config.modelName = getModelName(config.model)
      decodeData.result[0] = config
    }

    return decodeData
  }
}

// 总体设置
const GeneralSettingsProto = {
  0: {
    // ch: '设备名称',
    deviceName: {
      type: 'stringU16',
      len: 32,
    },
    // ch: '设备ID',
    ids: {
      type: 'u32',
      offset: 32,
    },
    // ch: '中继id',
    repeaterId: {
      type: 'u32',
      offset: 36,
    },
    // ch: '发射前导码持续时间',范围(0--36),步进240
    sendPreLastTime: {
      type: 'u8',
      offset: 40,
      interval: 240,
    },
    // ch: '组呼挂起时间',范围(0--14),步进500
    offlineGroupCallHungTime: {
      type: 'u8',
      offset: 41,
      interval: 500,
    },
    // ch: '单呼挂起时间',范围(0--14),步进500
    offlineSingleCallHungTime: {
      type: 'u8',
      offset: 42,
      interval: 500,
    },
    // ch: '语言环境，中文[0]、英文[1]，默认:中文[0]
    locale: {
      type: 'u8',
      offset: 43,
    },
    // 基础信息：44
    baseSettings: {
      type: 'u8',
      offset: 44,
      bits: {
        // ch: '拒绝陌生呼叫',
        rejectUnfamiliarCall: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
        // 是否允许写频擦除，default = 0
        allowErasing: {
          len: 1,
          bool,
          offset: 1,
          default: false,
        },
        // 自动添加陌生人到 通讯录  使能  0不允许 1允许 ，默认0
        allowAddUnfamiliar: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
        // 是否允许删除全部录音文件， 0不允许 1允许 default = 0
        allowDeleteAllRecord: {
          len: 1,
          bool,
          offset: 3,
          default: false,
        },
      },
    },
    // 省电模式: 0:省电关闭，1:省电比例1:1，2:省电比例1:2，3:省电比例1:3，4:省电比例1:4。默认值：2。
    savePowerMode: {
      type: 'u8',
      offset: 45,
    },
    // 省电延迟开启时间: 范围5~60秒，步进1。默认值：5。
    savePowerDelayTime: {
      type: 'u8',
      offset: 46,
      default: 5,
      interval: 1,
    },
    // LED相关
    LEDConfig: {
      type: 'u8',
      offset: 47,
      bits: {
        // LED禁用标记，0未选择，1选中
        disabledAllLED: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
      },
    },
    // 声音相关
    soundAndDisplayTip: {
      type: 'u8',
      offset: 48,
      bits: {
        // 全部禁音，0未选择，1选中
        muteAll: {
          len: 1,
          bool,
          offset: 0,
        },
        // 语音提示，0未选择，1选中
        voiceNotice: {
          len: 1,
          bool,
          offset: 1,
        },
        // 信道空闲指示，0未选择，1选中
        channelFreeNotice: {
          len: 1,
          bool,
          offset: 2,
        },
        // 呼叫允许指示 000无,001 模拟,010模拟和数字,011数字
        allowCallInstruction: {
          len: 3,
          offset: 3,
        },
        // 模拟发射信令侧音开关，0-关，1-开
        sendSignalSideSound: {
          len: 1,
          bool,
          offset: 6,
        },
        // 成功解码信号后是否发出提示音开关，0-关，1-开
        decodeSignalSound: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // ch: '接收低电池电量提示间隔',[0,635] 间隔 5, 默认120
    powerInfoAlert: {
      type: 'u8',
      interval: 5,
      offset: 49,
      default: 120,
    },
    // '声控等级', [关,1-9] 默认值 关
    soundCtrlLevel: {
      type: 'u8',
      offset: 50,
    },
    // ch: '声控延迟',[500,10000]  默认值 500 ms 间隔 500
    soundCtrlDelay: {
      type: 'u8',
      interval: 500,
      offset: 51,
    },
    // 隐蔽功能相关
    stealthSettings: {
      type: 'u8',
      offset: 52,
      bits: {
        // 隐蔽模式使能，default = 0
        stealthModeEnable: {
          len: 1,
          bool,
          offset: 0,
        },
        // 隐蔽模式耳麦是否静音，default = 0
        stealthModeHeadsetMute: {
          len: 1,
          bool,
          offset: 1,
        },
      },
    },
    // 时间功能相关, 以下参数用作设置时间
    // ch: 时区小时 [-12,12] 默认值 8
    timeZoneHour: {
      type: 'int8',
      offset: 53,
    },
    // ch: 时区分钟 [0,59]  默认值 0
    timeZoneMinute: {
      type: 'u8',
      offset: 54,
    },
    // ch: 年
    year: {
      type: 'u16',
      offset: 55,
    },
    // ch: 月
    month: {
      type: 'u16',
      offset: 57,
    },
    // ch: 日
    day: {
      type: 'u16',
      offset: 59,
    },
    // ch: 时
    hour: {
      type: 'u16',
      offset: 61,
    },
    // ch: 分
    minute: {
      type: 'u16',
      offset: 63,
    },
    // ch: 秒
    second: {
      type: 'u16',
      offset: 65,
    },
    // 定位模式选择，当设备带有GPS模块时有效
    gpsSettings: {
      type: 'u8',
      offset: 67,
      bits: {
        // GPS，默认1
        gpsEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 北斗，默认1
        beiDouEnable: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 格洛纳斯，默认0
        glonassEnable: {
          len: 1,
          bool,
          offset: 2,
        },
        // 伽利略，默认0
        galileoEnable: {
          len: 1,
          bool,
          offset: 3,
        },
        // GPS模块波特率，0为9600，1为115200，默认0
        gpsBaudRate: {
          len: 1,
          offset: 4,
        },
      },
    },
    // 录音功能相关
    recordSettings: {
      type: 'u8',
      offset: 68,
      bits: {
        // ch: '录音使能',默认值 0
        enable: {
          len: 1,
          bool,
          offset: 0,
        },
      },
    },
    // 蓝牙相关
    bluetoothSettings: {
      type: 'u8',
      offset: 69,
      bits: {
        // 蓝牙使能，default = 0;
        enable: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
        // 蓝牙PTT保持使能，default = 0
        pttKeepEnable: {
          len: 1,
          bool,
          offset: 1,
          default: false,
        },
      },
    },
    // 降噪相关
    denoiseSettings: {
      type: 'u8',
      offset: 70,
      bits: {
        // 降噪使能，default = 0;
        enable: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
      },
    },
    // 别名相关
    // 别名
    alias: {
      type: 'stringU16',
      len: 32,
      offset: 71,
    },
  },
}

class GeneralSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 4
    this.structLen = options.structLen || 103
  }
}

// 按键设置
export const ButtonKeys = generateEnumObject({
  // 未定义软按键功能
  NONE: 0,
  // 单键功能呼叫1
  SPDCALL1: 1,
  // 单键功能呼叫2
  SPDCALL2: 2,
  // 单键功能呼叫3
  SPDCALL3: 3,
  // 单键功能呼叫4
  SPDCALL4: 4,
  // 单键功能呼叫5
  SPDCALL5: 5,
  // 单键功能呼叫6
  SPDCALL6: 6,
  // 预设信道1
  CH_PRESET1: 7,
  // 预设信道2
  CH_PRESET2: 8,
  // 预设信道3
  CH_PRESET3: 9,
  // 预设信道4
  CH_PRESET4: 10,
  // 紧急模式开启
  WARNING_ON: 11,
  // 紧急模式关闭
  WARNING_OFF: 12,
  // 监听
  MONI: 13,
  // 永久监听
  LONGMONI: 14,
  // 键盘锁
  KEYLOCK: 15,
  // 电池电量提示
  BATTERY_CHANGE: 16,
  // 当前日期提示
  // DATE_TIME: 17,
  // 区域切换
  ZONE_SWITCH: 18,
  // 信道上调
  // CH_UP: 19,
  // 信道下调
  // CH_DOWN: 20,
  // 音量上调
  // VOLUME_UP: 21,
  // 音量下调
  // VOLUMN_DOWN: 22,
  // 优先打断
  INT_PRI: 23,
  // 数字模拟切换
  A_D_SWITCH: 24,
  // 功率切换
  PWRMODE_CHG_BP660: 25,
  // 中继/脱网
  NETMODE_CHG: 26,
  // 无用信道删除
  DEL_INVALID_CHL: 27,
  // 对讲机激活
  // DEV_ACTIVE: 28,
  // 对讲机遥毙
  // DEV_DIE: 29,
  // 对讲机检测
  // DEV_DETECT: 30,
  // 远程监听
  // RMT_MONITOR: 31,
  // 背光灯自动开/关
  // BACKLIGNT_SWITCH: 32,
  // 强光手电开/关
  // GLARE_FLASHLIGHT: 33,
  // 所有提示音开关
  TONE_MUTE_SWITCH: 34,
  // 扫描开/关
  SCAN_SWITCH: 35,
  // 声控开关
  VOX_SWITCH: 36,
  // GPS开关
  GPS_POSITION_SWITCH: 37,
  // 录音开关
  RECORD_SWITCH: 38,
  // 单独工作开关
  WORK_ALONE_SWITCH: 39,
  // 倒放开关
  WORK_DOWN_SWITCH: 40,
  // 漫游开关
  ROAM_SWITCH: 41,
  // 系统状态查询
  SystemStatusQuery: 42,
  // 站点锁定开关，支持漫游功能设备显示
  SITE_LOCK_SWITCH: 43,
  // 手动站点漫游，支持漫游功能设备显示
  ROAM_MANUAL: 44,
  // 隐蔽模式开/关
  HIDE_SWITCH: 45,
  // 加密开/关
  SK_ENCRYPTION_SWITCH: 46,
  // 蓝牙开/关
  SK_BT_SWITCH: 47,
  // 蓝牙搜索并自动连接
  SK_BT_SEARCH_AUTO_CONNECT: 48,
  // 信道锁定开/关
  SK_CH_LOCK: 49,
  // 降噪开/关
  SK_DNS_SWITCH: 50,
  // 禁用所有LED
  SK_DISABLED_ALL_LED: 51,

  /* 界面相关 */
  // 手动拨号界面
  // SCALL_DIAL_MENU: 64,
  // 联系人列表
  // CONTAS_LIST_MENU: 65,
  // 常用联系人列表
  // TOP_CONTAS_LIST_MENU: 66,
  // 电话联系人列表
  // PHONE_LIST_MENU: 67,
  // DTMF键盘
  // DTMF_MENU: 68,
  // 短信
  // MSG_MENU: 69,
  // 打开主菜单 确认键的默认功能
  // MAIN_MENU: 70,
  // 返回主界面 P2键的默认功能
  // BACKTOHOME: 71,

  /* 以下选项紧限于生产调试 */
  // // 误码率测试接收
  // ERRNUMBER_RX: 128,
  // // 误码率测试发射
  // ERRNUMBER_TX: 129,
  // // 基带固件升级
  // BASEBAND_FIRMWARE: 130,
  // // 发射测试
  // TX_TEST: 131,
})
// 单键呼叫类型定义
export const SoftKeyCallType = generateEnumObject({
  SINGLE: 0,		// 单呼
  GROUP: 1,			// 组呼
  TIP: 2,			// 呼叫提示
  MSG: 3, 			// 短信
})

// 按键定义
export const ButtonSettingsProto = {
  0: {
    // ch: '长按持续时间' [250,3750] 间隔 250
    longPressTime: {
      type: 'u8',
      interval: 250,
      offset: 0,
    },
    // ch: '侧键 短按/长按', 名字为:按键 S1、按键 S2、按键 P1、按键 P2
    sideKey: {
      type: 'byteArray',
      offset: 1,
      len: 2, // subFields协议字段的实际长度
      repeated: 4,
      subFields: {
        short: {
          type: 'u8',
          offset: 0,
        },
        long: {
          type: 'u8',
          offset: 1,
        },
      },
    },
    //新增正面6键快捷功能定义
    frontKey: {
      type: 'byteArray',
      offset: 9,
      len: 2,
      repeated: 6,
      subFields: {
        short: {
          type: 'u8',
          offset: 0,
        },
        long: {
          type: 'u8',
          offset: 1,
        },
      },
    },
    // ch: '单键呼叫'
    singleKeyCall: {
      type: 'byteArray',
      offset: 21,
      len: 5,
      repeated: 6,
      subFields: {
        // 呼叫模式: 0-数字，1-模拟，0XFF--无
        callMode: {
          type: 'u8',
          offset: 0,
        },
        // 呼叫ID	通讯录索引
        callId: {
          type: 'u16',
          offset: 1,
        },
        // 呼叫类型
        callType: {
          type: 'u8',
          offset: 3,
        },
        // 短信ID	短信索引
        smsId: {
          type: 'u8',
          offset: 4,
        },
      },
    },
    // 长按数字键盘0~9对应呼叫功能表定义
    longPressNumCallTable: {
      type: 'byteArray',
      offset: 51,
      len: 5,
      repeated: 10,
      subFields: {
        // 呼叫模式: 0-数字，1-模拟，0XFF--无
        callMode: {
          type: 'u8',
          offset: 0,
        },
        // 呼叫ID	通讯录索引
        callId: {
          type: 'u16',
          offset: 1,
        },
        // 呼叫类型
        callType: {
          type: 'u8',
          offset: 3,
        },
        // 短信ID	短信索引
        smsId: {
          type: 'u8',
          offset: 4,
        },
      },
    },
    // 预设信道
    defaultChannel: {
      type: 'byteArray',
      offset: 101,
      len: 2,
      repeated: 4,
      subFields: {
        // 信道ID 65535表示空
        channelId: {
          type: 'u16',
          offset: 0,
        },
      },
    },
  },
}

class ButtonSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 5
    this.structLen = options.structLen || 109
  }
}

// 短信 最大数量: 100
const ShortMessageProto = {
  0: {
    // ch: '短信id',
    msgId: {
      type: 'u8',
    },
    // ch: '短信内容',
    msgContent: {
      type: 'stringU16',
      len: 280,
      offset: 1,
    },
  },
}

class ShortMessage extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 6
    this.structLen = options.structLen || 281
  }
}

// 加密配置
const EncryptSettingsProto = {
  0: {
    config: {
      type: 'u8',
      offset: 0,
      bits: {
        // 加密使能
        encryptEnable: {
          offset: 0,
          bool,
          len: 1,
        },
      },
    },
  },
}

class EncryptSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 7
    this.structLen = options.structLen || 1
  }
}

//基础密钥列表 最大数量: 32
const EncryptListProto = {
  0: {
    // 列表序号
    index: {
      type: 'u8',
      offset: 0,
    },
    // 密钥ID
    id: {
      type: 'u8',
      offset: 1,
    },
    // 密钥值,所有可见字符
    value: {
      type: 'string',
      offset: 2,
      len: 10,
    },
    // 密钥名称
    name: {
      type: 'stringU16',
      offset: 12,
      len: 32,
    },
  },
}

class EncryptList extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 8
    this.structLen = options.structLen || 44
  }
}

// ARC4密钥列表 最大数量: 32
const ARC4ListProto = {
  // AES256密钥列表 最大数量: 32
  0: {
    // 列表序号
    index: {
      type: 'u8',
      offset: 0,
    },
    // 密钥ID
    id: {
      type: 'u8',
      offset: 1,
    },
    // 密钥值,0~9,A~F
    value: {
      type: 'string',
      offset: 2,
      len: 10,
    },
    // 密钥名称
    name: {
      type: 'stringU16',
      offset: 12,
      len: 32,
    },
  },
}

class ARC4List extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 9
    this.structLen = options.structLen || 44
  }
}

// AES256密钥列表 最大数量: 32
const AES256ListProto = {
  0: {
    // 列表序号
    index: {
      type: 'u8',
      offset: 0,
    },
    // 密钥ID
    id: {
      type: 'u8',
      offset: 1,
    },
    // 密钥值,0~9,A~F
    value: {
      type: 'string',
      offset: 2,
      len: 64,
    },
    // 密钥名称
    name: {
      type: 'stringU16',
      offset: 66,
      len: 32,
    },
  },
}

class AES256List extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 10
    this.structLen = options.structLen || 98
  }
}

// GPS设置
const GpsSettingsProto = {
  0: {
    baseConfig: {
      type: 'u8',
      offset: 0,
      bits: {
        // GPS开关 0:关, 1:开
        enable: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
        // GPS工作模式 0:省电模式, 1:高性能模式
        mode: {
          len: 1,
          offset: 1,
          default: 1,
        },
      },
    },
    // 控制中心ID.
    centerId: {
      type: 'u32',
      offset: 1,
    },
    // 连接次数. 0-255次.
    connectionCount: {
      type: 'u8',
      offset: 5,
    },
    // PTT次数.0-255次.
    pttCount: {
      type: 'u8',
      offset: 6,
    },
    // 查询命令,需包含截止符,默认为空,不允许查询
    queryCmd: {
      type: 'string',
      len: 25,
      offset: 7,
    },
  },
}

class GpsSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 13
    this.structLen = options.structLen || 32
  }
}

// 菜单配置
const MenuSettingsProto = {
  0: {
    // ch: '菜单挂起时间',[0,30] 默认值 30s
    hangTime: {
      type: 'u8',
      offset: 0,
      default: 10,
    },
    // 液晶背光挂起时间。 0表示关闭。最大30.单位秒 default = 10
    lcdHangTime: {
      type: 'u8',
      offset: 1,
      default: 10,
    },
    // 键盘背光挂起时间。 0表示关闭，最大30.单位秒 default = 5
    keyboardHangTime: {
      type: 'u8',
      offset: 2,
      default: 5,
    },
    baseSetting: {
      type: 'u8',
      offset: 3,
      bits: {
        // 菜单键关闭 0 禁用 1 可用
        menuOff: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
        // 信道显示模式 0 频率显示 1 信道显示 2 频率+信道
        chDisplayMode: {
          len: 2,
          offset: 1,
          default: 2,
        },
        // 呼叫显示模式，0显示名字，1显示号码，2显示名字或别名，默认2
        callDisplayMode: {
          len: 2,
          offset: 3,
          default: 2,
        },
        // 数字键快捷拨号，0未选择，1选中，默认0
        numberKeyFastDial: {
          len: 1,
          bool,
          offset: 5,
          default: false,
        },
        // 呼叫方位信息，0未选择，1选中，默认0
        callDirectionEnable: {
          len: 1,
          bool,
          offset: 6,
          default: false,
        },
      },
    },
    baseConfig: {
      type: 'u8',
      offset: 4,
      bits: {
        // ID最小显示位数。 范围0~7，显示1~8 default = 0
        idShownMinLen: {
          len: 3,
          offset: 0,
          default: 0,
        },
        // 开机界面显示，0不显示1显示，默认1
        bBootDisp: {
          len: 1,
          bool,
          offset: 3,
          default: 1,
        },
        // 手动快捷呼叫 呼叫方式：默认单呼0、默认组呼1、仅组呼2、仅单呼3；默认0
        manualQuickCallType: {
          len: 2,
          offset: 4,
          default: 0,
        },
        // 别名显示 0 显示本地名称 1 没有联系人的情况下显示别名，默认0
        AliasDisp: {
          offset: 6,
          len: 1,
          bool,
          default: 0,
        },
      },
    },
    // 开机密码
    powerOnPwd: {
      type: 'password',
      offset: 5,
      len: 6,
    },
    // 信道配置密码
    chConfigPwd: {
      type: 'password',
      offset: 11,
      len: 6,
    },
    // 通信录
    contactConfig: {
      type: 'u8',
      offset: 17,
      bits: {
        // 通信录	0 禁用 1 可用 默认值 1
        contacts: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 联系人列表
        contactList: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 联系人群组 1 Enable, 0 Disable. default = 1
        contactGroup: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 手动拨号
        manualDialing: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 新建联系人 1 Enable, 0 Disable. default = 1
        newContact: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 群组管理 1 Enable, 0 Disable. default = 1
        groupManagement: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
      },
    },
    // 联系人相关操作
    addressSetting: {
      type: 'u8',
      offset: 18,
      bits: {
        // 联系人编辑 1 Enable, 0 Disable. default = 1
        editEnable: {
          len: 1,
          offset: 0,
          default: true,
          bool,
        },
        // 联系人删除 1 Enable, 0 Disable. default = 1
        deleteEnable: {
          len: 1,
          offset: 1,
          default: true,
          bool,
        },
        // 加入或退出群组 1 Enable, 0 Disable. default = 1
        groupJoinOrExit: {
          len: 1,
          offset: 2,
          default: true,
          bool,
        },
        // 发送短信 1 Enable, 0 Disable. default = 1
        sendSms: {
          len: 1,
          offset: 3,
          default: true,
          bool,
        },
        // 呼叫提示 1 Enable, 0 Disable. default = 1
        callTip: {
          len: 1,
          offset: 4,
          default: true,
          bool,
        },
        // 对讲机检测 1 Enable, 0 Disable. default = 0
        deviceDetect: {
          len: 1,
          offset: 5,
          default: false,
          bool,
        },
        // 远程监听 1 Enable, 0 Disable. default = 0
        remoteMonitor: {
          len: 1,
          offset: 6,
          default: false,
          bool,
        },
        // 对讲机激活 1 Enable, 0 Disable. default = 0
        deviceActive: {
          len: 1,
          offset: 7,
          default: false,
          bool,
        },
      },
    },
    // 对讲机控制
    deviceControl: {
      type: 'u8',
      offset: 19,
      bits: {
        // 对讲机遥毙 1 Enable, 0 Disable. default = 0
        deviceRemoteDeath: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
      },
    },
    // 手动拨号、新建联系人、群组管理相关操作
    contactGroup: {
      type: 'u8',
      offset: 20,
      bits: {
        // 单呼呼叫 1 Enable, 0 Disable. default = 1
        singleDial: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 组呼呼叫 1 Enable, 0 Disable. default = 1
        groupDial: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 新建单呼联系人 1 Enable, 0 Disable. default = 1
        newSingleContact: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 新建组呼联系人 1 Enable, 0 Disable. default = 1
        newGroupContact: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 新建群组 1 Enable, 0 Disable. default = 1
        newGroup: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 删除群组 1 Enable, 0 Disable. default = 1
        deleteGroup: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 编辑群组 1 Enable, 0 Disable. default = 1
        editGroup: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
      },
    },
    // 电话本
    phoneConfig: {
      type: 'u8',
      offset: 21,
      bits: {
        // 电话本 1 Enable, 0 Disable. default = 1
        enable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 联系人列表 1 Enable, 0 Disable. default = 1
        phoneList: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 手动拨号 1 Enable, 0 Disable. default = 1
        manualDial: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 新建联系人 1 Enable, 0 Disable. default = 1
        newContact: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 联系人编辑 1 Enable, 0 Disable. default = 1
        editContact: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 联系人删除 1 Enable, 0 Disable. default = 1
        deleteContact: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
      },
    },
    // 区域
    zoneConfig: {
      type: 'u8',
      offset: 22,
      bits: {
        // 区域  1 Enable.  0 Disable. default = 1
        enable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
      },
    },
    // 扫描
    scanConfig: {
      type: 'u8',
      offset: 23,
      bits: {
        // 扫描 1 Enable, 0 Disable. default = 1
        menuEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 扫描开关 1 Enable, 0 Disable. default = 1
        scanEnable: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 编辑扫描列表 1 Enable, 0 Disable. default = 1
        editScanList: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
      },
    },
    // 漫游
    roamConfig: {
      type: 'u8',
      offset: 24,
      bits: {
        // 漫游 1 Enable, 0 Disable. default = 1
        menuEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 漫游开关 1 Enable, 0 Disable. default = 1
        roamEnable: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 编辑漫游列表 1 Enable, 0 Disable. default = 1
        editRoamList: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 锁定站点 1 Enable, 0 Disable. default = 1
        lockSite: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 手动站点漫游 1 Enable, 0 Disable. default = 1
        manualRoam: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
      },
    },
    // 短信
    smsConfig: {
      type: 'u8',
      offset: 25,
      bits: {
        // 短信  1 Enable.  0 Disable. default = 1
        enable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 新建短信  1 Enable.  0 Disable. default = 1
        newSms: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 预制短信  1 Enable.  0 Disable. default = 1
        presetSms: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 收件箱  1 Enable.  0 Disable. default = 1
        receiveBox: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 发件箱  1 Enable.  0 Disable. default = 1
        sendBox: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 短信清空  1 Enable.  0 Disable. default = 1
        clearSms: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
      },
    },
    // 短信相关操作
    smsOperation: {
      type: 'u8',
      offset: 26,
      bits: {
        // 回复  1 Enable.  0 Disable. default = 1
        reply: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 转发  1 Enable.  0 Disable. default = 1
        forward: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 重发  1 Enable.  0 Disable. default = 1
        resend: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 删除  1 Enable.  0 Disable. default = 1
        delete: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
      },
    },
    // 呼叫记录
    callConfig: {
      type: 'u8',
      offset: 27,
      bits: {
        // 呼叫记录 1 Enable, 0 Disable. default = 1
        callRecord: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 未接呼叫 1 Enable, 0 Disable. default = 1
        unReceivedCall: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 已接呼叫 1 Enable, 0 Disable. default = 1
        receivedCall: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 呼出呼叫 1 Enable, 0 Disable. default = 1
        callOut: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 记录清空  1 Enable.  0 Disable. default = 1
        clearCallRecord: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
      },
    },
    // 蓝牙
    bluetoothConfig: {
      type: 'u8',
      offset: 28,
      bits: {
        // 蓝牙 1 Enable, 0 Disable. default = 1
        enable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
      },
    },
    // 录音
    recordConfig: {
      type: 'u8',
      offset: 29,
      bits: {
        // 录音 1 Enable, 0 Disable. default = 1
        menuEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 录音开关 1 Enable, 0 Disable. default = 0
        recordEnable: {
          len: 1,
          bool,
          offset: 1,
          default: false,
        },
        // 录音文件 1 Enable, 0 Disable. default = 0
        recordFile: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
      },
    },
    // 设置
    deviceConfig: {
      type: 'u8',
      offset: 30,
      bits: {
        // 设置 1 Enable, 0 Disable. default = 1
        menuEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 对讲机设置 1 Enable, 0 Disable. default = 1
        deviceSetting: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 脱网 1 Enable, 0 Disable. default = 1
        offline: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 音调/提示 1 Enable, 0 Disable. default = 1
        toneTip: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 发射功率 1 Enable, 0 Disable. default = 1
        transmitPower: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 背光 1 Enable, 0 Disable. default = 1
        backLight: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 开机界面 1 Enable, 0 Disable. default = 1
        bootInterface: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
        // 键盘锁 1 Enable, 0 Disable. default = 1
        keyboardLock: {
          len: 1,
          bool,
          offset: 7,
          default: true,
        },
      },
    },
    // LED 指示灯,静噪,开机密码,语言环境,声控,时间设置,定位,单独工作
    deviceConfig2: {
      type: 'u8',
      offset: 31,
      bits: {
        // LED指示灯 1 Enable, 0 Disable. default = 1
        ledIndicator: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 静噪 1 Enable, 0 Disable. default = 1
        quieting: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 开机密码 1 Enable, 0 Disable. default = 0
        powerOnPassword: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
        // 语言环境 1 Enable, 0 Disable. default = 1
        locale: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 声控 1 Enable, 0 Disable. default = 1
        soundCtrl: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 时间设置 1 Enable, 0 Disable. default = 1
        timeSetting: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 定位 1 Enable, 0 Disable. default = 0
        locate: {
          len: 1,
          bool,
          offset: 6,
          default: false,
        },
        // 单独工作 1 Enable, 0 Disable. default = 0
        workAlone: {
          len: 1,
          bool,
          offset: 7,
          default: false,
        },
      },
    },
    // 对讲机信息
    deviceConfig3: {
      type: 'u8',
      offset: 32,
      bits: {
        // 倒放 1 Enable, 0 Disable. default = 0
        upendEnable: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
        // 隐蔽模式 1 Enable, 0 Disable. default = 1
        hideMode: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 加密  1 Enable.  0 Disable. default = 1
        encryptEnable: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 降噪  1 Enable.  0 Disable. default = 1
        denoiseEnable: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 信道锁定  1 Enable.  0 Disable. default = 1
        channelLockEnable: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 别名菜单 开关使能 ，0不选择1选择 ，默认1
        aliasEnable: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
      },
    },
    // 对讲机信息
    deviceInfo: {
      type: 'u8',
      offset: 33,
      bits: {
        // 对讲机信息 1 Enable, 0 Disable. default = 1
        infoMenuEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
      },
    },
    // 信道配置
    channelSetting: {
      type: 'u8',
      offset: 34,
      bits: {
        // 信道配置总开关，1 Enable, 0 Disable. default = 1
        channelConfigMenuEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 接收频率，1 Enable, 0 Disable. default = 1
        receivingFreq: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 发射频率，1 Enable, 0 Disable. default = 1
        transmittingFreq: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 信道名称，1 Enable, 0 Disable. default = 1
        channelName: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 发射限时，1 Enable, 0 Disable. default = 1
        transmitTimeLimit: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 亚音频设置，1 Enable, 0 Disable. default = 1
        subAudioSetting: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 发射联系人，1 Enable, 0 Disable. default = 1
        launchContact: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
        // 彩色码，1 Enable, 0 Disable. default = 1
        colorCode: {
          len: 1,
          bool,
          offset: 7,
          default: true,
        },
      },
    },
    // 时隙
    timeSlotSetting: {
      type: 'u8',
      offset: 35,
      bits: {
        // ch: '时隙',
        timeSlot: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // ch: '虚拟集群发射时隙',
        virtualClusterTimeSlot: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // ch: '接收列表',
        receivingList: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
      },
    },
  },
}

class MenuSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 15
    this.structLen = options.structLen || 36
  }
}

// 信令系统
const SignalingSystemProto = {
  0: {
    remoteConfig: {
      type: 'u8',
      offset: 0,
      bits: {
        // 对讲机遥毙/唤醒解码 1-允许，0-不允许。default = 1
        remoteShutDecodeEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 远程监听解码 1-允许，0-不允许。default = 0
        remoteMonitorDecode: {
          len: 1,
          bool,
          offset: 1,
          default: false,
        },
        // 紧急远程监听解码。1-允许，0-不允许。当远程监听解码允许该值固定为允许
        urgentRemoteMonitorDecode: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
        // 远程提示解码 1-允许，0-不允许。default = 1
        remoteNoticeDecode: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 远程检测解码 1-允许，0-不允许。default = 1
        remoteDetectDecode: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 远程销毁解码 1-允许，0-不允许。default = 1
        remoteEraseDecode: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 远程遥晕/遥醒解码 1-允许，0-不允许。default = 1
        remoteStunWakeupDecode: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
      },
    },
    // 远程监听持续时间(秒)。 范围:10 ~ 120 , 步长: 10
    remoteMonitorDuration: {
      type: 'u8',
      // interval: 10,
      offset: 1,
      default: 10,
    },
    // 远程提示次数。 范围:0 ~ 255, 255表示无限，默认10
    remoteAlertCount: {
      type: 'u8',
      offset: 2,
      default: 10,
    },
    // 信令空口加密相关
    signalingEncrypt: {
      type: 'u8',
      offset: 3,
      bits: {
        // 对讲机遥毙/唤醒空口加密使能 1-使能，0-不使能。default = 0
        remoteShutEncryptEnable: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
        // 远程监听空口加密使能 1-使能，0-不使能。default = 0
        remoteMonitorDecodeEnable: {
          len: 1,
          bool,
          offset: 1,
          default: false,
        },
      },
    },
    // 空口密钥，0~9，A~F，不足补F
    signalingPwd: {
      type: 'password',
      len: 16,
      offset: 4,
    },
  },
}

class SignalingSystem extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 16
    this.structLen = options.structLen || 20
  }
}

// 警报配置
const AlertConfigProto = {
  0: {
    // 单独工作
    // 单独工作使能，default = 0 以下参数当此使能=0时不可用
    aloneWorkEnable: {
      type: 'u8',
      bool,
      offset: 0,
      default: false,
    },
    // 单独工作响应时间，单位分钟，步进1，范围1-255分，默认10分；
    responseTime: {
      type: 'u8',
      offset: 1,
      default: 10,
      interval: 1,
    },
    // 单独工作提醒时间，单位秒，步进1，范围0-255秒，默认10秒；
    remindTime: {
      type: 'u8',
      offset: 2,
      default: 10,
      interval: 1,
    },
    // 单独工作响应操作，0：按键，1：语音发射；
    responseOperation: {
      type: 'u8',
      offset: 3,
    },

    // 倒放
    // 倒放使能 以下参数当此使能位=0时不可用 默认值 0
    upendEnable: {
      type: 'u8',
      bool,
      offset: 4,
      default: false,
    },
    // 进入延时，单位秒，步进1，范围5-255秒，默认10秒；
    entryDelay: {
      type: 'u8',
      offset: 5,
      default: 10,
      interval: 1,
    },
    // 退出延时，单位秒，步进1，范围0-254秒、无限(255)，默认10秒；
    quitDelay: {
      type: 'u8',
      offset: 6,
      default: 10,
      interval: 1,
    },
    upendConfig: {
      type: 'u8',
      offset: 7,
      bits: {
        // 倒放预提示时间，单位秒，步进1，范围0-10秒，默认5秒;
        preRewindTime: {
          len: 4,
          offset: 0,
          default: 5,
          interval: 1,
        },
        // 触发倾斜度，0：60度，1：45度，2：30度;
        triggerTilt: {
          len: 2,
          offset: 4,
          default: 0,
        },
        // 倒放触发方式，0：仅倾斜，1：仅运动检测，2：倾斜或运动检测;
        triggerMode: {
          len: 2,
          offset: 6,
          default: 0,
        },
      },
    },
  },
}

class AlertConfig extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 17
    this.structLen = options.structLen || 8
  }
}

// 数字警报 最大数量: 32
const DigitalAlarmProto = {
  0: {
    // 标识当前为第几组
    id: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 报警类型,(0--禁止，1--常规，2--静默，3--静默带语音)
    type: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 报警模式,(0--紧急报警，1--紧急报警和呼叫，2--紧急报警和语音)
    mode: {
      type: 'u8',
      offset: 2,
      default: 0,
    },
    // 回复信道,    (信道ID 值,等于信道最大数据为选定信道)
    replyChannel: {
      type: 'u16',
      offset: 3,
      default: 0xFFFF,
    },
    // 不礼貌重试, 范围:1~15， 步进:1
    impoliteRetry: {
      type: 'u8',
      offset: 5,
      default: 15,
      interval: 1,
    },
    // 礼貌重试, 范围:0~14，无穷。 步进:1，0xff表示无穷
    politeRetry: {
      type: 'u8',
      offset: 6,
      default: 5,
      interval: 1,
    },
    // Hot Mic 持续时间(秒): 范围: 10 ~120, setp:10
    hotMicDuration: {
      type: 'u8',
      offset: 7,
      default: 10,
    },
    config: {
      type: 'u8',
      offset: 8,
      bits: {
        // 报警自动发送GPS消息 1-允许，0-不允许。default = 0
        autoSendGps: {
          bool,
          offset: 0,
          len: 1,
        },
      },
    },
    // 数字报警名称
    name: {
      type: 'stringU16',
      len: 32,
      offset: 9,
    },
  },
}

class DigitalAlarm extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 19
    this.structLen = options.structLen || 41
  }
}

export const CallType = generateEnumObject({
  NONE: 0,
  // 组呼
  GROUP: 1,
  // 单呼
  SINGLE: 2,
  // 全呼
  BROADCAST: 3,
})

// 数字联系人列表
const DigitalAddressProto = {
  0: {
    // 通讯录 ID
    id: {
      type: 'u16',
      offset: 0,
    },
    // 归属哪一个群组
    groupId: {
      type: 'u16',
      offset: 2,
    },
    // 通讯录名字 32bytes
    name: {
      type: 'stringU16',
      len: 32,
      offset: 4,
    },
    // 通讯录号码 3bytes
    number: {
      type: 'mulInt',
      len: 3,
      offset: 36,
    },
    // 通讯录呼叫类型 1byte
    callType: {
      type: 'u8',
      offset: 39,
    },
    // 通讯录呼叫铃声类型 1byte
    ringType: {
      type: 'u8',
      offset: 40,
    },
  },
}

class DigitalAddress extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 21
    this.structLen = options.structLen || 41
  }
}

// 数字通讯录列表  最大数量: 10
const AddressGroupProto = {
  0: {
    // 群组 ID
    id: {
      type: 'u16',
      offset: 0,
    },
    // 群组名字 32bytes
    name: {
      type: 'stringU16',
      len: 32,
      offset: 2,
    },
  },
}

class AddressGroup extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 22
    this.structLen = options.structLen || 34
  }
}

// 电话通讯录  最大数量: 100
class PhoneBook extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 23
    this.structLen = options.structLen || 54
  }
}

// 接收组列表 最大数量: 32
const RxGroupProto = {
  0: {
    // 标识当前为第几组
    groupId: {
      type: 'u16',
      offset: 0,
    },
    // 接收组列表里成员
    listenGroup: {
      type: 'u16',
      repeated: 16,
      offset: 2,
    },
    // 接收组列表里的有效组号数量
    count: {
      type: 'u8',
      offset: 34,
    },
    // 接收组列表名称
    groupName: {
      type: 'stringU16',
      len: 32,
      offset: 35,
    },
  },
}

class RxGroup extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 24
    this.structLen = options.structLen || 67
  }
}

// 区域数据  最大区域数量: 64
const ZoneLeafProto = {
  0: {
    // 区域 ID
    areaId: {
      type: 'u16',
    },
    // 本区域信道使用标志	以bit位表示
    chFlag: {
      type: 'u16',
      offset: 2,
    },
    // 本区域信道索引表
    chIdList: {
      type: 'u16',
      offset: 4,
      repeated: 16,
    },
    // 区域有效位表示	1 有效 其它值无效
    validFlag: {
      type: 'u8',
      offset: 36,
    },
    // 区域名称
    areaName: {
      type: 'stringU16',
      len: 32,
      offset: 37,
    },
  },
}

class ZoneLeaf extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 25
    this.structLen = options.structLen || 69
  }
}

// 信道数据
const ChannelProto = {
  0: {
    // 信道 ID
    chId: {
      type: 'u16',
      offset: 0,
    },
    // PBA ID，范围0~65535，默认0，不显示
    PBAId: {
      type: 'u16',
      offset: 2,
    },
    // 接收频率 单位Hz，小端存储
    rxFreq: {
      type: 'u32',
      offset: 4,
    },
    // 发射频率 单位Hz，小端存储
    txFreq: {
      type: 'u32',
      offset: 8,
    },

    // 数字、模拟信道共用字节参数

    // 数字信道
    // 组呼列表  0,1,2,3…49 (MAX_RECEIVE_GROUP_LIST-1). default = MAX_RECEIVE_GROUP_LIST ，表示无
    groupList: {
      type: 'u16',
      offset: 12,
    },
    // 默认通讯录地址
    defaultAddress: {
      type: 'u16',
      offset: 14,
    },
    // 彩色码	[0,15]
    colorCode: {
      type: 'u8',
      offset: 16,
    },
    // 时隙设置
    timeSlotConfig: {
      type: 'u8',
      offset: 17,
      bits: {
        // 时隙: 1[0], 2[1], 虚拟集群[2]      default = 1[0]
        timeSlot: {
          len: 2,
          offset: 0,
        },
        // 虚拟集群发射时隙: 无[0], 1[1], 2[2] ,default = 无[0],当slotNum == 2时此选项才可选
        virtualTimeSlot: {
          len: 2,
          offset: 2,
        },
        // 直通双时隙使能, 0未选择，1选中 default=0
        DCDMEnable: {
          len: 1,
          offset: 4,
          default: false,
        },
        // 信道时隙校准器，不合格[0],合格[1],首选[2],default=0,当dcdmEnable==1时此项才可选
        chSlotAdjust: {
          len: 2,
          offset: 5,
        },
        //直通模式使能，1-勾选，0-不勾选（非异频时为灰色不可选）
        directModeEnable: {
          len: 1,
          offset: 7,
        },
      },
    },
    // 数字信道设置2
    voiceConfig: {
      type: 'u8',
      offset: 18,
      bits: {
        // 优先打断使能 default = 0
        priorityInterrupt: {
          len: 1,
          bool,
          offset: 7,
          default: false,
        },
        // 语音优先级0-3,default = 0
        voicePriority: {
          len: 2,
          offset: 1,
        },
        // 准许条件	0 可用彩色码 1 始终 2 信道空闲
        permitConditions: {
          len: 2,
          offset: 3,
          default: 2,
        },
        // 单呼应答	0 未选择 1 选中 默认值 0
        singleCallRes: {
          len: 1,
          bool,
          offset: 5,
        },
        // 语音双工, 0未选择，1选中 default=0，virtualTimeSlot=2时此选项才可选
        voiceDuplex: {
          len: 1,
          bool,
          offset: 6,
          default: false,
        },
        // 语音呼叫嵌入位置信息, 0未选择，1选中 default=0
        voiceCallEmbedding: {
          len: 1,
          bool,
          offset: 7,
          default: false,
        },
      },
    },
    // 数字信道设置3
    alertConfig: {
      type: 'u8',
      offset: 19,
      bits: {
        // 紧急警报指示	0 未选择 1 选中 默认值 0
        emergencyAlertTip: {
          len: 1,
          bool,
          offset: 0,
        },
        // 紧急警报确认
        emergencyAlertConfirm: {
          len: 1,
          bool,
          offset: 1,
        },
        // 紧急呼叫指示	0 未选择 1 选中 默认值 0
        emergencyCallTip: {
          len: 1,
          bool,
          offset: 2,
        },
        // 非确认单呼短信, 0未选择，1选中 default=0
        unConfirmSingleCall: {
          len: 1,
          bool,
          offset: 3,
          default: false,
        },
        // 短信UDP头压缩, 0未选择，1选中 default=0
        smsUdpCompress: {
          len: 1,
          bool,
          offset: 4,
          default: false,
        },
        // 联网, 0未选择，1选中 default=0
        networking: {
          len: 1,
          bool,
          offset: 5,
        },
        // 虚拟集群使能，1-允许，0-不允许 default=0，非中继模式为灰色不可选
        virtualClusterEnable: {
          len: 1,
          bool,
          offset: 6,
          default: false,
        },
        // 本地呼叫：1-勾选，0-不勾选（联网和虚拟集群同时不勾选时为灰色）
        localCall: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // 紧急系统ID值	默认值 255(无)
    emergencySysId: {
      type: 'u8',
      offset: 20,
    },
    // 虚拟集群站点信息列表ID值，default = 255 表示无
    // 虚拟集群未使能不勾选时此项为灰不可选
    // 混合信道无此选项
    virtualClusterSiteInfoListId: {
      type: 'u8',
      offset: 21,
      default: 255,
    },
    // 加密相关设置
    encryptConfig: {
      type: 'u8',
      offset: 22,
      bits: {
        // 加密使能，1 Enable, 0 Disable. default = 0
        enable: {
          bool,
          offset: 0,
          len: 1,
          default: false,
        },
        // 当未使能时，以下参数无效
        // 加密类型，0 基础，1 高级 default = 0
        type: {
          offset: 1,
          len: 1,
          default: 0,
        },
        // 加密算法，
        // 基础类型：0 异或，1 增强异或，2 ARC4，3 AES256，default=0
        // 高级类型：0 ARC4，1 AES256
        algorithm: {
          offset: 2,
          len: 3,
          default: 0,
        },
        // 多密钥加密，0未选择，1选中 default=0
        // 当加密类型为基础时，该选项不可选
        encryptKeyRandom: {
          bool,
          offset: 5,
          len: 1,
          default: false,
        },
        // 多密钥解密，0未选择，1选中 default=0
        // 当加密类型为基础时，该选项不可选
        decryptKeyRandom: {
          bool,
          offset: 6,
          len: 1,
          default: false,
        },
        // 短信是否加密, 0未选择，1选中 default=0
        // 当加密类型为基础时，该选项不可选
        smsEncrypt: {
          bool,
          offset: 7,
          len: 1,
          default: false,
        },
      },
    },
    // 密钥列表序号，根据加密类型不同选择不同密钥列表
    // 基础类型：对应基础密钥列表
    // 高级类型：ARC4对应ARC4密钥列表；AES256对应AES256列表
    encryptListId: {
      type: 'u8',
      offset: 23,
      default: 0,
    },
    // 别名设置
    aliasConfig: {
      type: 'u8',
      offset: 24,
      // 别名使能 0未选择，1选中 default=0
      bits: {
        enable: {
          bool,
          offset: 0,
          len: 1,
          default: false,
        },
      },
    },

    // 模拟信道
    // 亚音解码，bit15=0表示CTCSS，bit15=1表示DCS，bit14=0表示DCS正码，bit14=1表示DCS反码，
    // 0xffff表示无亚音，模拟信道default=0xffff，兼容信道default=0x0670
    subsonicDecode: {
      type: 'u16',
      offset: 25,
    },
    // 亚音编码,bit15=0表示CTCSS，bit15=1表示DCS，bit14=0表示DCS正码，bit14=1表示DCS反码，
    // 0xffff表示无亚音，模拟信道default=0xffff，兼容信道default=0x0670
    subsonicEncode: {
      type: 'u16',
      offset: 27,
    },
    // 扰频值	[0,6000] 默认值 3300
    scramble: {
      type: 'u16',
      offset: 29,
    },
    // 信令类型，0-两音，1-五音，2-DTMF ，0XFF-无，默认无
    signalType: {
      type: 'u8',
      offset: 31,
    },
    // 特性列表 索引ID
    featureListId: {
      type: 'u8',
      offset: 32,
    },
    // 信令系统索引ID,范围：选择两音信令时，0-3;五音或者DTMF信令时，0-7;  0XFF表示无
    signalSysId: {
      type: 'u8',
      offset: 33,
    },
    // 接收静噪类型
    noiseType: {
      type: 'u8',
      offset: 34,
    },
    // 接收自动复位时间，仅在接收静噪类型选择包含信令模式时显示，1-255，单位秒
    autoResetTime: {
      type: 'u8',
      offset: 35,
    },
    // 接收自动复位模式，仅在接收静噪类型选择包含信令模式时显示
    autoResetMode: {
      type: 'u8',
      offset: 36,
    },
    // 模拟报警系统ID
    alarmSysId: {
      type: 'u8',
      offset: 37,
    },
    // 模拟信道设置
    analogChannelConfig: {
      type: 'u8',
      offset: 38,
      bits: {
        // 带宽标志 0-宽带，1-窄带， 预留，默认值 = 1
        bandwidthFlag: {
          len: 1,
          offset: 0,
          default: 1,
        },
        // 拍频0-关，1-开，default = 0
        beatFreq: {
          len: 1,
          bool,
          offset: 1,
          default: false,
        },
        // 预加重 0-关，1-开，default = 0 保留字段，界面不显示
        preEmphasis: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // 压扩 0-关，1-开，default = 0 保留字段，界面不显示
        companding: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
        // 扰频开关 0-关，1-开，default = 0 保留字段，界面不显示
        scrambleEnable: {
          len: 1,
          offset: 4,
          bool,
          default: false,
        },
        // 0-关，1-载波，2-CT/DCS，default = 0
        flgBCL: {
          len: 2,
          offset: 5,
          default: 0,
        },
        // 尾音消除， 0-关，1-开 default=0
        tailNoise: {
          len: 1,
          offset: 7,
          bool,
          default: false,
        },
      },
    },
    subtoneConfig: {
      type: 'u8',
      offset: 39,
      bits: {
        // 爆破音：发射亚音为模拟亚音时，可选择功能：关亚音[0]、标准相位[1]，非标准相位[2]
        ctcssTxDps: {
          len: 3,
          offset: 0,
        },
        // 接收尾音：接收亚音为模拟亚音时，可选择功能：无亚音[0]、标准相位[1]，非标准相位[2]
        ctcssRxDps: {
          len: 3,
          offset: 3,
        },
      },
    },

    // 信道标志: 0-数字信道, 1-模拟信道  2-数字兼容模拟 3-模拟兼容数字
    chType: {
      type: 'u8',
      offset: 40,
    },
    // 扫描列表/漫游列表	默认值 255(无)
    scanList: {
      type: 'u8',
      offset: 41,
      default: 255,
    },
    // 列表类型,自动扫描标志, 只接收标志, IP站点连接,自动漫游标识,允许脱网标志
    scanConfig: {
      type: 'u8',
      offset: 42,
      bits: {
        // 列表类型	0 扫描列表 1 漫游列表(只支持数字信道)
        listType: {
          len: 1,
          offset: 0,
        },
        // 自动扫描标志
        autoScan: {
          len: 1,
          bool,
          offset: 1,
        },
        // 只接收标志	0 正常 1 只接收 默认值 0
        onlyReceive: {
          len: 1,
          bool,
          offset: 2,
        },
        // IP站点连接	0 关闭 1 使能 只支持数据信道
        ipSiteConnect: {
          bool,
          len: 1,
          offset: 3,
        },
        // 自动漫游标识
        autoRoam: {
          bool,
          len: 1,
          offset: 4,
        },
        // 允许脱网标志 0 不允许(默认值) 1 允许
        allowOfflineSign: {
          bool,
          len: 1,
          offset: 5,
        },
      },
    },
    // 高低功率标志,静噪等级
    powerConfig: {
      type: 'u8',
      offset: 43,
      bits: {
        // 高低功率标志	0 低功率 1 中功率 2 高功率 默认值 0
        powerType: {
          len: 4,
          offset: 0,
          default: 0,
        },
        // 静噪等级	[0,9] 默认值 3
        squelchLevel: {
          len: 4,
          offset: 2,
          default: 3,
        },
      },
    },
    // 发射限时 取值范围 [1,33] 表示范围 [15,495]秒 间隔 15秒 默认值 60秒 0表示无限
    transmissionLimit: {
      type: 'u8',
      interval: 15,
      offset: 44,
    },
    // TOT超时禁发延迟，单位:秒, 范围:0~255。直接使用变量值。default=0
    TOTKeyDelay: {
      type: 'u8',
      offset: 45,
    },
    // 信道名称
    chName: {
      type: 'stringU16',
      len: 32,
      offset: 46,
    },
  },
}

// 最大信道数量 1024
class Channel extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 26
    this.structLen = options.structLen || 78
  }
}

// 扫描列表   最大数量: 32
const ScanProto = {
  0: {
    // 扫描组 ID
    scanId: {
      type: 'u8',
      offset: 0,
    },
    // 成员数量
    memberCount: {
      type: 'u8',
      offset: 1,
    },
    // 成员列表	信道ID索引  最大信道数: 16
    // 默认有一个选定的成员, 信道ID为65534表示选定信道，信道ID为65535表示优先级信道为空
    membersList: {
      type: 'u16',
      repeated: 16,
      offset: 2,
    },
    // 第一优先信道	信道 ID 索引
    // (写频软件要求判断该信道是否已经设置为优先级2），默认空
    priority1Ch: {
      type: 'u16',
      offset: 34,
      default: 0xFFFE,
    },
    // 第二优先信道	信道 ID 索引
    priority2Ch: {
      type: 'u16',
      offset: 36,
      default: 0xFFFF,
    },
    // 优先回扫时间, 0：无，1：500ms，2：750ms，最大7500ms，步进250ms，默认无
    priSampleTime: {
      type: 'u8',
      interval: 250,
      offset: 38,
      default: 0,
    },
    // 停留时间, 0.5s ~ 10s，step = 0.5s，默认3s
    stayTime: {
      type: 'u8',
      offset: 39,
      default: 3,
    },
    config: {
      type: 'u8',
      offset: 40,
      bits: {
        // 亚音扫描模式 0 不检测亚音 1 非优先信道检测 2 优先信道检测 3 所有信道检测(默认值)
        subScanMode: {
          offset: 0,
          len: 2,
          default: 3,
        },
        // 回讲	0 不允许 1 允许
        reply: {
          bool,
          offset: 2,
          len: 1,
        },
        // 扫描发射模式	0 当前信道(默认值) 1 最后活动信道 2 指定信道
        scanTxMode: {
          offset: 3,
          len: 2,
          default: 0,
        },
      },
    },
    // 指定的发送信道ID值，该值为存放usScanMemList结构里的一个有效信道ID值，当 扫描发射模式 = 2时才有效
    appointTxCh: {
      type: 'u16',
      offset: 41,
    },
    // 名称
    name: {
      type: 'stringU16',
      len: 32,
      offset: 43,
    },
  },
}

class Scan extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 27
    this.structLen = options.structLen || 75
  }

  decode(structData) {
    const data = super.decode(structData)
    data.result = data.result.map((item) => {
      item.stayTime = Math.floor(item.stayTime / 2)
      return item
    })
    return data
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }

    const list = data.map((item) => {
      item.stayTime = Math.floor(item.stayTime * 2)
      return item
    })

    return super.encode(list)
  }
}

// 漫游列表  最大数量: 32
const RoamProto = {
  0: {
    // 扫描组 ID
    roamId: {
      type: 'u8',
      offset: 0,
    },
    config: {
      type: 'u8',
      offset: 1,
      bits: {
        // 主动站点漫游使能	0 禁用(默认值) 1 启用
        mainSiteRoaming: {
          bool,
          offset: 0,
          len: 1,
        },
        // 跟随主站点设置	0 禁用(默认值) 1 启用
        followMainSite: {
          bool,
          offset: 1,
          len: 1,
        },
        // 信道区域信道作为扫描列表	0 禁用(默认值) 1 启用 启用时列表不可选
        channelAsScanList: {
          bool,
          offset: 2,
          len: 1,
        },
      },
    },
    // 站点搜索计时器，单位秒，步进1，范围0~255，默认0
    siteSearchTimer: {
      type: 'u8',
      offset: 2,
      default: 0,
      interval: 1,
    },
    // 自动搜索计时器，单位秒，步进1，范围1~300，默认60
    autoSearchTimer: {
      type: 'u16',
      offset: 3,
      default: 60,
      interval: 1,
    },
    // RSSI阈值	[-120,-80] 间隔 1 绝对值传输
    rssi: {
      type: 'u8',
      offset: 5,
    },
    // 漫游channel的数量
    roamChCount: {
      type: 'u8',
      offset: 6,
    },
    // 成员列表	信道ID索引  最大信道数: 16
    memberList: {
      type: 'u16',
      repeated: 16,
      offset: 7,
    },
    // 名称
    name: {
      type: 'stringU16',
      len: 32,
      offset: 39,
    },
  },
}

class Roam extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 28
    this.structLen = options.structLen || 71
  }

  decode(structData) {
    const data = super.decode(structData)
    data.result = data.result.map((item) => {
      // 解RSSI阈值绝对值
      item.rssi = item.rssi * -1
      return item
    })
    return data
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }

    const list = data.map((item) => {
      //  RSSI阈值，绝对值传输
      item.rssi = Math.abs(item.rssi)
      return item
    })

    return super.encode(list)
  }
}

// 录音文件信息列表
const RecordListProto = {
  0: {
    // rt_uint8_t	isValid文件有效性，1有效，其他 文件损坏
    isValid: {
      type: 'u8',
      offset: 0,
      default: 1,
    },
    // rt_uint32_t u32DesId;目标ID，单呼存放对方ID，组呼存放组呼ID(Bit31 = 1)，ID= 0表示模拟/全呼？
    desId: {
      type: 'u32',
      offset: 1,
    },
    //rt_uint8_t  au8TimeBcd[7];会话起始时间，BCD格式，比如20230302161130=0x20 0x23 0x03 0x02 0x16 0x11 0x30
    startTime: {
      type: 'bcd',
      // repeated: 7,
      len: 7,
      offset: 5,
    },
    //rt_uint8_t  bVocoderType	:4;声码器类型 0:AMBE 1:NVOC 2:清华声码器
    // 	rt_uint8_t  bComType		:4; 通道类型 0:单工 1:双工
    // rt_uint8_t  bCallInOut   :1; 呼入=0 呼出=1
    recordConfig: {
      type: 'u8',
      offset: 12,
      bits: {
        vocoderType: {
          offset: 0,
          len: 4,
        },
        comType: {
          offset: 4,
          len: 1,
        },
        callInOut: {
          offset: 5,
          len: 1,
        },
      },
    },
    // rt_uint32_t u32FrameNum;语音帧数量 暂为空
    frameNum: {
      type: 'u32',
      offset: 13,
    },
    //rt_uint32_t fileIdx;文件索引，用来带入读单个文件数据的
    fileIdx: {
      type: 'u32',
      offset: 17,
    },
    // rt_uint32_t fileNum;//文件序号 备用
    fileNum: {
      type: 'u32',
      offset: 21,
    },
  },
}

// 一页的文件数量 6
class RecordList extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 31
    this.structLen = options.structLen || 25
  }
}

// 录音文件信息
const RecordProto = {
  0: {
    // 标识信息
    recordFlag: {
      type: 'u8',
      offset: 0,
      bits: {
        // 发射或接受
        transmitOrReceive: {
          len: 1,
          offset: 0,
        },
        // 帧类型 0空帧 1～6对于A-F,7无类型
        frameType: {
          len: 3,
          offset: 1,
        },
        // F为偷帧
        frameFNone: {
          len: 1,
          offset: 4,
        },
      },
    },
    //文件mask
    fileMask: {
      type: 'u32',
      offset: 1,
    },
    // 声码器数据
    frame: {
      type: 'u8',
      offset: 5,
      repeated: 27,
    },
  },
}

class Record extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 432
    this.structLen = options.structLen || 32
  }
}

export class PatrolConfig extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 51
    this.structLen = options.structLen || 3
  }
}

export const TrackMonitorProto = {
  0: {
    menuConfig: {
      type: 'u8',
      offset: 0,
      bits: {
        // 自动定位监控使能， default=1
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
      },
    },
    // 以下参数，当 自动定位监控使能 =0时为不可选状态，设置时默认发送0；
    // 轮询时间片，单位秒，步进5，范围10-9995，默认300秒[60]
    rollTime: {
      type: 'u16',
      offset: 1,
      interval: 5,
      default: 300,
    },
    // 轮询距离，单位米，步进5，范围0-495米，默认50米[10]
    rollDistant: {
      type: 'u8',
      offset: 3,
      interval: 5,
      default: 50,
    },
  },
}

// 自动定位监控
export class AutoPosMonitor extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 54
    this.version = options.version || 0
    this.structLen = options.structLen || 4
  }
}

export class EmergencyAlarm extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 53
    this.structLen = options.structLen || 6
  }
}

const VirtualClusterProto = {
  0: {
    // 所属归属组，从组呼联系人选择
    vcGroupId: {
      type: 'u16',
      offset: 0,
      default: 0xffff,
    },
    // 测机信号间隔，单位MS，范围960~18000，步进120
    testMachineInterval: {
      type: 'u16',
      offset: 2,
      default: 3600,
    },
    // RSSI阈值，范围-120~-80，步进1，绝对值传输
    rssiValue: {
      type: 'u8',
      offset: 4,
      default: 0,
      interval: 1,
    },
    // 站点成员数量
    siteListMemCount: {
      type: 'u8',
      offset: 5,
      default: 1,
    },
    // 密钥值,0~9,A~F,没填满默认补F
    authKey: {
      type: 'string',
      len: 32,
      offset: 6,
      default: ''.padEnd(32, 'F'),
    },
    // 站点列表(信道)，固定包含"选定的"站点 默认：0xFFFE
    // "0xFFFF:无
    // 0xFFFE:选定的信道
    // 0~1023：指定已配置信道"
    siteList: {
      type: 'u16',
      offset: 38,
      repeated: 16,
      default: [0xfffe],
    },
  },
}

export class VirtualCluster extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 各版本的数据结构长度
    this.type = options.type || 60
    this.structLen = options.structLen || 70
  }
}

const SiteInfoProro = {
  0: {
    // 站点信息ID	DBID_C_SITE	范围: 0~15
    id: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 频点列表的数量
    count: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 发射频点列表
    txFreqList: {
      type: 'u32',
      offset: 2,
      repeated: 16,
    },
    // 接收频点列表
    rxFreqList: {
      type: 'u32',
      offset: 66,
      repeated: 16,
    },
    // 站点名称	uint16[16+1]	"支持最长16个unicode字符"
    name: {
      type: 'stringU16',
      offset: 130,
      len: 32,
      default: '',
    },
  },
}

export class SiteInfo extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 61
    this.structLen = options.structLen || 162
  }
}

loadProtocol(Model, {
  // 设备信息
  1: cloneDeep(DeviceInfoProto),
  // 身份信息 IdentityInfo
  2: cloneDeep(IdentityInfoProto),
  // 通信密码
  3: cloneDeep(PasswordProto),
  // 总体设置
  4: cloneDeep(GeneralSettingsProto),
  // 按键设置
  5: cloneDeep(ButtonSettingsProto),
  // 短信
  6: cloneDeep(ShortMessageProto),
  // 加密配置
  7: cloneDeep(EncryptSettingsProto),
  // 基础密钥列表
  8: cloneDeep(EncryptListProto),
  // ARC4
  9: cloneDeep(ARC4ListProto),
  // AES256
  10: cloneDeep(AES256ListProto),
  // GPS设置
  13: cloneDeep(GpsSettingsProto),
  // 菜单配置
  15: cloneDeep(MenuSettingsProto),
  // 信令系统
  16: cloneDeep(SignalingSystemProto),
  // 警报设置
  17: cloneDeep(AlertConfigProto),
  // 模拟紧急警报 无
  18: {
    0: {
      // 警报索引ID, 0-3,标识当前为第几组, ALARM_VALID_FLAG = 0xFF表示无效
      alertIndexId: {
        type: 'u8',
        offset: 0,
      },
      // 警报类型
      alertType: {
        type: 'u8',
        offset: 1,
      },
      // 警报模式
      alertMode: {
        type: 'u8',
        offset: 2,
      },
      // 警报ID信令类型 0-无，1-五音，2-DTMF
      alertIdSignalType: {
        type: 'u8',
        offset: 3,
      },
      // 回复信道:值SELECTED_CH_ID = 0XFE表示选定的,其余值为对应信道ID)
      responseChannel: {
        type: 'u16',
        offset: 4,
      },
      // 紧急警报报文索引ID
      alertMsgIndexId: {
        type: 'u8',
        offset: 6,
      },
      // 本地紧急警报:0-关，1-开，默认0
      localEmergency: {
        type: 'u8',
        bool,
        offset: 7,
      },
      // 紧急警报持续时间[秒]: 1-255，步进1，默认10
      emergencyDuration: {
        type: 'u8',
        offset: 8,
        interval: 1,
      },
      // 紧急警报次数: 1-254/255(255表示无限制)，步进1，默认1
      emergencyCount: {
        type: 'u8',
        offset: 9,
        interval: 1,
      },
      // 报警静噪模式: 0-载波，1-亚音，默认0
      alertNoiseMode: {
        type: 'u8',
        offset: 10,
      },
      // 紧急呼叫次数:1-254/255(255表示无限制)，步进1，默认1
      emergencyCallCount: {
        type: 'u8',
        offset: 11,
        interval: 1,
      },
      // 麦克风激活时间[秒] :10-120,步进10，默认:10
      microphoneActiveTime: {
        type: 'u8',
        offset: 12,
        interval: 10,
      },
      // 接收持续时间[秒] :10-120,步进10，默认:10
      receiveDuration: {
        type: 'u8',
        offset: 13,
        interval: 10,
      },
      // [N]背景提示音: 0- 关，1-开
      backgroundSound: {
        type: 'u8',
        offset: 14,
      },
      // 不礼貌重试次数,1-15,默认15
      retryCount: {
        type: 'u8',
        offset: 15,
      },
      // 数字报警名称,写频传下来的格式是使用unicode表示但是实际存储转为gb2312格式
      alertName: {
        type: 'byteArray',
        offset: 16,
        len: 32,
      },
      // 礼貌重试次数,0-15 (15-不停的),默认0
      retryCount2: {
        type: 'u8',
        offset: 48,
      },
      // 自动紧急呼叫
      autoEmergencyCall: {
        type: 'u8',
        offset: 49,
      },
      // 对齐字节
      align: {
        type: 'byteArray',
        len: 2,
        offset: 50,
      },
    },
  },
  // 数字紧急报警
  19: cloneDeep(DigitalAlarmProto),
  // 数字联系人列表
  21: cloneDeep(DigitalAddressProto),
  // 数字联系人群组 最大数量16
  22: cloneDeep(AddressGroupProto),
  // 电话本
  23: cloneDeep(PhoneBookProto),
  // 接收组列表
  24: cloneDeep(RxGroupProto),
  // 区域数据
  25: cloneDeep(ZoneLeafProto),
  // 信道数据
  26: cloneDeep(ChannelProto),
  // 扫描列表
  27: cloneDeep(ScanProto),
  // 漫游列表
  28: cloneDeep(RoamProto),
  // 录音文件信息列表数据
  31: cloneDeep(RecordListProto),
  // 录音文件信息
  432: cloneDeep(RecordProto),
  // 资源版本信息
  // 0: {
  //   verInfo: {
  //     type: 'string',
  //     offset: 0,
  //     len: 256,
  //   }
  // }

  // 巡逻系统配置
  51: cloneDeep(PatrolConfigProto),
  // 紧急报警
  53: cloneDeep(EmergencyAlarmProto),
  // 自动定位监控
  54: cloneDeep(TrackMonitorProto),
  // 虚拟集群配置
  60: cloneDeep(VirtualClusterProto),
  // 虚拟集群站点信息列表
  61: cloneDeep(SiteInfoProro),
})

const StructIndex = {
  // 设备信息
  1: BP610DeviceInfo,
  // 身份信息
  2: SecondIdentityInfo,
  // 通信密码
  3: Password,
  // 总体设置
  4: GeneralSettings,
  // 按键设置
  5: ButtonSettings,
  // 短信
  6: ShortMessage,
  // 加密配置
  7: EncryptSettings,
  // 加密列表
  8: EncryptList,
  // 加密列表
  9: ARC4List,
  // 加密列表
  10: AES256List,
  // GPS
  13: GpsSettings,
  // 菜单配置
  15: MenuSettings,
  // 信令系统
  16: SignalingSystem,
  // 警报配置
  17: AlertConfig,
  // 数字警报
  19: DigitalAlarm,
  // 数字联系人列表
  21: DigitalAddress,
  // 数字联系人分组
  22: AddressGroup,
  // 电话通讯录
  23: PhoneBook,
  // 接收组列表
  24: RxGroup,
  // 区域
  25: ZoneLeaf,
  // 信道
  26: Channel,
  // 扫描列表
  27: Scan,
  // 漫游列表
  28: Roam,
  //录音文件列表
  31: RecordList,
  // 录音文件信息
  432: Record,
  // 巡逻系统配置
  51: PatrolConfig,
  // 紧急报警
  53: EmergencyAlarm,
  // 自动定位监控
  54: AutoPosMonitor,
  // 虚拟集群配置
  60: VirtualCluster,
  // 虚拟集群站点信息列表
  61: SiteInfo,
}

// 系统参数表ID及结构体定义
export const TableIndex = generateEnumObject({
  // 生产信息	生产相关的设备信息，包括 型号，频段，版本信息
  BP610DeviceInfo: 1,
  // 身份信息, TD-910 机型的序列号采用二代序列号
  SecondIdentityInfo: 2,
  // 通信密码
  Password: 3,
  // 常规设置
  GeneralSettings: 4,
  // 按键设置
  ButtonSettings: 5,
  // 预制短信
  ShortMessage: 6,
  // 加密配置
  EncryptSettings: 7,
  // 基础密钥列表
  EncryptList: 8,
  // ARC4密钥列表
  ARC4List: 9,
  // AES256密钥列表
  AES256List: 10,
  // 卫星定位
  GpsSettings: 13,
  // 菜单配置
  MenuSettings: 15,
  // 信令系统
  SignalingSystem: 16,
  // 警报配置
  AlertConfig: 17,
  // 数字警报
  DigitalAlarm: 19,
  // 数字联系人列表
  DigitalAddress: 21,
  // 数字联系人分组
  AddressGroup: 22,
  // 电话通讯录
  PhoneBook: 23,
  // 接收组列表
  RxGroup: 24,
  // 区域数据
  ZoneLeaf: 25,
  // 信道
  Channel: 26,
  // 扫描列表
  Scan: 27,
  // 漫游列表
  Roam: 28,

  //录音文件列表 31

  // 资源版本信息
  // ResourceVersion: 32,
  // 巡逻系统配置
  PatrolConfig: 51,
  // 紧急报警
  EmergencyAlarm: 53,
  // 自动定位监控,
  AutoPosMonitor: 54,
  // 区域电子围栏监控
  // 岗哨监控
  // 移动监控
  // GPS巡更
  // 有源RFID
  // 救援/求救配置
  // SosCfg: 42,
  // 救援/求救信道
  // SosChData: 43,
  // 虚拟集群配置
  VirtualCluster: 60,
  // 虚拟集群站点信息列表
  SiteInfo: 61,
})

export function getClassInstance(options) {
  const opts = {
    ...BaseOptions,
    ...options,
  }
  const DataClass = StructIndex[opts.type]
  if (!DataClass) {
    return null
  }

  return new DataClass(opts)
}
