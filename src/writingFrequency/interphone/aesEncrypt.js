import aesjs from 'aes-js'
import { DefModel } from './common'
import { md5ToBytes } from '@/utils/crypto.js'

// 写频加密向量对象，机型->向量值
const Ivs = {
  TD080000: [0x59, 0xbd, 0xa5, 0x3c, 0x3c, 0xba, 0xdd, 0x19, 0xc6, 0xc1, 0x32, 0xc0, 0xd5, 0xd8, 0x23, 0x1e],
  TD081000: [0xC7, 0xDC, 0xFD, 0xA5, 0x75, 0xDC, 0x6D, 0xB7, 0xB6, 0x94, 0x32, 0xFD, 0x1C, 0x79, 0x13, 0x46],
  TD081100: [0xC7, 0xDC, 0xFD, 0xA5, 0x75, 0xDC, 0x6D, 0xB7, 0xB6, 0x94, 0x32, 0xFD, 0x1C, 0x79, 0x13, 0x46],
}
// md5(program password)
const EncryptKey = [0xd4, 0x1d, 0x8c, 0xd9, 0x8f, 0x00, 0xb2, 0x04, 0xe9, 0x80, 0x09, 0x98, 0xec, 0xf8, 0x42, 0x7e]
 
const TD811EncryptKey = [0xbe, 0xd1, 0x28, 0x36, 0x52, 0x16, 0xc0, 0x19, 0x98, 0x89, 0x15, 0xed, 0x3a, 0xdd, 0x75, 0xfb]

// 128字节密钥
export const AuthenticationKeys = {
  TD080000: [
    0x8a, 0x85, 0x65, 0xe8, 0x62,
    0x8d, 0x03, 0x30, 0xdd, 0x78, 0x9e, 0xba, 0x5f, 0x0b, 0xba, 0xc8, 0x67, 0x9f, 0x70, 0xdd, 0xa1,
    0x63, 0x39, 0x4e, 0xd3, 0x7f, 0xb3, 0x9b, 0x76, 0x0e, 0xf9, 0x85, 0xaa, 0x04, 0x6c, 0x8f, 0x97,
    0x27, 0x73, 0x39, 0xf9, 0xd4, 0x5b, 0xf1, 0x76, 0x1f, 0x24, 0xd5, 0x2d, 0x14, 0x22, 0x44, 0xc2,
    0x6f, 0x3a, 0x21, 0xf1, 0x9f, 0x7d, 0x90, 0x93, 0xe8, 0x0e, 0x0f, 0xbe, 0xe1, 0x23, 0x0c, 0x11,
    0x7d, 0x9b, 0x94, 0x5d, 0x0d, 0x99, 0xcc, 0x7b, 0x74, 0xc8, 0x1f, 0x04, 0x32, 0xed, 0x50, 0xe3,
    0x29, 0x06, 0xee, 0xc2, 0x4c, 0x43, 0x27, 0x1d, 0xe1, 0x8c, 0x9b, 0x82, 0xa7, 0xc7, 0xec, 0x39,
    0x5a, 0x62, 0x2b, 0x58, 0x6f, 0xa2, 0x33, 0x14, 0x65, 0x8e, 0x8b, 0x01, 0xc5, 0x02, 0x28, 0xe9,
    0x8a, 0x4b, 0xb7, 0xae, 0xd9, 0xb0, 0x76, 0xce, 0xf2, 0xe0, 0x27,
  ],
  TD081000: [
    0x52, 0x24, 0xde, 0xda, 0x33,
    0xbc, 0x4a, 0x51, 0x21, 0x07, 0x17, 0x8b, 0xca, 0x11, 0x89, 0x2f, 0xc6, 0xec, 0x5e, 0xd8, 0x71,
    0x94, 0x50, 0xeb, 0xe6, 0xb2, 0x38, 0x2a, 0xbf, 0x38, 0x30, 0x4f, 0x7e, 0x9e, 0x90, 0x55, 0x57,
    0xaa, 0x6b, 0x8f, 0x0d, 0x14, 0x87, 0xa6, 0x9f, 0xc9, 0xb7, 0x1a, 0xd4, 0x40, 0x86, 0x41, 0x96,
    0x87, 0xeb, 0xa9, 0xdd, 0xef, 0x09, 0xe8, 0xda, 0x89, 0x02, 0xa4, 0x7d, 0x38, 0x90, 0x1b, 0xb8,
    0xb7, 0xcf, 0x95, 0x1f, 0x8f, 0x73, 0x0a, 0xc7, 0x55, 0xd1, 0x5e, 0xd9, 0xb4, 0x28, 0x4f, 0x99,
    0x11, 0x76, 0x54, 0x00, 0x38, 0xb8, 0xa7, 0xeb, 0x9a, 0xfe, 0x2c, 0x53, 0xcb, 0x63, 0x82, 0x5e,
    0x22, 0x3d, 0x75, 0x4c, 0x65, 0xbe, 0x1e, 0x53, 0x8f, 0xfb, 0x77, 0xb5, 0xba, 0x29, 0x29, 0x1a,
    0xb4, 0x44, 0x0b, 0xd2, 0x0d, 0x2f, 0x6c, 0x90, 0x3f, 0xac, 0xaa,
  ],
}
const models = ['511SDC00', 'DP109000']
models.forEach(model => {
  Ivs[model] = Ivs.TD081000
  AuthenticationKeys[model] = AuthenticationKeys.TD081000
})

function getIvFromModel(model = DefModel) {
  return Ivs[model] || Ivs[DefModel]
}

// TD081100机型，用户不输入密码时，使用空字符串进行md5秘钥计算
export function getMd5EncryptKey(pwd = '', mode = DefModel) {
  if (typeof pwd !== 'string' || !pwd) {
    return mode === 'TD081100' ? md5ToBytes('') : EncryptKey
  }

  return md5ToBytes(pwd)
}

export function getAuthenticationKey(model = DefModel) {
  return AuthenticationKeys[model] || AuthenticationKeys[DefModel]
}

// 加密数据补齐到16的倍数,最后的数据填充算法为(16-字节数),
// 例如:一个字节,补(16-1)个(16-1), 两个字节补(16-2)个(16-2),..., (16-n)个(16-n)
// [0x52, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f]
// 需要加密的数据填充到16字节
export function encryptedDataTo16Byte(buf) {
  const len = buf.length
  const residual = len % 16
  let missLen = 16 - residual
  // 刚好16的倍数，也需要补充16个16
  if (residual === 0) {
    missLen = 16
  }

  const missData = new Array(missLen)
  missData.fill(missLen)

  return buf.concat(missData)
}

function getAesCbc(model = DefModel, pwd = '') {
  const IV = getIvFromModel(model)
  const EncryptKey = getMd5EncryptKey(pwd, model)

  return new aesjs.ModeOfOperation.cbc(EncryptKey, IV)
}

export function Encrypt(bytes, model, pwd) {
  const aesCbc = getAesCbc(model, pwd)
  if (!(bytes instanceof Uint8Array)) {
    bytes = new Uint8Array(bytes)
  }
  return aesCbc.encrypt(bytes)
}

export function Decrypt(bytes, model, pwd) {
  const aesCbc = getAesCbc(model, pwd)
  if (!(bytes instanceof Uint8Array)) {
    bytes = new Uint8Array(bytes)
  }
  return aesCbc.decrypt(bytes)
}

export default {
  Encrypt,
  Decrypt,
}
