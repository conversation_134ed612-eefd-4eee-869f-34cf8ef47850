/*
该算法由福州研发提供的C++版本算法转换
/!**1021**!/
uint16 bf_crc16(uint8 *ptr,uint16 len)
{
	uint16 crc = 0;
	uint8 i;

	while(len--!=0)
	{
		for(i=0x80; i!=0; i/=2)
		{
			if((crc&0x8000)!=0)
			{
				crc*=2;
				crc^=0x1021;
			}
			else
			{
				crc*=2;
			}

			if((*ptr&i)!=0)
			{
				crc^=0x1021;
			}
		}

		ptr++;
	}

	return(crc);
}
*/

// 多项式
const Poly = 0x1021
// 初始值
const CrcInit = 0x0000
// CRC 算法最大字节数值
const CrcLimit = 0xFFFF

/**
 * @return {number}
 */
export function CRC16(buf = []) {
  if (!(buf instanceof Array) && !(buf instanceof Uint8Array)) {
    return CrcInit
  }

  let crc = CrcInit
  let index = 0
  let len = buf.length

  while (len-- !== 0) {
    const data = buf[index++]
    // i 取值范围 [128,64,32,16,8,4,2,1]
    for (let i = 7; i >= 0; i--) {
      if ((crc & 0x8000) !== 0) {
        crc *= 2
        crc ^= Poly
      } else {
        crc *= 2
      }

      if ((data & (1 << i)) !== 0) {
        crc ^= Poly
      }

      // 超出2字节部分舍掉
      crc &= CrcLimit
    }
  }

  return crc
}

export default {
  CRC16
}
