import { merge, cloneDeep } from 'lodash'
import {
  Unset,
  DeviceInfoProto,
  IdentityInfoProto,
  PasswordProto,
  GeneralSettingsProto,
  ShortMessageProto,
  MenuSettingsProto,
  DigitalAddressProto,
  RxGroupProto,
  ChannelProto,
  PatrolConfigProto,
  EmergencyAlarmProto,
  TrackMonitorProto,
  ActiveRFIDProto,
  AvailableChannelsProto,
  PhoneBookProto,
  DeviceBase,
  DeviceInfo,
  IdentityInfo,
  Password,
  base642hex,
  buf2base64,
  base642buf,
  loadProtocol,
  setRegistrationCode,
  getRegistrationCode,
  generateEnumObject, bool,
} from './common'
import { TD800SDCModel } from './models'

/*
数据区数据格式的定义如下表:
数据结构类型  	数据结构版本	结构数据数量	   结构数据区
1 Byte       	1 Byte     	2 Byte	   结构长度*结构数据的数量
数据结构类型：用枚举定义这个值，每个值对应一种数据结构，一帧中只允许有一种结构类型。
数据结构版本：该值与数据结构类型这两个值来确定一个唯一的数据结构。因为针对不同的机型，同一种结构类型会有不的结构定义，这样可以用这个变量来区分不同的定义。
结构数据数量：表示结构数据区里有几个同数据结构类型和数据结构版本的结构数据。当数据结构类型为只有一个结构数据时，该值为1。该值为0时表示本帧没有该结构类型数据，属于正常协议范围。
结构数据区：多个或一个结构数据，结构数据的对齐方式要求上下位机保持一致：按小端，单字节结构对齐的方式。
*/

export const Model = TD800SDCModel
const BaseOptions = {
  model: Model,
}
export const AddressBookCallTypes = generateEnumObject({
  // 无呼叫
  NONE: 0,
  // 组呼
  GROUP: 1,
  // 单呼
  SINGLE: 2,
  // 全呼
  BROADCAST: 3,
})

loadProtocol(Model, {
  // 设备信息 DeviceInfo
  1: cloneDeep(DeviceInfoProto),
  // 身份信息 IdentityInfo
  2: cloneDeep(IdentityInfoProto),
  // 通信密码
  3: cloneDeep(PasswordProto),
  // 总体设置 GeneralSettings
  4: merge(cloneDeep(GeneralSettingsProto), {
    0: {
      ids: {
        type: 'u32',
      },
      clientType: {
        type: 'u8',
        offset: 36,
        bits: {
          clientLevel: {
            /// ch: '终端级别',
            len: 2,
            offset: 0,
          },
          // ch: '是否终端机器',
          isDutyMachine: Unset,
          // ch: '是否指挥机',
          isDirectMachine: Unset,
        },
      },
      savePowerMode: Unset,
      locations: {
        type: 'u8',
        offset: 65,
        bits: {
          // 省电模式 3位  0:省电关闭，1:开
          savePowerMode: {
            len: 1,
            bool,
            offset: 0,
          },
          // 关闭定位 0:定位开启，1:定位关闭，默认0
          closePosition: {
            len: 1,
            bool,
            offset: 3,
          },
          // GPS定位
          gps: {
            len: 1,
            bool,
            offset: 4,
          },
          // 北斗定位
          beidou: {
            len: 1,
            bool,
            offset: 5,
          },
          // 格洛纳斯，默认0
          glonas: {
            len: 1,
            bool,
            offset: 6,
          },
          // 伽利略，默认0
          galileo: {
            len: 1,
            bool,
            offset: 7,
          },
        },
      },
    },
  }),
  // 短信 ShortMessage
  6: cloneDeep(ShortMessageProto),
  // 菜单设置 MenuSettings
  7: cloneDeep(MenuSettingsProto),
  // 数字通讯录 DigitalAddress
  9: cloneDeep(DigitalAddressProto),
  // 接收组列表 RxGroup
  10: cloneDeep(RxGroupProto),
  // 信道 Channel
  11: cloneDeep(ChannelProto),
  // 巡更系统配置 PatrolConfig
  12: cloneDeep(PatrolConfigProto),
  // 紧急报警 EmergencyAlarm
  14: merge(cloneDeep(EmergencyAlarmProto), {
    0: {
      alarmEnable: Unset,
      alarmConfig: {
        // ch: '功能开关',报警类型,
        type: 'u8',
        bits: {
          alarmEnable: {
            // ch: '功能开关',
            len: 1,
            offset: 0,
          },
          alarmType: {
            // ch: '报警类型',
            len: 1,
            offset: 1,
          },
        },
      },
      sendCount: {
        // ch: '发送次数',
        type: 'u8',
        offset: 1,
      },
      callingContact: {
        // ch: '呼叫联系人',
        type: 'u16',
        offset: 2,
      },
      autoListenTime: {
        // ch: '自动监听时间',
        type: 'u8',
        offset: 4,
      },
      autoTrackTime: {
        // ch: '自动追踪时间',
        type: 'u8',
        offset: 5,
      },
    },
  }),
  // 自动定位监控 TrackMonitor
  15: cloneDeep(TrackMonitorProto),
  // 有源RFID ActiveRFID
  20: cloneDeep(ActiveRFIDProto),
  // 可用信道 AvailableChannels
  21: cloneDeep(AvailableChannelsProto),
  // 电话本，PhoneBook 支持 100 个电话本
  22: cloneDeep(PhoneBookProto),
})

// 总体设置 GeneralSettings = 4,
class GeneralSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 4
    this.structLen = options.structLen || 75
  }

  encode(data) {
    let list = data
    if (!Array.isArray(data)) {
      list = [data]
    }
    list.forEach((item) => {
      item.regCode = setRegistrationCode()
    })
    return super.encode(list)
  }

  decode(structData) {
    const result = super.decode(structData)
    const config = result.result[0]
    config.regCode = getRegistrationCode(config.regCode)
    result.result[0] = config

    return result
  }
}

// 短信 ShortMessage = 6,
class ShortMessage extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 6
    this.structLen = options.structLen || 282
  }
}

// 菜单设置 MenuSettings = 7,
class MenuSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 7
    this.structLen = options.structLen || 4
  }
}

// 数字通讯录 DigitalAddress = 9
class DigitalAddress extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 9
    this.structLen = options.structLen || 39
  }
}

// 接收组列表 RxGroup = 10,
class RxGroup extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 10
    this.structLen = options.structLen || 67
  }
}

// 信道 Channel = 11,
class Channel extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 11
    this.structLen = options.structLen || 77
    this.subStructLen = options.subStructLen || 15
  }
}

// 巡查系统配置 PatrolConfig = 12,
class PatrolConfig extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 12
    this.structLen = options.structLen || 3
  }
}

// 紧急报警 EmergencyAlarm = 14,
class EmergencyAlarm extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 14
    this.structLen = options.structLen || 6
  }
}

// 自动定位监控 TrackMonitor = 15,
class TrackMonitor extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 15
    this.structLen = options.structLen || 4
  }
}

// 有源RFID ActiveRFID = 20
class ActiveRFID extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 20
    this.structLen = options.structLen || 50
  }
}

// 可用信道 AvailableChannels = 21
class AvailableChannels extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 21
    this.structLen = options.structLen || 76
  }

  decode(structData) {
    const structDataObj = super.decode(structData)

    for (let i = 0; i < structDataObj.result.length; i++) {
      const item = structDataObj.result[i]
      item.areaChannelIndexTable = item.areaChannelIndexTable.filter((n) => {
        return n !== 0xffff
      })
    }

    return structDataObj
  }
}

// 电话本 PhoneBook = 22
class PhoneBook extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 22
    this.structLen = options.structLen || 54
  }
}

// 数据结构类型索引对象
const StructIndex = {
  // 设备信息
  1: DeviceInfo,
  // 身份信息
  2: IdentityInfo,
  // 通信密码
  3: Password,
  // 总体设置
  4: GeneralSettings,
  // 短信
  6: ShortMessage,
  // 菜单设置
  7: MenuSettings,
  // 数字通讯录
  9: DigitalAddress,
  // 接收组列表
  10: RxGroup,
  // 信道
  11: Channel,
  // 配置
  12: PatrolConfig,
  // 紧急报警
  14: EmergencyAlarm,
  // 自动定位监控
  15: TrackMonitor,
  // 有源RFID
  20: ActiveRFID,
  // 可用信道
  21: AvailableChannels,
  // 电话本
  22: PhoneBook,
}

export function getClassInstance(options) {
  const opts = {
    ...BaseOptions,
    ...options,
  }
  const DataClass = StructIndex[opts.type]
  if (!DataClass) {
    return null
  }

  return new DataClass(opts)
}

export default {
  Model,
  buf2base64,
  base642buf,
  base642hex,
  getClassInstance,
}
