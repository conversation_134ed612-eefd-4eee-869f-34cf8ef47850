/***
 * Generate module in Wed Apr 30 2025 09:57:01 GMT+0800 (中国标准时间)
 * Don't edit this file, please!
 * https://wiki.bfdx.com/index.php/%E6%9C%BA%E5%9E%8B%E4%BF%A1%E6%81%AF
 ***/

/**
 * @typedef OneModelInfo
 * @property {string} modelName 机型名称
 * @property {string[]} modelCode 机型码列表
 * @property {(model: string) => boolean} [checkModel] 检查机型码是否匹配机型信息
 */

/**
 * 生成带"checkModel"方法的机型信息
 * @param {{[key:string]: OneModelInfo}} models
 * @returns {{[key:string]: OneModelInfo}}
 */
function generateModelCheckModelMethod(models) {
  for (const key in models) {
    const modelInfo = models[key]
    Object.assign(modelInfo, {
      checkModel: (model) => checkModel(modelInfo.modelCode, model),
    })
  }
  return models
}

/**
 * 带"checkModel"方法的机型信息集合
 * @type {{[key:string]: OneModelInfo}}
 */
const ModelInfo = generateModelCheckModelMethod({"510SDC00":{"modelName":"BF-TD510(SDC)","modelCode":["510SDC00","510R7F00"]},"510R7F00":{"modelName":"BF-TD510(SDC)","modelCode":["510SDC00","510R7F00"]},"510SVT00":{"modelName":"BF-TD510(SVT)","modelCode":["510SVT00"]},"511SDC00":{"modelName":"BF-TD511(SDC)","modelCode":["511SDC00","511S7000","511R7F00"]},"511S7000":{"modelName":"BF-TD511(SDC)","modelCode":["511SDC00","511S7000","511R7F00"]},"511R7F00":{"modelName":"BF-TD511(SDC)","modelCode":["511SDC00","511S7000","511R7F00"]},"511FR000":{"modelName":"BF-TD511(SDC)FR","modelCode":["511FR000","511FRR00"]},"511FRR00":{"modelName":"BF-TD511(SDC)FR","modelCode":["511FR000","511FRR00"]},"511SVT00":{"modelName":"BF-TD511(SVT)","modelCode":["511SVT00"]},"818SDC00":{"modelName":"BF-TD818(SDC)","modelCode":["818SDC00"]},"818SVT00":{"modelName":"BF-TD818(SVT)","modelCode":["818SVT00"]},"TD081000":{"modelName":"BF-TD800(SDC)","modelCode":["TD081000","TD081100"]},"TD081100":{"modelName":"BF-TD800(SDC)","modelCode":["TD081000","TD081100"]},"880SC100":{"modelName":"BF-TD880(SDC)","modelCode":["880SC100","880SC200"]},"880SC200":{"modelName":"BF-TD880(SDC)","modelCode":["880SC100","880SC200"]},"880SVT00":{"modelName":"BF-TD880(SVT)","modelCode":["880SVT00"]},"825SDC00":{"modelName":"BF-TM8250(SDC)","modelCode":["825SDC00"]},"8250FR00":{"modelName":"BF-TM8250(SDC)FR","modelCode":["8250FR00"]},"R82500S0":{"modelName":"BF-TM8250(SDC)R7F","modelCode":["R82500S0"]},"DP109SDC":{"modelName":"BF-TD910(SDC)","modelCode":["DP109SDC","109PSDC0","DP109R7F"]},"109PSDC0":{"modelName":"BF-TD910(SDC)","modelCode":["DP109SDC","109PSDC0","DP109R7F"]},"DP109R7F":{"modelName":"BF-TD910(SDC)","modelCode":["DP109SDC","109PSDC0","DP109R7F"]},"109PSDC1":{"modelName":"BF-TD910P(SDC)","modelCode":["109PSDC1","109PR7F1"]},"109PR7F1":{"modelName":"BF-TD910P(SDC)","modelCode":["109PSDC1","109PR7F1"]},"DP140100":{"modelName":"BF-TD920","modelCode":["DP140100"]},"930SDC00":{"modelName":"BF-TD930(SDC)","modelCode":["930SDC00"]},"R930SDC0":{"modelName":"BF-TD930(SDC)R7F","modelCode":["R930SDC0","D930SD"]},"D930SD":{"modelName":"BF-TD930(SDC)R7F","modelCode":["R930SDC0","D930SD"]},"R935SDC0":{"modelName":"BP750(SDC)","modelCode":["R935SDC0","D935SD"]},"D935SD":{"modelName":"BP750(SDC)","modelCode":["R935SDC0","D935SD"]},"930SVT00":{"modelName":"BF-TD930(SVT)","modelCode":["930SVT00"]},"R930SVT0":{"modelName":"BF-TD930(SVT)R7F","modelCode":["R930SVT0","D930SV"]},"D930SV":{"modelName":"BF-TD930(SVT)R7F","modelCode":["R930SVT0","D930SV"]},"R935SVT0":{"modelName":"BP750(SVT) ","modelCode":["R935SVT0","D935SV"]},"D935SV":{"modelName":"BP750(SVT) ","modelCode":["R935SVT0","D935SV"]},"BP660100":{"modelName":"BP660","modelCode":["BP660100"]},"D860SD00":{"modelName":"BF-BP860SDC","modelCode":["D860SD00"]},"D860SV00":{"modelName":"BF-BP860SVT","modelCode":["D860SV00"]},"R82500V0":{"modelName":"BF-TM8250(SVT)R7F","modelCode":["R82500V0"]},"BP660200":{"modelName":"BP610","modelCode":["BP660200"]},"BP660300":{"modelName":"BP620","modelCode":["BP660300"]},"TR805005":{"modelName":"BF-TR8050E","modelCode":["TR805005"]},"TR900M":{"modelName":"TR900M","modelCode":["TR900M"]},"TR90FR":{"modelName":"BF-TR900MFR","modelCode":["TR90FR"]},"RR900M":{"modelName":"BF-TR900M(R7F)","modelCode":["RR900M"]},"TR925M":{"modelName":"TR925M","modelCode":["TR925M"]},"DZ1480":{"modelName":"BF-TS908","modelCode":["DZ1480"]},"TR900L":{"modelName":"TR900L","modelCode":["TR900L"]},"TR925D":{"modelName":"TR925D","modelCode":["TR925D"]},"TR925R":{"modelName":"TR925R","modelCode":["TR925R"]},"DZ1481":{"modelName":"DZ148SVT","modelCode":["DZ1481"]},"TR900SVT":{"modelName":"TR900SVT","modelCode":["TR900SVT"]},"TR850SVT":{"modelName":"TR850SVT","modelCode":["TR850SVT"]},"TR850M":{"modelName":"BF-TR8500M","modelCode":["TR850M"]},"RR850M":{"modelName":"BF-TR8500M(R7F)","modelCode":["RR850M"]},"BR105M":{"modelName":"BR1050(SDC)","modelCode":["BR105M"]},"TR850S":{"modelName":"TR8500SVT","modelCode":["TR850S"]},"RR850S":{"modelName":"TR8500SVT","modelCode":["RR850S"]},"BR105S":{"modelName":"BR1050SVT","modelCode":["BR105S"]},"TR900S":{"modelName":"TR900SVT","modelCode":["TR900S"]},"RR900S":{"modelName":"TR900SVT","modelCode":["RR900S"]}})

/**
 * 通过机型码查找机型信息
 * @param {string} modelCode 机型码
 * @returns {OneModelInfo | undefined} 机型信息
 */
function getModel(modelCode) {
  if (!modelCode) return undefined

  const model = ModelInfo[modelCode]
  // 机型码后面2位不固定的，才需要遍历所有机型信息，调用'checkModel'来检查是否匹配机型
  if (!model) {
    for (const key in ModelInfo) {
      const data = ModelInfo[key]
      if (data.checkModel(modelCode)) {
        return data
      }
    }
  }

  return model
}

/**
 * 通用的检查机型码兼容判断方法，只要检查的机型码前6位匹配即可，后2位为功能码
 * @param {Array<string>} modelList 兼容的机型码列表
 * @param {string} model 需要检查的机型码
 * @returns {boolean}
 */
export function checkModel(modelList, model) {
  const model6bit = model.slice(0, 6)
  return modelList.some(m => m.startsWith(model6bit))
}

/**
 * 检测读频的机型是否与界面一致，一致则返回true，否则返回false
 * @param {string} modelCode: 读取终端数据的机型码
 * @param {string} expectedModel: 系统内置的机型码
 * @returns {boolean}
 */
export function checkDeviceModel(modelCode, expectedModel) {
  const model = getModel(expectedModel)
  // 查找不到机型信息，非法机型
  if (!model) {
    return false
  }

  return checkModel(model.modelCode, modelCode)
}

/**
 * 获取机型码对应的机型名称方法
 * @param {string} modelCode
 * @returns {string}
 */
export function getModelName(modelCode) {
  const model = getModel(modelCode)
  return model?.modelName ?? modelCode
}

export default {
  getModelName,
  checkModel,
  checkDeviceModel,
}
