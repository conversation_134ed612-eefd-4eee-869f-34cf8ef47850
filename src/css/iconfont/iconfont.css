@font-face {
  font-family: "iconfont"; /* Project id 398279 */
  src: url('iconfont.woff2?t=1695777874745') format('woff2'),
       url('iconfont.woff?t=1695777874745') format('woff'),
       url('iconfont.ttf?t=1695777874745') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-guijihuifang:before {
  content: "\e61f";
}

.icon-authenticationbg:before {
  content: "\e64c";
}

.icon-select-all:before {
  content: "\e6cc";
}

.icon-select-all-off:before {
  content: "\e6e8";
}

.icon-base-station:before {
  content: "\e60b";
}

.icon-iot-history:before {
  content: "\e61b";
}

.icon-iot:before {
  content: "\e689";
}

.icon-kapian:before {
  content: "\e63e";
}

.icon-yangan:before {
  content: "\e68a";
}

.icon-task-group:before {
  content: "\e628";
}

.icon-temp-group:before {
  content: "\e6bc";
}

.icon-cz-jl:before {
  content: "\e772";
}

.icon-virtual-org:before {
  content: "\e646";
}

.icon-virtual-plus:before {
  content: "\e623";
}

.icon-maximize:before {
  content: "\e66b";
}

.icon-simple-list:before {
  content: "\e617";
}

.icon-low-battery-alarm:before {
  content: "\e64d";
}

.icon-phone-transfer:before {
  content: "\e6ec";
}

.icon-hangup:before {
  content: "\e610";
}

.icon-phone-book:before {
  content: "\ecfd";
}

.icon-blackList:before {
  content: "\e66a";
}

.icon-whiteList:before {
  content: "\e60a";
}

.icon-Gateway:before {
  content: "\e938";
}

.icon-mapping:before {
  content: "\e68e";
}

.icon-software:before {
  content: "\e62c";
}

.icon-write-data:before {
  content: "\e685";
}

.icon-sms:before {
  content: "\e77b";
}

.icon-version:before {
  content: "\e68d";
}

.icon-login-user:before {
  content: "\e82b";
}

.icon-number:before {
  content: "\e661";
}

.icon-password:before {
  content: "\e62d";
}

.icon-connect:before {
  content: "\e612";
}

.icon-disconnect2:before {
  content: "\e645";
}

.icon-fullScreen:before {
  content: "\e650";
}

.icon-reduction:before {
  content: "\e6b2";
}

.icon-reload:before {
  content: "\e6d5";
}

.icon-line3:before {
  content: "\e64f";
}

.icon-line1:before {
  content: "\e8f3";
}

.icon-line2:before {
  content: "\e8f4";
}

.icon-line4:before {
  content: "\e8f5";
}

.icon-collapseAll:before {
  content: "\e616";
}

.icon-bluetooth:before {
  content: "\e61a";
}

.icon-usb:before {
  content: "\e74a";
}

.icon-signal:before {
  content: "\e705";
}

.icon-disconnect:before {
  content: "\e6b4";
}

.icon-connect2:before {
  content: "\e631";
}

.icon-emptyAll:before {
  content: "\e8f2";
}

.icon-expandAll:before {
  content: "\e619";
}

.icon-selectedAll:before {
  content: "\e803";
}

.icon-deselectAll:before {
  content: "\e703";
}

.icon-list:before {
  content: "\e615";
}

.icon-nav:before {
  content: "\e611";
}

.icon-table:before {
  content: "\e606";
}

.icon-move-car:before {
  content: "\e618";
}

.icon-data-add:before {
  content: "\e63a";
}

.icon-data-update:before {
  content: "\e6a7";
}

.icon-maikefeng:before {
  content: "\e81d";
}

.icon-about:before {
  content: "\e61e";
}

.icon-original-location:before {
  content: "\e633";
}

.icon-gps-track:before {
  content: "\e62b";
}

.icon-instruction-book:before {
  content: "\e61c";
}

.icon-address-book:before {
  content: "\e624";
}

.icon-command:before {
  content: "\e6a5";
}

.icon-network:before {
  content: "\e609";
}

.icon-dispatch-history:before {
  content: "\e62f";
}

.icon-connect-dispatch-history:before {
  content: "\e696";
}

.icon-mapmarker:before {
  content: "\e65c";
}

.icon-command-history:before {
  content: "\e715";
}

.icon-sound-history:before {
  content: "\e639";
}

.icon-notes:before {
  content: "\e676";
}

.icon-user-online:before {
  content: "\e7fc";
}

.icon-controller-online:before {
  content: "\e660";
}

.icon-line-master:before {
  content: "\e613";
}

.icon-work-shift:before {
  content: "\e614";
}

.icon-interphone:before {
  content: "\e601";
}

.icon-set-user:before {
  content: "\e663";
}

.icon-xunchajilu:before {
  content: "\e65d";
}

.icon-rules:before {
  content: "\e6cd";
}

.icon-rules-tongji:before {
  content: "\e607";
}

.icon-job:before {
  content: "\e7b2";
}

.icon-organize:before {
  content: "\e662";
}

.icon-patrol-point:before {
  content: "\e6c6";
}

.icon-controller:before {
  content: "\e691";
}

.icon-jingbaojilu:before {
  content: "\e608";
}

