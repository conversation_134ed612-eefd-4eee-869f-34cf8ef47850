$bf-direction: (
  t: top,
  r: right,
  b: bottom,
  l: left,
  x: x,
  y: y,
  n: none
);

$bf-border-color: #ddd;

.bf-border {
  border: 1px solid $bf-border-color;
}

@each $k, $v in $bf-direction {
  .bf-border-#{$k} {
    @if $v == none {
      border: unset;
    } @else if $v == y {
      border-top: 1px solid $bf-border-color;
      border-bottom: 1px solid $bf-border-color;
    } @else if $v == x {
      border-left: 1px solid $bf-border-color;
      border-right: 1px solid $bf-border-color;
    } @else {
      border-#{$v}: 1px solid $bf-border-color;
    }
  }
}
