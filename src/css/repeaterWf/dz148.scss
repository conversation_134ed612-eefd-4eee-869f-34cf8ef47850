.dz148-wrapper {
  width: auto;

  // .el-table {
  //   .el-table__cell.actions {
  //     .el-button {
  //       width: 28px;
  //       height: 28px;
  //     }
  //   }
  // }
}

.el-overlay {
  .dz148-dialog.el-dialog {
    width: 45vw;

    & > .el-dialog__body {
      height: 50vh;

      & > .el-tabs.el-tabs--left {
        .el-tabs__content {
          border-left: 2px solid var(--el-border-color);
          margin-left: -11px;
          padding-left: 11px;
        }
      }

      .actions {
        .el-button {
          min-width: 36%;
        }
      }

      .el-form .el-form-item {
        margin-bottom: unset;

        &.actions {
          .el-form-item__content {
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

              .el-input-number {
          width: 100%;
          min-width: 150px;
        }
    }

    &.is-fullscreen > .el-dialog__body {
      height: calc(100vh - 50px);
    }
  }
}
