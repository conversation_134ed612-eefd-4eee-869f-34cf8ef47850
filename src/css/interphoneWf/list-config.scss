// 列表配置公共样式
// 左侧名称列表
.label-list-container {
  overflow: auto;
}

.label-list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 4px;
  padding: 0 6px;
  line-height: 30px;
  cursor: default;

  &:not(:last-child) {
    margin-bottom: 4px;
  }
}

.label-list-item-name {
  // flex: auto;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: keep-all;
  white-space: nowrap;
  margin-right: 4px;
  min-width: 100px;
}

.label-list-item-name__hover {
   color: #66b1ff;

  .label-list-item-close {
    visibility: visible;
  }
}

.label-list-item-name__selected {
  color: #fff;
  background-color: #409eff;
}

.label-list-item-name__disToggle {
  cursor: not-allowed;
}

.label-list-item-close {
  visibility: hidden;
}

.label-new-list-item {
  // margin-top: 4px;
}

// 右侧配置表单容器样式
.list-config-container {
  flex: auto;
}

// 父级容器样式
.label-list-container-wrapper {
  display: flex;
  gap: 1em;

  .label-list {
    @extend .label-list-container;

    .list-item {
      @extend .label-list-item;

      .list-item-name {
        @extend .label-list-item-name;
      }

      .list-item-close {
        @extend .label-list-item-close;
      }

      &:hover {
        @extend .label-list-item-name__hover;
      }

      &.selected {
        @extend .label-list-item-name__selected;
      }

      &.disToggle:not(.selected) {
        @extend .label-list-item-name__disToggle;
      }
    }

    .new-list-item {
      @extend .label-new-list-item;
    }
  }

  .config-container {
    @extend .list-config-container;
  }
}
