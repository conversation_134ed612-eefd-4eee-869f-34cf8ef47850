import { utils } from 'xlsx'

// 合并子表数据
function mergeChildData(dtData) {
  const data = {
    body: [],
    header: dtData.data.header
  }

  if (dtData.child.data.length > 0) {
    dtData.child.data.forEach((child, i) => {
      const row = dtData.data.body[i]

      // 行数据
      data.body.push(row)

      // 子表数据
      if (child.length > 0) {
        // 子表表头
        data.body.push(dtData.child.header)
        child.forEach((item) => {
          data.body.push(item)
        })
        // 添加一个空行以
        data.body.push([])
      }
    })

    // 如果子表的表头长度大于行数据表头长度，则拓展行数据表头
    const childHeaderLen = dtData.child.header.length
    const dataHeaderLen = data.header.length
    if (childHeaderLen > dataHeaderLen) {
      data.header = data.header.concat(new Array(childHeaderLen - dataHeaderLen).fill(''))
    }
  } else {
    data.body = dtData.data.body
  }

  return data
}

// 生成xlsx模块需要的excel数据数组
function generateExcelData(data) {
  const output = []
  for (let i = 0; i < data.body.length; i++) {
    // row,导出的数据的每行数据
    const row = {}
    const bodyItem = data.body[i]
    // 遍历表头和数据，生成excel的行数据，
    for (let k = 0; k < data.header.length; k++) {
      const headerItem = data.header[k]
      const value = bodyItem[k]
      // 给每行设置单元格内容
      row[headerItem] = value

      // 如果是DMRID列，则添加10进制DMRID数据
      if (headerItem.includes('DMRID') && /^[0-9a-fA-F]{8}$/.test(value)) {
        row[headerItem] = value + ' / ' + parseInt(value.slice(2), 16)
      }
    }

    output.push(row)
  }

  return output
}

// excel表格名称
let exportNamePrefix = 'export'

// 监听主进程的message事件，处理需要导出的表格数据
self.addEventListener('message', (event) => {
  switch (event.data.command) {
    case 'sheetName':
      exportNamePrefix = event.data.sheetName
      break
    default:
      const data = mergeChildData(event.data)
      const output = generateExcelData(data)

      const ws = utils.json_to_sheet(output)
      const wb = utils.book_new()
      // Sheet names cannot exceed 31 chars
      utils.book_append_sheet(wb, ws, exportNamePrefix.slice(0, 31).trim())

      // 向主进程发送处理后的excel数据
      self.postMessage(wb)
  }
})
