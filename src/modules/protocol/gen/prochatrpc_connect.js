// @generated by protoc-gen-connect-es v1.2.0 with parameter "target=js+dts,import_extension=none"
// @generated from file prochatrpc.proto (package bfdx_proto, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CommonReq, CommonResp } from "./mesh_pb";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service bfdx_proto.Prochat
 */
export const Prochat = {
  typeName: "bfdx_proto.Prochat",
  methods: {
    /**
     * query prochat device list
     *
     * @generated from rpc bfdx_proto.Prochat.QueryProchatDeviceList
     */
    queryProchatDeviceList: {
      name: "QueryProchatDeviceList",
      I: CommonReq,
      O: CommonResp,
      kind: MethodKind.Unary,
    },
  }
};

