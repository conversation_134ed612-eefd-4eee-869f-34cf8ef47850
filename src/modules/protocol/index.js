import base64js from 'base64-js'
import pako from 'pako'
import Protobuf from 'protobufjs'
import bf_proto from '@/modules/protocol/bf_proto.json'
import { v1 as uuid } from 'uuid'
import bfutil from '@/utils/bfutil'
import { warningBoxWithOption } from '@/utils/notify'
import i18n from '../i18n'
import dbCmd from './db.pb.cmd'
import { merge } from 'lodash'

const Root = Protobuf.Root
const proto = Root.fromJSON(bf_proto)
export const bfdxProtoPackageName = 'bfdx_proto'
export const defPackageName = bfdxProtoPackageName
export const kcpPackageName = 'bfkcp'
export const tr925PackageName = 'tr925'
const packageNameList = [defPackageName, kcpPackageName, tr925PackageName]
const DEFAULT_TIMEOUT = 300

export const SyncDataMap = {
  db_org: [dbCmd.DB_ORG_INSERT, dbCmd.DB_ORG_UPDATE, dbCmd.DB_ORG_DELETE, dbCmd.DB_ORG_PUPDATE],
  db_user_title: [
    dbCmd.DB_USER_TITLE_INSERT,
    dbCmd.DB_USER_TITLE_UPDATE,
    dbCmd.DB_USER_TITLE_DELETE,
    dbCmd.DB_USER_TITLE_PUPDATE,
  ],
  db_user: [dbCmd.DB_USER_INSERT, dbCmd.DB_USER_UPDATE, dbCmd.DB_USER_DELETE, dbCmd.DB_USER_PUPDATE],
  db_user_privelege: [
    dbCmd.DB_USER_PRIVELEGE_INSERT,
    dbCmd.DB_USER_PRIVELEGE_UPDATE,
    dbCmd.DB_USER_PRIVELEGE_DELETE,
    dbCmd.DB_USER_PRIVELEGE_PUPDATE,
  ],
  db_device: [
    dbCmd.DB_DEVICE_INSERT,
    dbCmd.DB_DEVICE_UPDATE,
    dbCmd.DB_DEVICE_DELETE,
    dbCmd.DB_DEVICE_PUPDATE,
  ],
  db_device_channel_zone: [
    dbCmd.DB_DEVICE_CHANNEL_ZONE_INSERT,
    dbCmd.DB_DEVICE_CHANNEL_ZONE_UPDATE,
    dbCmd.DB_DEVICE_CHANNEL_ZONE_DELETE,
    dbCmd.DB_DEVICE_CHANNEL_ZONE_PUPDATE,
  ],
  db_line_point: [
    dbCmd.DB_LINE_POINT_INSERT,
    dbCmd.DB_LINE_POINT_UPDATE,
    dbCmd.DB_LINE_POINT_DELETE,
    dbCmd.DB_LINE_POINT_PUPDATE,
  ],
  db_line_master: [
    dbCmd.DB_LINE_MASTER_INSERT,
    dbCmd.DB_LINE_MASTER_UPDATE,
    dbCmd.DB_LINE_MASTER_DELETE,
    dbCmd.DB_LINE_MASTER_PUPDATE,
  ],
  db_line_detail: [
    dbCmd.DB_LINE_DETAIL_INSERT,
    dbCmd.DB_LINE_DETAIL_UPDATE,
    dbCmd.DB_LINE_DETAIL_DELETE,
    dbCmd.DB_LINE_DETAIL_PUPDATE,
  ],
  db_rfid_rule_master: [
    dbCmd.DB_RFID_RULE_MASTER_INSERT,
    dbCmd.DB_RFID_RULE_MASTER_UPDATE,
    dbCmd.DB_RFID_RULE_MASTER_DELETE,
    dbCmd.DB_RFID_RULE_MASTER_PUPDATE,
  ],
  db_map_point: [
    dbCmd.DB_MAP_POINT_INSERT,
    dbCmd.DB_MAP_POINT_UPDATE,
    dbCmd.DB_MAP_POINT_DELETE,
    dbCmd.DB_MAP_POINT_PUPDATE,
  ],
  db_controller: [
    dbCmd.DB_CONTROLLER_INSERT,
    dbCmd.DB_CONTROLLER_UPDATE,
    dbCmd.DB_CONTROLLER_DELETE,
    dbCmd.DB_CONTROLLER_PUPDATE,
  ],
  db_controller_gateway_manage: [
    dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_INSERT,
    dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_UPDATE,
    dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_DELETE,
    dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_PUPDATE,
  ],
  db_phone_short_no: [
    dbCmd.DB_PHONE_SHORT_NO_INSERT,
    dbCmd.DB_PHONE_SHORT_NO_UPDATE,
    dbCmd.DB_PHONE_SHORT_NO_DELETE,
    dbCmd.DB_PHONE_SHORT_NO_PUPDATE,
  ],
  db_phone_gateway_filter: [
    dbCmd.DB_PHONE_GATEWAY_FILTER_INSERT,
    dbCmd.DB_PHONE_GATEWAY_FILTER_UPDATE,
    dbCmd.DB_PHONE_GATEWAY_FILTER_DELETE,
    dbCmd.DB_PHONE_GATEWAY_FILTER_PUPDATE,
  ],
  db_phone_gateway_permission: [
    dbCmd.DB_PHONE_GATEWAY_PERMISSION_INSERT,
    dbCmd.DB_PHONE_GATEWAY_PERMISSION_UPDATE,
    dbCmd.DB_PHONE_GATEWAY_PERMISSION_DELETE,
    dbCmd.DB_PHONE_GATEWAY_PERMISSION_PUPDATE,
  ],
  db_phone_no_list: [
    dbCmd.DB_PHONE_NO_LIST_INSERT,
    dbCmd.DB_PHONE_NO_LIST_UPDATE,
    dbCmd.DB_PHONE_NO_LIST_DELETE,
    dbCmd.DB_PHONE_NO_LIST_PUPDATE,
  ],
}
const DBOperations = Object.keys(SyncDataMap).map(key => SyncDataMap[key]).reduce((p, c) => ([...p, ...c]), [])

export const ManualSyncOperations = ['db_user_privelege', 'db_device_channel_zone', 'db_line_detail']
  .map(key => SyncDataMap[key]).reduce((p, c) => ([...p, ...c]), [])

export default {
  __bfdx_proto_seq_no: 0,
  __bfdx_T_map: {},
  bfdx_proto_new_seq_no() {
    ++this.__bfdx_proto_seq_no
    if (this.__bfdx_proto_seq_no > 2000000) {
      this.__bfdx_proto_seq_no = 0
    }
    return this.__bfdx_proto_seq_no
  },
  // 根据消息名生成的类型对象
  bfdx_proto_msg_T(msg_name, packageName = defPackageName) {
    const proto_name = `${packageName}.${msg_name}`
    const Tmsg = proto.lookup(proto_name)
    return Tmsg
  },
  // 根据消息名生成相应的json对象
  bfdx_proto_msg_obj(msg_name, packageName = defPackageName) {
    const Tmsg = this.bfdx_proto_msg_T(msg_name, packageName)
    if (Tmsg) {
      return Tmsg.create()
    } else {
      return undefined
    }
  },
  Trpc_cmd(packageName = defPackageName) {
    switch (packageName) {
      case kcpPackageName:
      case tr925PackageName:
        return this.bfdx_proto_msg_T('rpc_cmd', kcpPackageName)
      case defPackageName:
      default:
        return this.bfdx_proto_msg_T('rpc_cmd', packageName)
    }
  },
  // new one rpc_cmd object
  bfdx_rpc_cmd_new() {
    const rpc_cmd_obj = this.Trpc_cmd().create()
    rpc_cmd_obj.reqId = uuid()
    rpc_cmd_obj.seqNo = this.bfdx_proto_new_seq_no()
    rpc_cmd_obj.sysId = bfglob.sysId
    rpc_cmd_obj.sid = bfglob.sessionId
    return rpc_cmd_obj
  },
  __serverPendingCall: {},
  bfdx_server_call(msg_subject, rpc_cmd, opts) {
    return new Promise((resolve, reject) => {
      const needManualSyncCenter = ManualSyncOperations.includes(rpc_cmd.command)
      if (needManualSyncCenter) {
        // 利用opt来传递操作的表数据的上级单位rid
        rpc_cmd.opt = opts.orgId || rpc_cmd.opt
      }

      let ssid = null
      const rpc_cmd_t = this.Trpc_cmd(opts.encodePackageName || opts.packageName)
      const rpc_cmd_bytes = rpc_cmd_t.encode(rpc_cmd).finish()
      const msg_data = base64js.fromByteArray(rpc_cmd_bytes)

      // 同一系统下客户端同步数据同步时，标记为自己发的指令
      DBOperations.includes(rpc_cmd.command) && (bfglob.cmdReqId.add(rpc_cmd.reqId))

      // 判断指令是否为没有orgId的表的操作
      if (needManualSyncCenter) {
        setTimeout(() => {
          // 没有orgId属性的数据，主动发布到特定的主题事件上
          bfglob.server.publish(`cdc.${bfglob.sysId}`, msg_data)
        }, 0)
      }

      // 服务器响应的消息事件名称
      let msg_replyTo = opts.replyTo
      if (!msg_replyTo || (msg_replyTo.length === 0)) {
        switch (opts.encodePackageName || opts.packageName) {
          case kcpPackageName:
          case tr925PackageName:
            msg_replyTo = rpc_cmd.sid
            break
          default:
            msg_replyTo = rpc_cmd.reqId
        }
      }

      const clearServerTimeout = (msg_replyTo) => {
        if (this.__serverPendingCall[msg_replyTo]) {
          clearTimeout(this.__serverPendingCall[msg_replyTo])
        }
        delete this.__serverPendingCall[msg_replyTo]
      }
      // 订阅服务器返回的响应数据
      if (typeof opts.processStreamData === 'function') {
        bfglob.server.subscribe(msg_replyTo, (msg_data, msg_reply, msg_subject, nats_ssid) => {
          clearServerTimeout(msg_replyTo)
          const rpc_cmd_obj = this.bfdx_natsMsg2rpcCmd(msg_data, opts.packageName)
          const dbListName_t = this.bfdx_proto_msg_T(opts.decodeMsgType, opts.packageName)
          rpc_cmd_obj.body = dbListName_t.decode(rpc_cmd_obj.body)
          opts.processStreamData(rpc_cmd_obj)
          if (rpc_cmd_obj.opt.endsWith('end')) {
            bfglob.server.unsubscribe(nats_ssid)
          }
        })
      } else {
        ssid = bfglob.server.subscribe(msg_replyTo, (_msg_data, msg_reply, msg_subject, nats_ssid) => {
          clearServerTimeout(msg_replyTo)
          bfglob.server.unsubscribe(nats_ssid)

          const resData = {
            data: _msg_data,
            replyTo: msg_subject,
            nsid: nats_ssid,
          }
          return resolve(resData)
        })
      }

      // 超时处理机制
      this.__serverPendingCall[msg_replyTo] = setTimeout(() => {
        if (!this.__serverPendingCall[msg_replyTo]) {
          return
        }
        delete this.__serverPendingCall[msg_replyTo]
        if (msg_replyTo && ssid) {
          bfglob.server.unsubscribe(ssid)
        }

        return reject({
          subject: msg_subject,
          replyTo: msg_replyTo,
        })
      }, (opts.timeout || DEFAULT_TIMEOUT) * 1000)

      bfglob.server.publish(msg_subject, msg_data, msg_replyTo, opts.sendCb)
    })
  },
  bfdx_natsMsg2rpcCmd(msg, packageName = defPackageName) {
    const rpc_cmd_bytes = base64js.toByteArray(msg)
    const rpc_cmd_t = this.Trpc_cmd(packageName)
    const rpc_cmd_obj = rpc_cmd_t.decode(rpc_cmd_bytes)

    if (packageName !== kcpPackageName) {
      // if (rpc_cmd_obj.reqOrResponse === 2) {
      // }
      if (rpc_cmd_obj.compressMethod === 1) {
        rpc_cmd_obj.body = rpc_cmd_obj.body.length > 0 ? pako.inflateRaw(rpc_cmd_obj.body) : []
        rpc_cmd_obj.compressMethod = 0
      }
    }

    return rpc_cmd_obj
  },
  decodeMessage(message, decodeMsgType, packageName = defPackageName) {
    const message_t = this.bfdx_proto_msg_T(decodeMsgType, packageName)
    if (!message_t) { return {} }

    const message_obj = message_t.decode(message)
    const value = this.copyFieldsFromProto(message_obj, packageName)
    return message_t.create(value)
  },
  getRpcCmdByProtoNested(packageName, command) {
    let rpc_cmd_obj
    switch (packageName) {
      case kcpPackageName:
      case tr925PackageName:
        rpc_cmd_obj = this.bfkcp_new_rpc_cmd(kcpPackageName)
        rpc_cmd_obj.cmd = command
        break
      case defPackageName:
      default:
        rpc_cmd_obj = this.bfdx_rpc_cmd_new()
        rpc_cmd_obj.command = command
        break
    }

    return rpc_cmd_obj
  },
  sendMessage(command, data, encodeMsgType, msg_subject, opts) {
    return new Promise((resolve, reject) => {
      /**
       * decodeMsgType, 需要反序列化服务器响应的rpc_cmd.body的消息类型名称
       * replyTo, 服务器响应的消息事件名称，默认是rpcId
       * timeout，超时时间
       * sendCb，发送命令成功时回调，不管服务器是否响应都执行
       * rpcCmdFields rpc_cmd消息其他额外字段对象集
       * **/
      opts = Object.assign({
        decodeMsgType: '',
        replyTo: '',
        timeout: DEFAULT_TIMEOUT,
        sendCb: () => { },
        beforeSend: (rpcCmd) => { },
      }, opts || {})

      let rpc_cmd_obj = this.getRpcCmdByProtoNested(opts.packageName, command)

      // 有序列化protobuf消息类型名称才进行序列化操作
      let msg_type, msg_obj
      if (encodeMsgType && data) {
        msg_type = this.bfdx_proto_msg_T(encodeMsgType, opts.encodePackageName || opts.packageName)
        msg_obj = msg_type.create(data)
        rpc_cmd_obj.body = msg_type.encode(msg_obj).finish()
      }

      // rpc_cmd_obj 一些特殊的字段属性
      if (opts.rpcCmdFields && opts.rpcCmdFields.toString() === '[object Object]') {
        rpc_cmd_obj = Object.assign(rpc_cmd_obj, opts.rpcCmdFields || {})
      }

      if (typeof opts.beforeSend === 'function') {
        opts.beforeSend(rpc_cmd_obj)
      }

      this.bfdx_server_call(msg_subject, rpc_cmd_obj, opts).then(res => {
        // 反序列化rpc_cmd数据
        const rpcCmdObj = this.bfdx_natsMsg2rpcCmd(res.data, opts.packageName)
        // 所有指令交互，判断是否登录失效
        if (rpcCmdObj.resInfo.includes('Err:sid not found')) {
          // 强制刷新
          warningBoxWithOption(i18n.global.t('msgbox.loginExpiredAndLoginAgain'), {
            type: 'error',
            showClose: false,
          }).then(bfutil.systemReload)
          return
        }

        // 反序列化rpc_cmd.body数据
        if (opts.decodeMsgType) {
          const packageName = packageNameList.includes(opts.packageName) ? opts.packageName : defPackageName
          rpcCmdObj.body = this.decodeMessage(rpcCmdObj.body, opts.decodeMsgType, packageName)
        }

        resolve(rpcCmdObj)
      }).catch(res => {
        reject(res)
      })
    })
  },
  bfkcp_new_rpc_cmd(packageName = kcpPackageName) {
    const bfkcp_rpc_cmd_obj = this.bfdx_proto_msg_obj('rpc_cmd', packageName)
    bfkcp_rpc_cmd_obj.seqNo = this.bfdx_proto_new_seq_no()
    bfkcp_rpc_cmd_obj.sid = bfglob.sessionId

    return bfkcp_rpc_cmd_obj
  },
  copyFieldsFromProto(from, packageName = kcpPackageName) {
    const to = {}
    if (!from) {
      return to
    }

    let source = from
    if (from.$type) {
      source = from.$type.fields
    }

    // 找到proto原型链上的字段属性对象，使用字段属性将值拷贝到目标对象上
    for (const k in source) {
      let item = from[k]
      if (item === null) {
        const fieldType = this.bfdx_proto_msg_T(source[k].type, packageName)
        if (fieldType) {
          item = typeof fieldType.create === 'function' && fieldType.create()
        } else {
          // 找不到数据协议对象源
          to[k] = item
          continue
        }
      }

      if (item !== null && typeof item === 'object' && item.$type) {
        to[k] = merge(to[k] || {}, this.copyFieldsFromProto(item))
      } else {
        to[k] = item
      }
    }

    // 把form中自定义的属性也拷贝过来
    const keys = Object.keys(from)
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      if (to[key] !== null && to[key] !== undefined) continue

      to[key] = from[key]
    }

    return to
  },
  lookupEnum(name, pkg = tr925PackageName) {
    const msgT = this.bfdx_proto_msg_T(name, pkg)
    return msgT && msgT.values ? msgT.values : {}
  },
}
