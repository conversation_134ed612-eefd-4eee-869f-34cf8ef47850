import DataManage from './dataManage'

if (!window.bfglob) {
  window.bfglob = {}
}
window.bfglob.gorgData = DataManage.orgsDataClass()
window.bfglob.gjobsData = DataManage.jobsDataClass()
window.bfglob.guserData = DataManage.usersDataClass()
window.bfglob.gimages = DataManage.imagesDataClass()
window.bfglob.gdevices = DataManage.devicesDataClass()
window.bfglob.gmapPoints = DataManage.mapPointsDataClass()
window.bfglob.glinePoints = DataManage.linePointsDataClass()
window.bfglob.glineMaster = DataManage.lineMastersDataClass()
window.bfglob.gruleMaster = DataManage.rulesDataClass()
window.bfglob.gcontrollers = DataManage.controllersDataClass()
window.bfglob.dmrAddr = DataManage.DMRIDAddressClass()
window.bfglob.gshortNo = DataManage.phoneShortNoClass()
window.bfglob.gatewayFilter = DataManage.phoneGatewayFilterClass()
window.bfglob.gatewayPermission = DataManage.phoneGatewayPermissionClass()
window.bfglob.gcontrollerGateway = DataManage.controllerGatewayClass()
window.bfglob.gphoneBook = DataManage.phoneNoListClass()
window.bfglob.gchannelZone = DataManage.DeviceChannelZoneClass()
window.bfglob.gdynamicGroupDetail = DataManage.DynamicGroupDetailClass()
window.bfglob.giotDevices = DataManage.IOTDevicesClass()
window.bfglob.gapppMapPivilegeDevice = DataManage.AppMapPrivilegeDeviceDataClass()

// 没有权限的数据
window.bfglob.noPermOrgData = new DataManage.NoPermOrg()
window.bfglob.noPermDevData = new DataManage.NoPermDevice()

export default null
