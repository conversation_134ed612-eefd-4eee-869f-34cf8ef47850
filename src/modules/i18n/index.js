import bfStorage from '@/utils/storage'
import { createI18n } from 'vue-i18n'
import { BASE_URL } from '@/envConfig'

export const SupportedLang = {
  zhCN: 'zh-cn',
  enUS: 'en',
  fr: 'fr',
}

const BuiltinLanguages = new Set(Object.values(SupportedLang))

// 合并言语配置，主要处理默认值
export const languageConfig = (() => {
  const languages = bfglob.languages
  languages.default = languages.default || window.navigator.language
  languages.supports = languages.supports ?? []
  languages.displayLabels = languages.displayLabels ?? []
  return languages
})()

export const DisplayLanguageLabels = { 'zh-cn': '简体中文', en: 'English', fr: 'Français' }

export function getDisplayLabel(lang) {
  return DisplayLanguageLabels[lang] ?? lang
}

for (let i = 0; i < languageConfig.supports.length; i++) {
  const lang = languageConfig.supports[i]
  if (!lang) continue

  if (!BuiltinLanguages.has(lang)) {
    SupportedLang[lang] = lang
  }

  const displayLabel = languageConfig.displayLabels[i]
  if (displayLabel) {
    DisplayLanguageLabels[lang] = displayLabel
  }
}
export const SupportedLangList = [...new Set([...BuiltinLanguages, ...languageConfig.supports])]

const DefaultLocale = getNavigatorLang()
const UserDefaultLocale = SupportedLangList.includes(languageConfig.default)
  ? languageConfig.default
  : SupportedLangList[0]
const defaultLocale = UserDefaultLocale ?? DefaultLocale

const i18n = createI18n({
  locale: defaultLocale, // 默认语言
  fallbackLocale: SupportedLang.enUS, // 回退语言，当当前语言中没有对应的翻译时，使用回退语言
  messages: {},
  globalInjection: true, // 全局注入 $t 函数
})

export default i18n

// our default language that is prelaoded
const loadedLanguages = []

export function setI18nLanguage(lang) {
  i18n.global.locale = lang
  document.title = bfglob.siteConfig.siteTitle || i18n.global.t('header.siteTitle')
  return lang
}

// 为了能自动适配多个国家语言，打包时不再指定特定的语言
const ElLangsCtx = import.meta.glob([
  '/node_modules/element-plus/lib/locale/lang/zh-cn.js',
  '/node_modules/element-plus/lib/locale/lang/en.js',
  '/node_modules/element-plus/lib/locale/lang/fr.js'
])

function loadLangPack(lang) {
  return new Promise((resolve, reject) => {
    // 同时加载应用自身的语言包和Element Plus的语言包
    Promise.all([
      fetch(`${BASE_URL}langs/${lang}.json?${Date.now()}`).then(res => res.json()),
      ElLangsCtx[`/node_modules/element-plus/lib/locale/lang/${lang}.js`]()
    ])
      .then(([appMessages, elModule]) => {
        const elLang = elModule.default || elModule

        // 合并Element Plus和应用的语言包
        i18n.global.setLocaleMessage(lang, {
          ...(elLang.default || elLang),
          ...appMessages,
        })

        loadedLanguages.push(lang)
        resolve(setI18nLanguage(lang))
      })
      .catch(err => {
        bfglob.console.error('Failed to load language pack:', err)
        reject(err)
      })
  })
}

export function loadLanguageAsync(_lang, _isInit = false) {
  const lang = fixLang(_lang)

  // 如果是初始化或切换到新语言
  if (_isInit || i18n.global.locale !== lang) {
    // 如果语言包未加载，则加载新语言包
    if (!loadedLanguages.includes(lang)) {
      return loadLangPack(lang)
    }
    // 如果已加载，直接切换语言
    return Promise.resolve(setI18nLanguage(lang))
  }

  return Promise.resolve(lang)
}

// 当前只支持中文、英文
export function fixLang(lang) {
  // // 兼容旧的cn缓存
  // if (lang === SupportedLang.zhCN || lang === 'cn') {
  //   return SupportedLang.zhCN
  // }
  //
  // lang = lang.split('-')[0]
  // if (SupportedLangList.includes(lang)) {
  //   return lang
  // }
  //
  // return SupportedLang.enUS
  return lang
}

function getNavigatorLang() {
  return fixLang(window.navigator.language)
}

try {
  // 读取系统缓存账号信息
  let account = bfStorage.getItem('bfdx_account')
  if (account) {
    window.bfaccount = account = JSON.parse(account)
  }

  // 用户缓存的语言已经不在支持列表中，则使用默认的语言
  let lang = account?.locale ?? defaultLocale
  if (!SupportedLangList.includes(lang)) {
    lang = defaultLocale
  }

  loadLanguageAsync(lang, true)
} catch (e) {
  bfglob.console.error('init browser language error:', e)
  loadLanguageAsync(defaultLocale, true)
}
