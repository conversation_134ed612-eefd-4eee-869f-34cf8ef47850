<template>
  <el-dialog
    v-model="visible"
    :title="dlgTitle"
    :close-on-click-modal="false"
    v-bfdirective.max.drag
    :close-on-press-escape="false"
    :modal="false"
    :fullscreen="fullscreen"
    top="0"
    class="low-battery-alarm"
    @close="closeDlgFn"
    @open="openDlgFn"
  >
    <dataTablesVue3
      ref="dataTable"
      :head="dtHead"
      :data="tbody"
      :name="tableName"
      :exportNamePrefix="dlgTitle"
    />
  </el-dialog>
</template>

<script>
  import bfutil from '@/utils/bfutil'

  import { SupportedLang } from '@/modules/i18n'
  import dataTablesVue3 from '@/components/common/dataTablesVue3.vue'

  const ComponentName = 'lowBatteryAlarm'
  const MinimizeSubject = `open_${ComponentName}`

  export default {
    name: ComponentName,
    data() {
      return {
        bfmaxi: false,
        bfmini: {
          tooltip: 'nav.activePointLowVoltageAlarm',
          icon: 'icon-low-battery-alarm',
          subject: MinimizeSubject,
        },
        visible: false,
        tableName: ComponentName,
        linePoints: bfutil.objToArray(bfglob.glinePoints.getAll()),
        alarmList: [],
      }
    },
    methods: {
      openDlgFn() {
        // 发布关闭最小化导航区对应按钮事件
        bfglob.emit('closeMiniNavBtn', MinimizeSubject)
        bfutil.columnsAdjust(this)
      },
      closeDlgFn() {
        bfutil.closeDialogCallback(this)
        this['isMini'] = true
        bfglob.emit('minusEvent', this.bfmini)
      },
      minimizeSubject() {
        this.visible = true
        this.openDlgFn()
      },
    },
    mounted() {
      bfglob.on(MinimizeSubject, this.minimizeSubject)
    },
    components: {
      dataTablesVue3,
    },
    watch: {
      fullscreen: {
        handler(newVal) {
          bfglob.emit(this.tableName, newVal)
        },
      },
    },
    computed: {
      fullscreen() {
        return this.bfmaxi ? true : !(this.$root.layoutLevel > 0)
      },
      locale() { return this.$i18n.locale },
      isFR() { return this.locale === SupportedLang.fr },
      isEN() { return this.locale === SupportedLang.enUS },
      dtHead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          // {
          //   title: this.$t('dialog.serialNo'),
          //   data: 'pointId',
          //   width: '100px'
          // },
          {
            title: this.$t('dialog.pointName'),
            data: 'pointName',
            width: this.isFR ? '160px' : this.isEN ? '130px' : '100px',
          },
          {
            title: this.$t('dialog.pointRfid'),
            data: 'pointRfid',
            width: this.isFR ? '160px' : this.isEN ? '130px' : '100px',
          },
          {
            title: this.$t('dataTable.alarmTime'),
            width: '130px',
            render: (data, type, row, meta) => {
              return row.lastAlarmTime || ''
            },
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.activePointLowVoltageAlarm')
      },
      points() {
        return this.linePoints.filter((point) => {
          return point.pointType === 2
        })
      },
      tbody() {
        return this.points.filter((point) => {
          return Object.keys(this.alarmList).includes(point.pointRfid)
        })
      },
    },
    beforeUnmount() {
      bfglob.off(MinimizeSubject, this.minimizeSubject)
    },
  }

</script>

<style></style>
