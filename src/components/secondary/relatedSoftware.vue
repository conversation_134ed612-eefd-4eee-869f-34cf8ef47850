<template>
  <el-main
    class="text-center related-software"
    v-html="softwareHtml"
  />
</template>

<script>

  let lastRequestTime = 0
  const requestSoftwareInterval = 10 * 60 * 1000

  export default {
    name: 'RelatedSoftware',
    data() {
      return {
        softwareHtml: '',
      }
    },
    methods: {
      getSoftwareHtml() {
        const softwareUrl = '/bf8100/assets/software/'
        fetch(`${softwareUrl}?${Date.now()}`)
          .then(res => res.text())
          .then(htmlStr => {
            lastRequestTime = Date.now()
            this.softwareHtml = htmlStr.replace(/href="/ig, 'download href="' + softwareUrl)
            return this.softwareHtml
          })
          .catch(err => bfglob.console.error(this.$t('software.refreshFailed')))
      },
    },
    activated() {
      // 切换页面时，如果间隔小于10分钟，则不再请求数据
      if (Date.now() - lastRequestTime >= requestSoftwareInterval) {
        this.getSoftwareHtml()
      }
    },
  }
</script>

<style lang="scss">
  .related-software {
    a {
      color: #007bff;
    }
  }
</style>
