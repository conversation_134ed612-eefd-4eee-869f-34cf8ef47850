<template>
  <el-dialog
    v-model="sipGatewayVisible"
    :title="$t('dialog.sipGatewayConfig')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="header-border footer-border phone-gateway-info-dialog"
    @close="onClose"
  >
    <el-form
      ref="formData"
      :model="formData"
      :rules="rules"
      label-width="130px"
      :validate-on-rule-change="false"
      class="grid grid-cols-1 controller-sip-gateway-config"
    >
      <el-form-item>
        <el-checkbox
          v-model="formData.runBy8100"
          class="sip-runBy8100"
          @change="runBy8100Change"
        >
          {{ $t('dialog.sipProxyGateway') }}
        </el-checkbox>
      </el-form-item>
      <el-form-item
        ref="hostItem"
        prop="host"
        class="form-item-ellipsis"
      >
        <template #label>
          <span :title="$t('dialog.sipDomain')">{{ $t('dialog.sipDomain') }}</span>
        </template>
        <el-input
          v-model="formData.host"
          maxlength="56"
        />
      </el-form-item>
      <el-form-item
        ref="portItem"
        :label="$t('dialog.sipPort')"
        prop="port"
      >
        <el-input-number
          v-model.number="formData.port"
          :min="0x01"
          :max="0xFFFF"
          step-strictly
        />
      </el-form-item>
      <el-form-item
        :label="$t('dialog.sipNo')"
        prop="sipNo"
      >
        <el-input
          v-model="formData.sipNo"
          :maxlength="16"
        />
      </el-form-item>
      <el-form-item
        :label="$t('dialog.sipPassword')"
        prop="password"
      >
        <el-input
          v-model="formData.password"
          type="password"
          show-password
          :maxlength="16"
        />
      </el-form-item>
      <el-form-item
        prop="soundLocale"
        class="form-item-ellipsis"
      >
        <template #label>
          <span :title="$t('dialog.sipSoundLocale')">{{ $t('dialog.sipSoundLocale') }}</span>
        </template>
        <el-select
          v-model="formData.soundLocale"
          :placeholder="$t('dialog.select')"
          filterable
          clearable
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="item in soundLocaleTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="text-center actions">
        <el-button
          type="primary"
          class="w-32"
          @click="onConfirm"
          v-text="$t('dialog.confirm')"
        />
        <el-button
          type="warning"
          class="w-32"
          @click="onReset"
          v-text="$t('dialog.reset')"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script>
  import validateRules from '@/utils/validateRules'
  import { SoundLocales, DefaultFormData, DefaultFormData as DefaultSipFormData } from './common'

  export default {
    name: 'SipGatewaySettings',
    emits: ['update:modelValue', 'update:visible'],
    props: {
      visible: {
        type: Boolean,
        required: true,
      },
      modelValue: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        formData: {
          ...DefaultFormData,
        },
      }
    },
    methods: {
      runBy8100Change(value) {
        // 重置表单校验规则
        if (!value) {
          this.$refs.formData?.clearValidate()
        }
      },
      onClose() {
        this.sipGatewayVisible = false
      },
      async validate() {
        return await this.$refs.formData.validate().catch(() => false)
      },
      async onConfirm() {
        const valid = await this.validate()
        if (!valid) {
          return
        }

        this.$emit('update:modelValue', this.formData)
        this.onClose()
      },
      onReset() {
        this.formData = {
          ...DefaultSipFormData,
        }
        this.$nextTick(() => {
          this.$refs.formData?.clearValidate()
        })
      },
    },
    computed: {
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      sipGatewayVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      rules() {
        if (!this.formData.runBy8100) {
          return {}
        }

        return {
          sipNo: [
            validateRules.required(),
            validateRules.mustNumber(),
          ],
          password: [
            validateRules.required(),
          ],
          host: [validateRules.required(), validateRules.checkHost()],
          port: [validateRules.required()],
          // port: [validateRules.required(), validateRules.range('blur', 1, 0xFFFF)],
          soundLocale: [
            validateRules.required(),
          ],
        }
      },
      soundLocaleTypes() {
        return [
          { label: this.$t('header.CN'), value: SoundLocales.ZH },
          { label: this.$t('header.EN'), value: SoundLocales.EN },
        ]
      },
    },
    watch: {
      modelValue: {
        handler(val) {
          this.formData = val
        },
        immediate: true,
        deep: true,
      },
    },
  }
</script>

<style lang='scss'>
  .el-dialog.phone-gateway-info-dialog {
    width: 520px;
  }
</style>
