<template>
  <el-dialog
    v-model="visible"
    :title="$t('repeaterStatus.repeaterStatus')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    class="repeater-status-dialog"
    append-to-body
    @open="onOpen"
    @close="onClose"
  >
    <div class="repeater-info">
      <div class="id-info">
        {{ repeaterIdLabel }}
      </div>
      <div class="update-time">
        <span>{{ $t('repeaterStatus.updateTime') }}: {{ receiveTimeLabel }}</span>
        <span>
          <el-button
            circle
            type="primary"
            icon="refresh"
            :disabled="isDisableRefresh"
            :title="$t('software.refresh')"
            @click="refreshStatus"
          />
        </span>
      </div>
    </div>

    <el-descriptions
      title=""
      border
      :column="2"
      contentClassName="repeater-descriptions-content"
      labelClassName="repeater-descriptions-label"
    >
      <el-descriptions-item
        label="DMRID"
        :span="2"
      >
        {{ dmrIdLabel }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.channel')">
        {{ repeaterStatus.channelId + 1 }} {{ $t('dialog.channel') }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.receiveFrequency')">
        {{ rxFrequency }} Mhz
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.transFrequency')">
        {{ txFrequency }} Mhz
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.power')">
        {{ repeaterStatus.powerValue }} W
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dataTable.IPAddress')">
        {{ ipAddr }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('repeaterStatus.voltage')">
        {{ volValue }} V
      </el-descriptions-item>
      <el-descriptions-item :label="$t('repeaterStatus.temperature')">
        {{ tmpValue }} ℃
      </el-descriptions-item>
      <el-descriptions-item :label="$t('repeaterStatus.antValue')">
        {{ antValue }}
      </el-descriptions-item>
    </el-descriptions>

    <section
      class="repeater-status-container"
      :class="{ 'is-zhCN': isCN }"
    >
      <div
        v-for="status in repeaterStatusList"
        :key="status.label"
        class="repeater-status-item"
      >
        <div
          class="status-icon"
          :class="status.color"
          :title="status.tooltip"
        >
          <i :class="status.icon" />
        </div>
        <div class="status-label">
          {{ status.label }}
        </div>
      </div>
    </section>
  </el-dialog>
</template>

<script>
  import { formatDmrIdLabel, toHexDmrId, frequencyHz2Mhz } from '@/utils/bfutil'
  import { formatTime } from '@/utils/time'

  /** @type {null|number} */
  let refreshTimer = null
  let disconnectTime = 0
  const timeInterval = 30 * 1000

  export default {
    name: 'RepeaterStatusMonitor',
    emits: ['update:modelValue', 'refresh'],
    props: {
      modelValue: {
        type: Boolean,
        required: true,
      },
      repeaterStatus: {
        type: Object,
        required: true,
      },
      lastReceiveTime: {
        type: Number,
        default: 0,
      }
    },
    data() {
      return {
        isConnected: bfglob.server?.wasConnected ?? false,
      }
    },
    methods: {
      /**
       * 服务器状态变更事件，断开或已连接
       * @param {boolean} isConnected
       */
      serverStatusChanged(isConnected) {
        this.isConnected = isConnected

        // 标记断开时间
        if (!isConnected) {
          disconnectTime = Date.now()
          return
        }

        // 服务器断开后重新连接的时间间隔大于30s，则请求一次
        if (Date.now() - disconnectTime >= timeInterval) {
          this.refreshStatus()
        }
      },
      refreshStatus() {
        // 非法id，不再请求，初始化时可能出现
        if (this.repeaterStatus.deviceDmrid <= 0) return
        this.$emit('refresh', this.dmrId)
      },
      clearRefreshTimer() {
        clearTimeout(refreshTimer)
        refreshTimer = null
      },
      // 30s查询一次
      createRefreshTimer() {
        this.refreshStatus()
        const run = (ms) => {
          refreshTimer = setTimeout(() => {
            // 如果中继主动上报距离现在的时间小于定时器时间的一半, 则跳过本次查询
            const now = Date.now()
            const timeDiff = now - this.lastReceiveTime
            if (timeDiff <= timeInterval / 2) {
              run(timeInterval - timeDiff)
              return
            }

            // 如果服务器已经断开，则不再请求
            if (this.isConnected) {
              this.refreshStatus()
            }
            run(timeInterval)
          }, ms)
        }

        run(timeInterval)
      },
      onClose() {
        this.clearRefreshTimer()
      },
      onOpen() {
        // 先清除上一个定时器
        this.clearRefreshTimer()
        // 重新定时查询状态
        this.createRefreshTimer()
      },
    },
    computed: {
      // 是否禁用刷新按钮，如果服务器断开或者控制器不在线都禁用
      isDisableRefresh() {
        return (!this.isConnected || this.controller?.ctrlStats !== 1)
      },
      isCN() {
        return this.$i18n.locale === 'zh-cn'
      },
      controller() {
        return bfglob.gcontrollers.getDataByIndex(this.dmrId)
      },
      orgData() {
        const orgId = this.controller?.orgId
        return bfglob.gorgData.get(orgId)
      },
      repeaterIdLabel() {
        // 找不到控制器,则不显示
        const controllerName = this.controller?.selfId
        if (!controllerName) return ''
        // 找不到单位数据, 则显示控制器名称
        if (!this.orgData || !this.orgData.orgShortName) return controllerName
        // 组合显示单位及控制器名称
        return `${this.orgData.orgShortName} / ${controllerName}`
      },
      receiveTimeLabel() {
        return this.lastReceiveTime > 0 ? formatTime(this.lastReceiveTime) : this.$t('dialog.nothing')
      },
      visible: {
        get() {
          return this.modelValue
        },
        set(value) {
          this.$emit('update:modelValue', value)
        },
      },
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      dmrId() {
        return toHexDmrId(this.repeaterStatus.deviceDmrid, false)
      },
      dmrIdLabel() {
        return formatDmrIdLabel(this.dmrId)
      },
      rxFrequency() {
        return frequencyHz2Mhz(this.repeaterStatus.rxFrequency) || 0
      },
      txFrequency() {
        return frequencyHz2Mhz(this.repeaterStatus.txFrequency) || 0
      },
      ipAddr() {
        return `${this.repeaterStatus.ipAddr >>> 24}.${(this.repeaterStatus.ipAddr & 0xFF0000) >>> 16}.${(this.repeaterStatus.ipAddr & 0xFF00) >>> 8}.${this.repeaterStatus.ipAddr & 0xFF}`
      },
      volValue() {
        // 将毫伏转换为伏 1V=10**3mV
        const vol = this.repeaterStatus.volValue / 1000
        return vol === 0 ? vol + '' : `${vol.toFixed(1)}`
      },
      tmpValue() {
        // 转换正常值，被放大了10倍，摄氏度
        const vol = this.repeaterStatus.tmpValue / 10
        return vol === 0 ? vol + '' : `${vol.toFixed(1)}`
      },
      antValue() {
        // //驻波值,单位 0.1
        const val = this.repeaterStatus.antValue / 10
        return val === 0 ? val + '' : `${val.toFixed(1)}`
      },

      commonErrLabel() {
        return {
          0: this.$t('repeaterWriteFreq.normal'),
          1: this.$t('repeaterWriteFreq.abnormal'),
        }
      },
      volStatus() {
        const icons = {
          0: 'mdi mdi-battery',
          1: 'mdi mdi-battery-arrow-up',
          2: 'mdi mdi-battery-arrow-down-outline',
        }
        const colors = {
          0: 'text-success',
          1: 'text-danger',
          2: 'text-warning',
        }
        const labels = {
          0: this.$t('repeaterWriteFreq.normal'),
          1: this.$t('repeaterWriteFreq.tooHigh'),
          2: this.$t('repeaterWriteFreq.tooLow'),
        }
        return {
          icon: icons[this.repeaterStatus.volErr] ?? icons[1],
          color: colors[this.repeaterStatus.volErr] ?? colors[1],
          tooltip: labels[this.repeaterStatus.volErr] ?? this.commonErrLabel[1],
          label: this.$t('repeaterWriteFreq.state.volStatus'),
        }
      },
      tmpStatus() {
        const icons = {
          0: 'mdi mdi-thermometer',
          1: 'mdi mdi-thermometer-chevron-up',
          2: 'mdi mdi-thermometer-chevron-down',
        }
        const colors = {
          0: 'text-success',
          1: 'text-danger',
          2: 'text-warning',
        }
        const labels = {
          0: this.$t('repeaterWriteFreq.normal'),
          1: this.$t('repeaterWriteFreq.tooHigh'),
          2: this.$t('repeaterWriteFreq.tooLow'),
        }
        return {
          icon: icons[this.repeaterStatus.tmpErr] ?? icons[1],
          color: colors[this.repeaterStatus.tmpErr] ?? colors[1],
          tooltip: labels[this.repeaterStatus.tmpErr] ?? this.commonErrLabel[1],
          label: this.$t('repeaterWriteFreq.state.tempStatus'),
        }
      },
      gpsStatus() {
        const icons = {
          0: 'mdi mdi-crosshairs-off',
          1: 'mdi mdi-crosshairs',
          2: 'mdi mdi-crosshairs-gps',
        }
        const colors = {
          0: 'text-info',
          1: 'text-warning',
          2: 'text-success',
        }
        const labels = {
          0: this.$t('repeaterStatus.notInstalled'),
          1: this.$t('repeaterStatus.notSynced'),
          2: this.$t('repeaterStatus.synced'),
        }
        return {
          icon: icons[this.repeaterStatus.gpsErr] ?? icons[1],
          color: colors[this.repeaterStatus.gpsErr] ?? colors[1],
          tooltip: labels[this.repeaterStatus.gpsErr] ?? labels[1],
          label: this.$t('repeaterStatus.gpsStatus'),
        }
      },
      fanStatus() {
        const icons = {
          0: 'mdi mdi-fan',
          1: 'mdi mdi-fan-alert',
        }
        const colors = {
          0: 'text-success',
          1: 'text-danger',
        }
        return {
          icon: icons[this.repeaterStatus.fanErr] ?? icons[1],
          color: colors[this.repeaterStatus.fanErr] ?? colors[1],
          tooltip: this.commonErrLabel[this.repeaterStatus.fanErr] ?? this.commonErrLabel[1],
          label: this.$t('repeaterWriteFreq.state.fanStatus'),
        }
      },
      rxPllStatus() {
        const icons = {
          0: 'mdi mdi-message',
          1: 'mdi mdi-message-lock',
        }
        const colors = {
          0: 'text-success',
          1: 'text-danger',
        }
        return {
          icon: icons[this.repeaterStatus.rxPllErr] ?? icons[1],
          color: colors[this.repeaterStatus.rxPllErr] ?? colors[1],
          tooltip: this.commonErrLabel[this.repeaterStatus.rxPllErr] ?? this.commonErrLabel[1],
          label: this.$t('repeaterWriteFreq.state.rxStatus'),
        }
      },
      txPllStatus() {
        const icons = {
          0: 'mdi mdi-send',
          1: 'mdi mdi-send-lock',
        }
        const colors = {
          0: 'text-success',
          1: 'text-danger',
        }
        return {
          icon: icons[this.repeaterStatus.txPllErr] ?? icons[1],
          color: colors[this.repeaterStatus.txPllErr] ?? colors[1],
          tooltip: this.commonErrLabel[this.repeaterStatus.txPllErr] ?? this.commonErrLabel[1],
          label: this.$t('repeaterWriteFreq.state.txStatus'),
        }
      },
      antStatus() {
        const icons = {
          0: 'mdi mdi-antenna',
          1: 'mdi mdi-antenna',
        }
        const colors = {
          0: 'text-success',
          1: 'text-danger',
        }
        return {
          icon: icons[this.repeaterStatus.antErr] ?? icons[1],
          color: colors[this.repeaterStatus.antErr] ?? colors[1],
          tooltip: this.commonErrLabel[this.repeaterStatus.antErr] ?? this.commonErrLabel[1],
          label: this.$t('repeaterWriteFreq.state.antStatus'),
        }
      },
      signalStatus() {
        const icons = {
          0: 'mdi mdi-signal',
          1: 'mdi mdi-signal-off',
        }
        const colors = {
          0: 'text-success',
          1: 'text-danger',
        }
        const labels = {
          0: this.$t('dialog.nothing'),
          1: this.$t('repeaterStatus.thereIsInterference'),
        }

        return {
          icon: icons[this.repeaterStatus.signal] ?? icons[1],
          color: colors[this.repeaterStatus.signal] ?? colors[1],
          tooltip: labels[this.repeaterStatus.antErr] ?? labels[1],
          label: this.$t('repeaterStatus.signalInterference'),
        }
      },
      repeaterStatusList() {
        return [
          this.volStatus,
          this.tmpStatus,
          this.gpsStatus,
          this.fanStatus,
          this.rxPllStatus,
          this.txPllStatus,
          this.antStatus,
          this.signalStatus,
        ]
      },
    },
    watch: {
      // 监听中继DMRID变更，在窗口打开状态下，应该重新请求中继的状态参数
      'repeaterStatus.deviceDmrid'() {
        if (!this.visible || !this.isConnected) return
        this.refreshStatus()
      },
    },
    beforeMount() {
      bfglob.on('serverStatus', this.serverStatusChanged)
    },
    beforeUnmount() {
      bfglob.off('serverStatus', this.serverStatusChanged)
    },
  }
</script>

<style scoped lang="scss">
  .repeater-status-dialog {
    width: 560px;

    .text-primary {
      color: #409eff !important;
    }

    .text-info {
      color: #909399 !important;
    }

    .text-success {
      color: #67c23a !important;
    }

    .text-warning {
      color: #e6a23c !important;
    }

    .text-danger {
      color: #f56c6c !important;
    }

    .repeater-status-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.35rem;
      justify-content: center;
      margin-top: 10px;

      .repeater-status-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.25rem;
        width: 20%;
        word-break: break-word;
        text-align: center;

        .status-icon {
          font-size: 1.5rem;
        }

        .status-label {
          height: 42px;
        }
      }

      &.is-zhCN {
        .repeater-status-item {
          .status-label {
            height: auto;
          }
        }
      }
    }

    .repeater-descriptions-label {
      word-break: break-word;
      width: 120px;
    }

    .repeater-descriptions-content {
      word-break: break-word;
      width: 140px;
    }

    .repeater-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 0.35rem;
      line-height: 1.5;
      max-height: 48px;
      margin-bottom: 10px;

      .update-time {
        display: flex;
        gap: 6px;
        align-items: center;
        word-break: break-word;
      }

      .el-button--mini.is-circle {
        padding: 5px;
      }
    }
  }
</style>
