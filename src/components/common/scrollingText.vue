<template>
  <section
    ref="scrollWrap"
    class="title-wrap"
  >
    <h3
      ref="scrollText"
      class="scrolling-text"
      v-text="title"
    />
  </section>
</template>

<script>
  export default {
    name: 'ScrollingText',
    props: {
      title: {
        type: String,
        default: ''
      },
      scrollStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        scrollTimer: null
      }
    },
    methods: {
      stop() {
        clearInterval(this.scrollTimer)
        this.setPositionProp({
          target: this.$refs.scrollText
        })
      },
      start() {
        if (!this.canScrollTitle) {
          return
        }
        this.move()
      },
      setPositionProp({ target, prop = 'left', value = 'unset' }) {
        if (!target) {
          return
        }
        target.style[prop] = value
      },
      move() {
        const scrollWrap = this.$refs.scrollWrap
        const scrollText = this.$refs.scrollText
        if (!scrollWrap || !scrollText) {
          this.stop()
          return
        }

        const initLeft = () => {
          this.setPositionProp({
            target: scrollText,
            value: `${scrollWrap.offsetWidth}px`
          })
        }
        initLeft()

        // 当前标题所滚动的值
        let left = 0
        // 总共需要滚动的值
        const offsetWidth = scrollText.offsetWidth

        const _move = () => {
          if (left - offsetWidth < scrollWrap.offsetWidth) {
            left += 5
            this.setPositionProp({
              target: scrollText,
              value: `${scrollWrap.offsetWidth - left}px`
            })
          } else {
            initLeft()
            left = 0
          }
        }

        this.scrollTimer = setInterval(_move, 150)
      }
    },
    mounted() {
      this.start()
    },
    watch: {
      canScrollTitle(val) {
        if (val) {
          this.start()
        } else {
          this.stop()
        }
      }
    },
    computed: {
      canScrollTitle() {
        return this.scrollStatus && this.title && this.layout >= 2
      },
      layout() {
        return this.$root.layoutLevel
      }
    }
  }
</script>

<style lang='scss'>
  .title-wrap {
    display: flex;
    justify-content: center;
    position: relative;

    .scrolling-text {
      width: max-content;
      height: 100%;
      line-height: 40px;
      position: absolute;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 2px;
      margin: 0;
      padding: 0;
    }
  }
</style>
