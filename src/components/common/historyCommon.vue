<template>
  <el-main>
    <el-form
      ref="query"
      :model="query"
      :label-position="isMobile ? 'left' : 'top'"
      :label-width="formLabelWidth"
      :rules="rules"
      class="hisComForm grid grid-cols-1 gap-1 lg:grid-cols-7 2xl:grid-cols-6 4xl:grid-cols-10 lg:gap-4 2xl:gap-4 4xl:gap-4"
    >
      <el-form-item
        :label="$t('dialog.startTime')"
        prop="startTime"
        class="date-picker-item"
      >
        <el-date-picker
          v-model="query.startTime"
          type="datetime"
          :format="timeFormat"
          :clearable="false"
          @change="startTimeChanged"
        />
      </el-form-item>
      <el-form-item
        :label="$t('dialog.endTime')"
        prop="endTime"
        class="date-picker-item"
      >
        <el-date-picker
          v-model="query.endTime"
          type="datetime"
          :format="timeFormat"
          :clearable="false"
          @change="endTimeChanged"
        />
      </el-form-item>
      <template v-if="isExpandFormItem">
        <slot name="optionsFormItem" />
      </template>
      <div class="btnGroup flex justify-end md:justify-start">
        <div
          class="flex-shrink-0 relative"
          :style="isMobile ? { width: formLabelWidth } : {}"
        >
          <el-button
            v-if="isMobile"
            :class="['expandBtn', cmd === 56 ? 'absolute bottom-0 left-0' : '']"
            circle
            icon="more"
            @click="isexpand = !isexpand"
          />
        </div>
        <slot name="queryBtn">
          <el-button
            type="primary"
            class="queryBtn mdx:flex-shrink mdx:flex-grow"
            @click="queryFunc"
            v-text="$t('nav.enquiry')"
          />
        </slot>
      </div>
    </el-form>
    <template v-if="$slots['not-confirm-sms']">
      <slot name="not-confirm-sms" />
    </template>
    <template v-else>
      <dataTablesVue3
        :key="name"
        ref="datatable"
        :name="name"
        :head="head"
        :data="dataTableBody"
        :order="order"
        :detailHead="detailHead"
        :detailBodyName="detailBodyName"
        :exportNamePrefix="exportNamePrefix"
        :buttons="buttons"
        :columnDefs="columnDefs"
        :detailBodyIndex="detailBodyIndex"
        :edit="edit"
        :detailRender="detailRender"
        :getDetailData="getDetailData"
        :historyTable="historyTable"
        :scrollY="scrollY"
      />
    </template>
  </el-main>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'
  import bfTime, { utcToLocalTime } from '@/utils/time'
  import bfNotify from '@/utils/notify'
  import { v1 as uuid } from 'uuid'
  import bfproto from '@/modules/protocol'
  import dataTablesVue3 from '@/components/common/dataTablesVue3.vue'

  let bodyCache = []
  let notConfirmTableBody = []

  export default {
    name: 'HistoryCommon',
    emits: ['removeDataTableData', 'query-finish-proxyCurdDetail', 'query-finish-sound', 'clearDt', 'data-change'],
    components: {
      dataTablesVue3
    },
    mixins: [vueMixin],
    props: {
      dbListName: {
        type: String,
        required: true,
      },
      cmd: {
        type: Number,
        required: true,
      },
      //表单宽度
      labelWidth: {
        type: String,
        default: '',
      },
      //是否需要自定义时间格式
      rule: {
        type: Boolean,
        default: false
      },
      // 终端设备名称id
      deviceRid: {
        type: String
      },
      //用户名称id
      userRid: {
        type: String
      },
      //巡查点
      pointRid: {
        type: String
      },
      point: {
        type: Boolean,
        default: false,
      },
      //规则名称
      ruleRid: {
        type: String
      },
      //巡查线路
      lineRid: {
        type: String
      },
      deviceDmrId: {
        type: String
      },
      ctrlDmrId: {
        type: String
      },
      notConfirmSmsList: {
        type: String,
        default: '',
      },
      orgRid: {
        type: String
      },
      /*
      * 下面的props是datatables的props
      */
      name: {
        type: String,
        required: true,
      },
      head: {
        type: Array,
        required: true,
      },
      order: {
        type: Array,
        default: () => {
          return [[2, 'asc']]
        },
      },
      detailHead: {
        type: Array,
      },
      detailBodyName: {
        type: String,
      },
      exportNamePrefix: {
        type: String,
        default: 'bfdx',
      },
      buttons: {
        type: Array,
        default: () => ([]),
      },
      columnDefs: {
        type: Array,
      },
      detailBodyIndex: {
        type: Boolean,
        default: true,
      },
      edit: {
        type: Boolean,
        default: false,
      },
      detailRender: {
        type: Function,
      },
      getDetailData: {
        type: Function,
      },
      historyTable: {
        type: Boolean,
        default: true
      },
      parseRequestData: {
        type: Function
      },
      afterRequestEnd: {
        type: Function
      },
      notConfirmDtRef: {
        type: Object
      }
    },
    data() {
      const _time = new Date()
      return {
        //el-from所绑定的表单对象
        query: {
          startTime: bfTime.getDayBeforeTheSpecifiedTime(_time),
          endTime: _time,
        },
        //过滤时选择的搜索列
        // filterColumnData: 0,
        // filterValue: '',
        //表单可选项是否展开
        isexpand: false,
        //datatables的属性
        dataTable: {
          scrollTop: 0,
          scrollLeft: 0,
          page: 0,
          showDetails: false,
        },
        // 计算表格滚动高度，导航header高度68px，页面上下边距40px
        // 请求表单高度76px，自定义表尾高度39px
        // 滚动区域表头高度40px
        scrollY: 'calc(100vh - 68px - 40px - 76px - 39px - 40px)',
        previousRouteName: '',
        dataTableBody: []
      }
    },
    computed: {
      //表单规则
      rules() {
        return {
          startTime: [
            {
              type: 'date',
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'change',
            },
          ],
          endTime: [
            {
              type: 'date',
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'change',
            },
          ],
        }
      },
      timeFormat() {
        return this.rule ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm'
      },
      formLabelWidth() {
        return this.labelWidth || (this.isFR ? '120px' : this.isEN ? '110px' : '90px')
      },
      // filterSelect() {
      //   const initfilSel = [{ title: this.$t('dialog.overallSituation'), data: 'overall situation' }]
      //   initfilSel.push(...this.head)
      //   return initfilSel
      // },
      isExpandFormItem() {
        if (!this.isMobile) {
          return true
        } else {
          return this.isexpand
        }
      },
    },
    methods: {
      //判断路由标签被删除则表格数据被清空
      onRemoveQuickRoutingTag(tag) {
        if (tag.route.name === this.previousRouteName) {
          const _time = new Date()
          this.dataTableBody = []
          this.query.startTime = bfTime.getDayBeforeTheSpecifiedTime(_time)
          this.query.endTime = _time
          this.$refs.datatable?.instance.clear().draw()
          this.$emit('removeDataTableData')
        }
      },
      queryLoadingClose() {
        if (!this.queryLoadingClosed) {
          this.queryLoadingClosed = true
          this.queryLoading.close()
        }
      },
      parseRequestdata(msg_data, msg_reply, msg_subject, nats_ssid) {
        // 加载动画超时关闭
        this.queryLoadingClose()

        // 根据数据库表名解析请求数据，生成json数据对象
        var rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
        var dbListName_t = bfproto.bfdx_proto_msg_T(this.dbListOfProtoTypeName)
        var dbListName_obj = dbListName_t.decode(rpc_cmd_obj.body)

        // 向父组件传递result自定义事件，返回请求结果
        this.queryResult(dbListName_obj, rpc_cmd_obj.opt, this.dbListOfProtoTypeName)
        if (rpc_cmd_obj.opt.endsWith('end')) {
          bfglob.server.unsubscribe(nats_ssid)
        }
      },
      queryFunc(type = 0) {
        const that = this
        this.$refs.query.validate((valid) => {
          if (valid) {
            // 检测 fromTime,toTime 是否存在，不存在则格式化相应的时间字符串
            if (!that.query.fromTime) {
              that.query.fromTime = bfTime.getUtcTimeString(that.query.startTime)
            }
            if (!that.query.toTime) {
              that.query.toTime = bfTime.getUtcTimeString(that.query.endTime)
            }

            // 限制请求时间范围在3个月内
            if (!that.limitRequestTimeRange()) {
              return false
            }
            this.dbListOfProtoTypeName = type === 1 ? this.notConfirmSmsList : this.dbListName
            // 发布开始请求数据的消息给父组件，让父组件清掉旧的请求数据
            // that.$emit('query', this.dbListOfProtoTypeName)
            this.dataTableBody = []
            notConfirmTableBody = []
            // 开始请求数据
            that.queryHistoryData()
          } else {
            return false
          }
        })
      },
      queryEnd(dbListOfProtoTypeName) {
        if (dbListOfProtoTypeName === 'db_not_confirm_sms_list') {
          Object.freeze(bodyCache)
          notConfirmTableBody = bodyCache
          bodyCache = []
        } else {
          Object.freeze(bodyCache)
          this.dataTableBody = bodyCache
          bodyCache = []
          if (dbListOfProtoTypeName === 'db_crud_log_list') {
            this.$emit('query-finish-proxyCurdDetail')
          }
        }
        this.processReqMaximum = false
      },
      processInsRulesHistoryRows(db_data, opt, dbListOfProtoTypeName) {
        if (opt === '0,end' && db_data.no === -1) {
          bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
          return
        }
        if (!this.processReqMaximum && (bodyCache.length || this.dataTableBody.length) >= bfglob.reqMaximum) {
          this.processReqMaximum = true
          bfNotify.reqMaximumPrompt()
        } else {
          // 执行父组件传递的函数
          const insRuleItem = this.parseRequestData(db_data)
          if (insRuleItem) {
            bodyCache.push(insRuleItem)
          }
        }

        if (opt.includes('end')) {
          this.queryEnd(dbListOfProtoTypeName)
        }
      },
      processSmsHistoryRows(db_data, opt, dbListOfProtoTypeName) {
        for (var i = 0; i < db_data.rows.length; i++) {
          var item = db_data.rows[i]
          if (this.processReqMaximum) {
            break
          }
          if ((bodyCache.length || this.dataTableBody.length || notConfirmTableBody.length) >= bfglob.reqMaximum) {
            this.processReqMaximum = true
            bfNotify.reqMaximumPrompt()
            break
          } else {
            const result = this.parseRequestData(item, dbListOfProtoTypeName)
            result && bodyCache.push(result)
          }
        }
        if (opt.includes('end')) {
          this.queryEnd(dbListOfProtoTypeName)
          this.afterRequestEnd(dbListOfProtoTypeName, this.dataTableBody.length !== 0 ? this.dataTableBody : notConfirmTableBody)
        }
      },
      processSoundHistoryRows(db_data, opt, dbListOfProtoTypeName) {
        for (let i = 0; i < db_data.rows.length; i++) {
          const item = db_data.rows[i]
          if (this.processReqMaximum) {
            break
          }
          if ((bodyCache.length || this.dataTableBody.length) >= bfglob.reqMaximum) {
            this.processReqMaximum = true
            bfNotify.reqMaximumPrompt()
            break
          } else {
            //执行父组件传递的请求函数
            // this.parseRequestData ? bodyCache.push(this.parseRequestData(item)) : bodyCache.push(item)
            let result = item
            if (this.parseRequestData) {
              result = this.parseRequestData(item)
            }
            bodyCache.push(result)
          }
        }

        if (opt.includes('end')) {
          Object.freeze(bodyCache)
          this.dataTableBody = bodyCache
          bodyCache = []
          this.processReqMaximum = false
          // 发布代理操作列的点击事件
          this.$emit('query-finish-sound')
        }
      },
      queryResult(db_data, opt, dbListOfProtoTypeName) {
        //规则巡查历史
        if (this.cmd === 52) {
          this.processInsRulesHistoryRows(db_data, opt, dbListOfProtoTypeName)
          return
        }
        if (opt === '0,end' && db_data.rows.length === 0) {
          bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
          return
        }
        //查询短信历史
        if (this.cmd === 56) {
          this.processSmsHistoryRows(db_data, opt, dbListOfProtoTypeName)
          return
        }
        // 录音历史
        if (this.cmd === 50) {
          this.processSoundHistoryRows(db_data, opt, dbListOfProtoTypeName)
          return
        }
        for (let i = 0; i < db_data.rows.length; i++) {
          const item = db_data.rows[i]
          // 巡逻历史的接收时间
          if (this.cmd === 48) {
            item.receiveTime = utcToLocalTime(item.receiveTime)
          }
          if (this.processReqMaximum) {
            break
          }

          if ((bodyCache.length || this.dataTableBody.length) >= bfglob.reqMaximum) {
            this.processReqMaximum = true
            bfNotify.reqMaximumPrompt()
            break
          } else {
            //执行父组件传递的请求函数
            // this.parseRequestData ? bodyCache.push(this.parseRequestData(item)) : bodyCache.push(item)
            if (this.parseRequestData) {
              const pasreItem = this.parseRequestData(item)
              pasreItem ? bodyCache.push(pasreItem) : ''
            } else {
              bodyCache.push(item)
            }
          }
        }
        if (opt.includes('end')) {
          this.queryEnd(dbListOfProtoTypeName)
          // this.afterRequestEnd()
        }
      },
      limitRequestTimeRange() {
        // 时间差在3个月内则返回true,否则返回false
        var get3MonthBefor = (currentDate) => {
          var resultDate, year, month, date, hms
          var currDate = new Date(currentDate)
          year = currDate.getFullYear()
          month = currDate.getMonth() + 1
          date = currDate.getDate()
          hms = currDate.getHours() + ':' + currDate.getMinutes() + ':' +
            (currDate.getSeconds() < 10 ? '0' + currDate.getSeconds() : currDate.getSeconds())
          switch (month) {
            case 1:
            case 2:
            case 3:
              month += 9
              year--
              break
            default:
              month -= 3
              break
          }
          month = (month < 10) ? ('0' + month) : month
          resultDate = year + '-' + month + '-' + date + ' ' + hms
          return resultDate
        }
        var startTime_num = new Date(this.query.startTime).getTime()
        var endTime_num = new Date(this.query.endTime).getTime()
        var befor3Mon = get3MonthBefor(this.query.endTime)
        if (new Date(befor3Mon) > this.query.startTime) {
          bfNotify.messageBox(this.$t('msgbox.maxSelTime'), 'warning')
          return false
        } else if (startTime_num - endTime_num >= 0) {
          bfNotify.messageBox(this.$t('msgbox.selTimeError'), 'warning')
          return false
        }
        return true
      },
      queryHistoryData() {
        this.queryLoading = ElLoading.service({
          lock: true,
          text: this.$t('msgbox.querying'),
          spinner: 'loading',
          background: 'rgba(0,0,0, 0.7)',
          customClass: 'loginLoading',
        })
        this.queryLoadingClosed = false

        const msgObj = {
          timezone: -(new Date().getTimezoneOffset()),
          fromTime: this.query.fromTime,
          toTime: this.query.toTime,
          deviceRid: this.deviceRid,
          personRid: this.userRid,
        }

        switch (this.cmd) {
          case 48:
            // 巡查线路历史
            msgObj.optPointId = this.pointRid
            break
          case 52:
            // 巡查规则统计历史
            msgObj.optRuleRid = this.ruleRid
            msgObj.optLineRid = this.lineRid
            let _toTime = bfTime.getLocalTimeString(this.query.toTime, 'YYYY-MM-DD')
            _toTime += ' 23:59:59'
            msgObj.toTime = _toTime
            break
          case 53:
            // 调度历史
            msgObj.deviceRid = this.deviceDmrId
            break
          case 54:
            // 控制器上下线历史
            msgObj.deviceRid = this.ctrlDmrId
            break
          case 56:
            // 查询短信历史
            msgObj.deviceRid = this.deviceDmrId
            if (this.dbListOfProtoTypeName === 'db_not_confirm_sms_list') {
              msgObj.optSmsDbType = 1
            }
            break
          case 57:
            // 查询有源点低压报警历史
            msgObj.optPointId = this.pointRid
            break
          case 59:
            // iot设备历史
            msgObj.deviceRid = this.deviceDmrId
            break
        }

        const opt = uuid()
        const msgOpts = {
          rpcCmdFields: {
            opt,
          },
        }

        bfglob.server.subscribe(opt, this.parseRequestdata)

        bfproto.sendMessage(this.cmd, msgObj, 'search_filter', `db.${bfglob.sysId}`, msgOpts).then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            // bfutil.messageBox(this.$t("msgbox.selSuccess,'success');
            this.$refs.datatable?.instance.clear()
            // 清除查询未确认短信的表格
            this.$emit('clearDt')
          } else {
            // 加载动画超时关闭
            this.queryLoadingClose()
            if (rpc_cmd_obj.resInfo.includes('session')) {
              bfNotify.warningBox(this.$t('msgbox.serverReconnect'))
              return
            }
            bfNotify.messageBox(this.$t('msgbox.selError'), 'error')
          }
        }).catch(err => {
          bfglob.console.warn('查询历史记录超时', err)
          bfNotify.messageBox(this.$t('msgbox.selError'), 'error')
          // 加载动画超时关闭
          this.queryLoadingClose()
        })
      },
      startTimeChanged(val) {
        this.query.fromTime = val ? bfTime.getUtcTimeString(val) : ''
      },
      endTimeChanged(val) {
        this.query.toTime = val ? bfTime.getUtcTimeString(val) : ''
      },
      //初始化选择器和过滤输入框
      // initSelectorInstance() {
      //   const select_filter_div = this.$refs.datatable.$el.querySelector('.select_filter')
      //   var Profile = Vue.extend({
      //     template: `<el-input placeholder="请输入内容进行搜索"
      //                 v-model="filterValue"
      //                 :disabled="disable"
      //                 class="data_filter"
      //                 clearable
      //                 @input="filterColumn(filterColumnData)"
      //                  >
      //         <el-select
      //             class="filter_select"
      //             slot="prepend"
      //             clearable
      //             v-model="filterColumnData"
      //             :placeholder="$t('dialog.overallSituation')"
      //             :disabled="disable"
      //         >
      //           <el-option v-for="(item, index) in filterSelect"
      //                      :label="item.title"
      //                      :value="index"
      //                      :key="item.data"></el-option>
      //         </el-select>
      //       </el-input>`,
      //     props: {
      //       filterSelect: {
      //         type: Array
      //       }
      //     },
      //     methods: {
      //       filterColumn(index) {
      //         if (index && this.filterColumnData) {
      //           $('.bfdataTable').DataTable().column(index).search(this.filterValue).draw()
      //         } else {
      //           $('.bfdataTable').DataTable().search(this.filterValue).draw()
      //         }
      //       },
      //     },
      //     beforeMount() {
      //       this.filterColumn = debounce(this.filterColumn.bind(this), 600)
      //     }
      //   })
      //   this.selectFilterInstance = new Profile({
      //     propsData: {
      //       filterSelect: this.filterSelect,
      //     },
      //     data() {
      //       return {
      //         filterValue: '',
      //         filterColumnData: 0,
      //         disable: true,
      //       }
      //     },
      //     i18n,
      //   }).$mount()
      //   select_filter_div.appendChild(this.selectFilterInstance.$el)
      // },
    },
    watch: {
      dataTableBody(val) {
        this.$emit('data-change', val)
      }
    },
    mounted() {
      if (this.isMobile) {
        this.isexpand = false
      }
      bfglob.on('removeQuickRoutingTag', this.onRemoveQuickRoutingTag)
    },
    beforeUnmount() {
      bfglob.off('removeQuickRoutingTag', this.onRemoveQuickRoutingTag)
    },
    activated() {
      this.previousRouteName = this.$route.name
    },
  }
</script>

<style lang="scss">
  .hisComForm {
    height: auto;

    @media (max-width: 1023px) {
      margin-bottom: 0.5rem;
    }

    .el-form-item {
      max-width: 460px;
      margin: 0 0 10px 0;

      .el-select.el-select--small {
        width: 100%;
      }

      .el-form-item__label {
        padding: 0;
      }

      .el-form-item__content .el-input {
        @media(max-width: 1024px) {
          width: 100%;
        }
      }
    }

    .btnGroup {
      @media(min-width: 1024px) {
        margin-top: 24px;
      }

      @media(max-width: 1024px) {
        display: flex;
        justify-content: flex-start;
        button {
          flex: 1;
        }
      }

      .el-button {
        line-height: 6px;
      }

      .queryBtn {
        min-width: 160px;
        max-width: 460px;

        @media(min-width: 3840px) {
          height: 38px;
        }
      }

      .smsBtn:first-child {
        @media (max-width: 767px) {
          margin-bottom: 0.5rem;
        }
      }

      .smsBtn {
        height: 32px;
        min-width: 226px;
        margin-left: 0;

        @media(min-width: 1179px) and (max-width: 1919px) {
          max-width: 226px;
        }

        @media(min-width: 2560px) {
          height: 38px;
        }

        @media(max-width: 767px) {
          white-space: normal;
          line-height: 12px;
          min-width: 160px;
        }
      }

      .expandBtn {
        min-width: 32px;
        height: 32px;
      }
    }
  }

  .data-tables-wrapper {
    height: auto;
  }

  //.data_filter{
  //  .el-input-group__prepend{
  //    width:120px;
  //    .filter_select{
  //      .el-input__suffix{
  //        right:-32px;
  //      }
  //      .el-input__inner{
  //        width: 120px;
  //        padding: 0 30px 0 10px;
  //      }
  //    }
  //  }
  //  .input-with-select .el-input-group__prepend {
  //    background-color: #fff;
  //  }
  //}
</style>
