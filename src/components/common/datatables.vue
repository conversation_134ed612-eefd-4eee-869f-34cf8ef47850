<template>
  <div class="w-full data-tables-wrapper">
    <table
      ref="datatable"
      cellpadding="0"
      cellspacing="0"
      border="0"
      width="100%"
      class="display table-striped table-bordered table-hover dataTable bfdataTable"
      :class="tableClass"
      style="width:100%"
    />
  </div>
</template>

<script>
  import 'bootstrap/dist/css/bootstrap.css'
  import 'datatables.net-bs4'
  import 'datatables.net-bs4/css/dataTables.bootstrap4.css'
  import 'datatables.net-buttons-bs4'
  import 'datatables.net-buttons/js/buttons.colVis'
  import 'datatables.net-scroller-dt'
  import 'datatables.net-scroller-dt/css/scroller.dataTables.css'
  import 'datatables.net-staterestore-bs4'
  import $ from 'jquery'
  import { debounce, throttle } from 'lodash'
  import bfutil from '@/utils/bfutil'
  import '@/utils/dataTables.cellEdit'

  import bfNotify from '@/utils/notify'
  import bfTime from '@/utils/time'
  import { writeFile } from 'xlsx'

  const excludesDisable = ['dynamicGroupTable']

  export default {
    name: 'BfDatatables',
    emits: ['row-click', 'row-dbclick'],
    props: {
      tableClass: {
        type: String,
        default: '',
      },
      name: {
        type: String,
        required: true,
      },
      head: {
        type: Array,
        required: true,
      },
      data: {
        type: Array,
        required: true,
      },
      order: {
        type: Array,
        default: () => {
          return [[2, 'asc']]
        },
      },
      detailHead: {
        type: Array,
      },
      detailBodyName: {
        type: String | Array,
      },
      exportNamePrefix: {
        type: String,
        default: 'bfdx',
      },
      buttons: {
        type: Array,
        default: () => ([]),
      },
      columnDefs: {
        type: Array,
      },
      detailBodyIndex: {
        type: Boolean,
        default: true,
      },
      edit: {
        type: Boolean,
        default: false,
      },
      detailRender: {
        type: Function,
      },
      getDetailData: {
        type: Function,
      },
      historyTable: {
        type: Boolean,
        default: false
      },
      scrollY: {
        type: String,
        default: '50vh',
      },
      customDom: {
        type: String,
      },
      rowId: {
        type: String,
        default: 'rid',
      }
    },
    data() {
      return {
        page: 0,
        showDetails: false,
        scrollTopRow: 0,
      }
    },
    watch: {
      data: {
        deep: true,
        handler(newData) {
          this.drawDataTable(newData)
          this.instance?.button(0).enable(newData.length > 0)
        },
      },
      buttons: {
        deep: true,
        handler(btnList) {
          for (let i = 0; i < btnList.length; i++) {
            const btnOpts = btnList[i]
            // if (btnOps.enabled)
            this.instance?.button(i + 1).enable(btnOpts.enabled ?? true)
          }
        }
      },
    },
    computed: {
      isMobile() {
        return this.$root.layoutLevel === 0
      },
      customRender() {
        return typeof this.detailRender === 'function'
      },
    },
    methods: {
      defaultRender(rowData) {
        if (Object.keys(rowData[this.detailBodyName] || {}).length > 0) {
          const format_row_child_table = (data) => {
            let html = `<table cellpadding='0' cellspacing='0' border='0'
                class='display table table-striped table-bordered row-details'
                ><thead>`
            html += bfutil.getTableHead(this.detailHead)
            html += '</thead><tbody>'

            let detailBodyName = this.detailBodyName
            if (Array.isArray(this.detailBodyName)) {
              detailBodyName = 'detailBodyNameTmp'
              data[detailBodyName] = {}
              data[detailBodyName].default = this.detailBodyName.map((name) => {
                return { [name]: data[name] }
              })
                .reduce((p, c) => {
                  return Object.assign(p, c)
                }, {})
            }

            const detailBody = (this.customRender && this.getDetailData)
              ? this.getDetailData(data.rid)
              : data[detailBodyName]
            html += bfutil.getTableBody(this.detailHead, detailBody)
            html += '</tbody></table>'

            return html
          }

          return format_row_child_table(rowData)
        }

        return ''
      },
      getChildTable(rowData) {
        let childTable = ''
        const that = this
        const defaultRender = () => {
          return that.defaultRender(rowData)
        }
        // 有给子表渲染函数
        if (typeof this.detailRender === 'function') {
          childTable = this.detailRender(rowData, defaultRender)
        } else {
          // 采用默认的渲染逻辑
          childTable = defaultRender()
        }

        return childTable
      },
      loadChildTable() {
        const that = this
        $(this.$el).find('tbody').off('click', 'td.details-control')
          .on('click', 'td.details-control', function (e) {
            e.stopPropagation()

            const $this = $(this)
            const tr = $this.closest('tr')

            // 上面已经阻止了事件冒泡，就必须再次处理，否则点击控件单元格无法选择表格行
            that.processRowClick(tr, e)

            const row = that.instance.row(tr)
            const rowData = row.data()
            const childTable = that.getChildTable(rowData)
            if (!childTable) {
              that.$message(that.$t('dialog.childRowIsEmpty'))
              return
            }
            if (row.child.isShown()) {
              row.child.hide()
            } else {
              row.child(childTable).show()
            }
            that.$nextTick(() => {
              that.resetTableHeight()
            })
          })
      },

      resetTableHeight() {
        setTimeout(() => {
          const h = Math.max(this.$refs.datatable.offsetHeight, this.$refs.datatable.scrollHeight)
          $(this.$el).find('.dt-scroll-body table.bfdataTable+div').height(h)
        }, 0)
      },
      processRowClick(tr, e) {
        const $tr = $(tr)
        // 点击子行自动选择子行的父元素
        // if (!$tr[0].classList.contains('odd') && !$tr[0].classList.contains('even')) {
        //   $tr = $($tr[0].previousSibling || $tr)
        // }

        $(this.$el).find('tbody tr.selected').removeClass('selected')
        $tr.addClass('selected')
        const data = this.instance.row(tr).data()
        this.$emit('row-click', data, e)
      },
      initDataTableEvents() {
        const that = this
        const $thisEl = $(this.$el)
        const $tbody = $thisEl.find('tbody')
        if (!$tbody) return

        // 行单击事件
        $tbody.on('click', 'tr', function (e) {
          // 表格行点击事件
          that.processRowClick(this, e)
        })

        // 行双击
        $tbody.on('dblclick', 'tr', function (e) {
          const data = that.instance.row(this).data()
          that.$emit('row-dbclick', data, e)
        })
        $(this.$refs.datatable).on('draw.dt', this.resetTableHeight)
      },
      setTooltip(el) {
        if (el.offsetWidth >= el.scrollWidth) {
          return
        }

        const $el = $(el)
        const text = $el.text()
        if (!text) {
          return
        }

        $el.attr('title', text)
      },
      drawTableTdTooltips() {
        const that = this

        // 表头内容溢出，设置title属性
        const dataTableParents = $(that.$refs.datatable).parents('.dt-scroll')
        const thList = dataTableParents.find('.dt-scroll-head thead th')
        thList.each(function () {
          that.setTooltip(this)
        })

        // 表格内容溢出，设置title属性
        const tdList = $(this.$refs.datatable).find('tbody td')
        tdList.each(function () {
          that.setTooltip(this)
        })
      },
      initDataTable() {
        const index_col = {
          title: this.$t('dialog.index'),
          data: null,
          // orderable: false,
          defaultContent: '',
          width: '60px',
          render: (data, type, row, meta) => {
            return meta.row + 1
          },
        }
        if (this.detailHead) {
          index_col.class = 'details-control'
          index_col.width = '50px'
        }
        const _columns = [index_col]

        // 在data()方法中未声明，避免被vue劫持代理
        this.instance = $(this.$refs.datatable).DataTable({
          columns: _columns.concat(this.head),
          data: this.data,
          rowId: this.rowId,
          stateSave: true,
          // stateDuration: -1,
          // destroy: true,
          // order: this.order,
          autoWidth: false,
          searching: true,
          info: true,
          deferRender: true,
          scroller: true,
          scrollCollapse: true,
          scrollX: true,
          scrollY: this.scrollY,
          dom: this.customDom ?? (this.historyTable ? 't<"dataTables_foot"Bfi>' : '<"dataTables_head"B<"space">f>t<"dataTables_foot"i>'),
          buttons: [
            {
              text: `<span class='mdi mdi-download'></span>${this.$t('dialog.export')}`,
              action: this.exportMethod,
              enabled: this.data.length > 0,
            },
          ].concat(this.buttons),
          language: this.$t('dtable'),
          initComplete: (settings, json) => {
            this.detailHead && this.loadChildTable()
            if (this.data.length > 0) {
              this.$nextTick(() => {
                $(this.$el).find('tbody tr:first-child').click()
              })
            }
          },
          fixedHeader: true,
          columnDefs: this.columnDefs || [],
          createdRow: (row, data, dataIndex) => {
            if (excludesDisable.includes(this.name)) {
              return
            }
            // 判断数据是否为权限内数据，以禁用行点击/双击/高亮的事件
            const noPermOrg = bfglob.noPermOrgData.get(data.orgId || data.parentOrgId)
            if (noPermOrg) {
              $(row).addClass('row-disabled')
            }
          },
          drawCallback: (settings) => {
            // 表格在其他标签(tabs)隐藏下，无法正确得到列宽，不设置tooltip
            if (!settings?.nTBody?.offsetWidth) {
              return
            }

            this.drawTableTdTooltips()
          },
          stateSaveCallback: debounce((settings, data) => {
            sessionStorage.setItem(this.name, JSON.stringify(data))
          }, 300)
        })

        this.instance.on('requestChild', (e, row) => {
          row.child(this.getChildTable(row.data())).show()
          this.$nextTick(() => {
            // 展开了子行，但是没有在上层tr添加dt-hasChild类，导致上层tr不是显示的展开按钮样式
            const tr = document.getElementById(row.data().rid)
            tr?.setAttribute('class', 'dt-hasChild')
          })
        })
      },
      generateChildHeader() {
        if (!this.detailHead) {
          return []
        }

        const header = this.detailHead.map((v) => {
          return v.title
        })
        // 缩进一个单元格
        header.unshift('')

        return header
      },
      generateChildData(childData) {
        // index为子表索引序号
        let index = 1
        const childBody = []

        for (const k in childData) {
          const child = childData[k]
          // 设置子表每行的单元格数据
          const data = this.detailHead.map((v, i) => {
            if (typeof v.render === 'function') {
              return v.render(child[v.data], 'string', child, {
                row: index,
                col: i,
              })
            }
            // 替换掉无用的标签元素字符串
            if (v.data === '_checkResult') {
              return child[v.data].replace(/<span .*?>|<\/span>.*?/ig, '')
            }
            return child[v.data]
          })
          // 重置子表索引
          if (this.detailBodyIndex) {
            data[0] = index
          }
          // 子表缩进一个单元格
          data.unshift('')
          // 将子表的每行都push到_body中
          childBody.push(data)
          // 子表索引递增
          index++
        }

        return childBody
      },
      generateChildTable(originData, exportDataBody) {
        if (!this.detailHead) {
          return []
        }
        const body = []

        // 从表格数据中读取源数据索引，查找对应的源数据，生成子表数据
        for (let i = 0; i < exportDataBody.length; i++) {
          const index = exportDataBody[i][0] - 1
          const childData = (this.customRender && this.getDetailData)
            ? this.getDetailData(originData[index].rid)
            : originData[index][this.detailBodyName]
          const hasChildData = Object.keys(childData || {}).length > 0
          // 跳过没有子表数据的表格行生成逻辑
          if (hasChildData) {
            body.push(this.generateChildData(childData))
          } else {
            body.push([])
          }
        }
        return body
      },
      exportMethod(e, dt) {
        // 创建 Web Worker
        const exportExcelWorker = new Worker(new URL('@/worker/exportExcel.worker.js?worker', import.meta.url), {
          type: 'module'
        })
        exportExcelWorker.onmessage = (event) => {
          // 处理完数据，关闭当前worker进程
          exportExcelWorker.terminate()

          // 导出 Excel
          const time = bfTime.nowLocalTime().replace(' ', '_').replace(/-|:/ig, '')
          const filename = `${this.exportNamePrefix}_${time}.xlsx`
          writeFile(event.data, filename)
        }
        exportExcelWorker.onerror = () => {
          // worker进程创建失败，提示用户导出数据失败
          bfNotify.messageBox(this.$t('msgbox.exportError'), 'error')
        }
        exportExcelWorker.postMessage({
          command: 'sheetName',
          sheetName: this.exportNamePrefix,
        })

        const exportData = dt.buttons.exportData()
        const originData = Array.prototype.slice.call(dt.data())
        const data = {
          data: {
            header: exportData.header,
            body: exportData.body,
            footer: exportData.footer,
          },
          child: {
            header: this.generateChildHeader(),
            data: this.generateChildTable(originData, exportData.body),
          },
        }
        exportExcelWorker.postMessage(data)
      },
      columnsAdjust() {
        this.instance && this.instance.columns.adjust()
        // this.instance?.draw(false)
        this.drawTableTdTooltips()
      },
      drawDataTable(newData) {
        const json = sessionStorage.getItem(this.name)
        const state = JSON.parse(json ?? '{}')
        if (!newData) {
          bfglob.console.warn('can not found data!')
          return
        }
        if (!this.instance) {
          bfglob.console.warn('can not found instance!')
          return
        }
        if (!this.scrollTopRow) {
          this.scrollTopRow = state?.scroller?.topRow ?? 0
        } else {
          state.scroller.topRow = this.scrollTopRow
        }
        this.instance.clear()
        this.instance.rows.add(newData)
        this.instance.state(state).draw()
        this.$nextTick(() => {
          this.columnsAdjust()
        })
      },
      destoryAndDrawDt() {
        const json = sessionStorage.getItem(this.name)
        const state = JSON.parse(json ?? '{}')
        // 重绘表格
        this.instance.clear()
        this.instance.destroy()
        this.initDataTable()
        this.$nextTick(() => {
          this.instance.state(state).draw()
          this.scrollTopRow = state?.scroller?.topRow ?? 0
        })
      },
      loadRestoreState() {
        try {
          const json = sessionStorage.getItem(this.name)
          if (json) {
            const state = JSON.parse(json)
            if (this.scrollTopRow) {
              state.scroller.topRow = this.scrollTopRow
              this.scrollTopRow = 0
            }
            // 在切换语言后，state.length会发生变化，除了聚焦页面的state,其他的state.length都会变化，所以再次进入的时候需要设置
            // length设置后导致路由页面切换后，渲染数据条数超出length长度无法渲染问题
            // state.length = this.data.length
            this.instance.state(state).draw()
          }
        } catch (e) {
          // no-empty
        }
      },
      removeState() {
        sessionStorage.removeItem(this.name)
      },
    },
    mounted() {
      this.initDataTable()
      this.initDataTableEvents()
      bfglob.on('change_lang', (lang) => {
        // 系统语言变化后，销毁表格再重绘
        this.destoryAndDrawDt()
      })
      bfglob.on(this.name, (dlgSize) => {
        this.columnsAdjust()
      })
      bfglob.emit(this.name)
      bfglob.on('bflayout', (data) => {
        this.columnsAdjust()
      })
      window.addEventListener('beforeunload', this.removeState)
    },
    beforeMount() {
      this.resetTableHeight = throttle(this.resetTableHeight, 250)
      this.drawDataTable = debounce(this.drawDataTable, 120)
      this.drawTableTdTooltips = debounce(this.drawTableTdTooltips, 120)
    },
    // 页面由隐藏到显示时触发,路由切换
    activated() {
      this.loadRestoreState()
    },
    beforeUnmount() {
      window.removeEventListener('beforeunload', this.removeState)
    }
  }
</script>

<style lang='scss'>

  .dataTables_wrapper,
  .dt-container {
    font-size: 14px;

    .dataTables_head,
    .dataTables_foot {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      flex-wrap: wrap;

      .space {
        flex: auto;
      }

      .dataTables_info {
        padding-top: 0;
      }
    }

    .dataTables_head,
    .dataTables_foot {
      .dt-buttons {
        @media (min-width: 2560px) {
          .btn {
            line-height: 1;
            padding: 12px 20px;
          }
        }

        &.btn-group {

          &>.btn:last-child:not(:first-child),
          &>.dropdown-toggle:not(:first-child) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }

          &>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }

          &>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
            border-radius: 0;
          }
        }

        .btn-secondary {
          outline: none;
          color: #fff;
          background-color: #409eff;
          border-color: #409eff;
          font-size: 14px;
          box-shadow: none !important;

          &:first-child {
            border-right-color: #66b1ff;
          }

          &:last-child {
            border-left-color: #66b1ff;
          }

          &:not(:first-child):not(:last-child) {
            border-left-color: #66b1ff;
            border-right-color: #66b1ff;
          }

          &:focus,
          &:hover {
            background-color: #66b1ff !important;
            border-color: #66b1ff !important;
          }

          &:active {
            background-color: #3a8ee6 !important;
            border-color: #3a8ee6 !important;
          }
        }
      }

      .dataTables_filter,
      .dt-search {
        flex: none;
        display: flex;

        &>label {
          flex: none;
          position: relative;
          margin: 0;
          font-weight: unset;
        }

        input {
          -webkit-appearance: none;
          background-color: #fff;
          background-image: none;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          box-sizing: border-box;
          color: #606266;
          display: inline-block;
          font-size: inherit;
          height: 30px;
          line-height: 30px;
          outline: none;
          padding: 0 10px 0 30px;
          transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
          width: 100%;
          margin: 0;

          &:hover {
            border-color: #c0c4cc;
          }

          &:focus {
            border-color: #409eff;
          }
        }

        @media (min-width: 2560px) {
          input {
            height: 40px;
            line-height: 40px;
          }
        }

        .el-icon-search {
          position: absolute;
          top: 50%;
          left: 8px;
          transform: translateY(-50%);
          color: #c0c4cc;
        }
      }

      .dataTables_scrollHead table.table-bordered {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
      }
    }

    .dataTables_foot {
      line-height: 38px;
      padding: 0 30px;
      display: flex;
      justify-content: space-between;
      border: 1px solid #ddd;
      border-top: none;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;

      .dt-buttons {
        display: flex;

        .disabled {
          cursor: not-allowed;
          background-image: none;
        }

        .iconfont {
          font-size: 14px;
        }

        .btn-secondary>span {

          .mdi-download,
          .iconfont {
            margin-right: 4px;
          }
        }
      }

      .dataTables_filter,
      .dt-search {
        flex-grow: 1;
        max-width: 350px;
      }
    }

    .dt-scroll {

      .dt-scroll-head,
      .dt-scroll-body {

        thead>tr>th,
        tbody>tr>td {
          padding: 2px 4px;
          height: 30px;
          line-height: 30px;
          vertical-align: middle;
          word-break: break-all;
          text-align: center;
          background-color: transparent;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        @media (min-width: 2560px) {

          thead>tr>th,
          tbody>tr>td {
            height: 30px;
            line-height: 30px;
          }
        }

        thead>tr>th.sorting_asc,
        thead>tr>th.sorting_desc,
        thead>tr>th.sorting,
        thead>tr>td.sorting_asc,
        thead>tr>td.sorting_desc,
        thead>tr>td.sorting {
          padding-right: 24px;
        }
      }

      .dt-scroll-body {
        border: 1px solid #dee2e6;
        border-top: none;

        tbody>tr {
          &.row-disabled {
            background-color: unset !important;
            color: #E6A23C !important;
            font-style: italic;
            cursor: default;
          }

          td.details-control {
            cursor: pointer;
            min-width: 20px;
            text-align: right;
            background: url(@/images/sysImg/details_open.png) no-repeat left;
            padding-left: 22px;
          }

          &.dt-hasChild td.details-control {
            background-image: url(@/images/sysImg/details_close.png);
            padding-left: 22px;
          }

          &.dt-hasChild+tr {
            td table {
              margin-bottom: 0;
              min-width: 150px;
            }
          }
        }
      }

      .dt-scroll-head {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;

        table>thead>tr>th {
          .dt-column-order {
            right: 4px;
          }
        }
      }
    }

    .dataTables_head {
      margin-bottom: 6px;
    }
  }

  .dt-scroll-body tbody tr table.row-details {
    width: auto;
    font-size: 14px;

    thead {
      tr {
        background-color: #fff;
      }

      border-bottom: 1px solid #dee2e6;
    }
  }

  .dt-scroll-body>table.dataTable.table-striped>tbody>tr.selected>td {
    background-color: rgb(var(--dt-row-selected));
    box-shadow: none;
    color: #fff;
  }
</style>
