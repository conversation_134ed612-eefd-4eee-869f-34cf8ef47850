<template>
  <el-form-item class="form-item-ellipsis prochat-device-select">
    <template #label>
      <span
        class="form-item-label"
        :title="$t('dialog.prochatDeviceList')"
      >{{ $t('dialog.prochatDeviceList') }}</span>
    </template>
    <el-select
      v-model="prochatDeviceInfo"
      filterable
      :placeholder="$t('dialog.select')"
      :no-match-text="$t('dialog.noMatchText')"
      value-key="selfId"
      @change="onProchatDeviceInfoChanged"
    >
      <el-option
        v-for="(item, i) in prochatDeviceInfoList"
        :key="i"
        :label="item.selfId"
        :value="item"
      />
    </el-select>
    <el-button
      type="primary"
      icon="refresh-right"
      @click="queryProchatDeviceInfoList"
    />
  </el-form-item>
</template>

<script>
  import {
  getProchatDeviceInfoList,
  gprochatDeviceInfoList,
  prochatDeviceInfo,
} from '@/utils/prochatDeviceInfoList'
import { debounce } from 'lodash'

  export default {
    name: 'ProchatDeviceSelect',
    emits: ['update-prochat-info'],
    data() {
      return {
        prochatDeviceInfoList: gprochatDeviceInfoList,
        prochatDeviceInfo: prochatDeviceInfo
      }
    },
    methods: {
      queryProchatDeviceInfoList: debounce(getProchatDeviceInfoList, 300),
      onProchatDeviceInfoChanged(val) {
        this.$emit('update-prochat-info', val)
      },
    },
    created() {
      if (!gprochatDeviceInfoList || gprochatDeviceInfoList.length === 0) {
        getProchatDeviceInfoList()
      } else {
        this.prochatDeviceInfoList = gprochatDeviceInfoList
      }
    },
  }
</script>

<style lang="scss">
  .prochat-device-select .el-form-item__content {
    display: flex;
    flex-wrap: nowrap;

    & > .el-select {
      flex: auto;

      .el-select__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    & > .el-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
</style>
