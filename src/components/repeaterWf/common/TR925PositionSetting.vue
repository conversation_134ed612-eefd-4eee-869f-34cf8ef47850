<template>
  <el-form
    ref="positionSetting"
    :model="positionSetting"
    label-position="top"
    class="position-setting"
  >
    <el-row
      :gutter="20"
      class="no-margin-x"
    >
      <!--写频软件无该配置-->
      <!--<el-col :xs="24">-->
      <!--<el-form-item>-->
      <!--<el-checkbox v-model="posEnable">-->
      <!--<span v-text="$t('header.setting')"></span>-->
      <!--</el-checkbox>-->
      <!--</el-form-item>-->
      <!--</el-col>-->
      <el-col :xs="24">
        <el-form-item
          :label="$t('dialog.timeZone')"
          prop="timeZoneId"
        >
          <el-select
            v-model="positionSetting.timeZoneId"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in timeZoneList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item
          :label="$t('dialog.timeZoneSet')"
          prop="timeZoneSet"
        >
          <el-select
            v-model="positionSetting.timeZoneSet"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="customTimeZoneId"
          >
            <el-option
              v-for="(item, i) in timeZoneSetList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item
          :label="$t('dialog.speedUnit')"
          prop="speedUnit"
        >
          <el-select
            v-model="positionSetting.speedUnit"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in speedUnitList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import { DefModel } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'

  export default {
    name: 'TR925PositionSetting',
    props: {
      repeaterData: {
        type: [Object, undefined]
      },
      repeater: {
        type: String,
        default: ''
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop
      },
      disabled: {
        type: Boolean,
        default: false
      },
      packageName: {
        type: String,
        default: ''
      },
      deviceModel: {
        type: String,
        default: DefModel
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop
      },
    },
    data() {
      return {
        positionSetting: {
          posEnable: 1,
          timeZoneId: 21,
          timeZoneSet: 28,
          speedUnit: 0
        }
      }
    },
    methods: {},
    computed: {
      posEnable: {
        get() {
          return this.positionSetting.posEnable === 1
        },
        set(val) {
          if (val) {
            this.positionSetting.posEnable = 1
          } else {
            this.positionSetting.posEnable = 0
          }
        },
      },
      customTimeZoneId() {
        return this.positionSetting.timeZoneId !== 0
      },
      timeZoneList() {
        const list = [
          {
            label: this.$t('dialog.customize'),
            value: 0
          }
        ]
        let id = 1
        for (let i = -12; i <= 12; i++) {
          const sign = i > 0 ? '+' : ''
          const val = i === 0 ? '' : i
          const obj = {
            label: `GMT${sign}${val}`,
            value: id++

          }

          list.push(obj)
        }

        return list
      },
      timeZoneSetList() {
        const list = []
        let id = -12
        const hasHalfId = [8, 10, 18, 20, 22, 23, 25, 29]
        for (let i = 0; i <= 33; i++) {
          const sign = id > 0 ? '+' : ''
          let val = id === 0 ? '' : id

          // 处理非整点的label文本
          if (hasHalfId.includes(i)) {
            if (val > 0) {
              val--
            }
            if (i === 23) {
              val += ':45'
            } else {
              val += ':30'
            }
          } else {
            id++
          }

          const obj = {
            label: `GMT${sign}${val}`,
            value: i
          }

          list.push(obj)
        }

        return list
      },
      speedUnitList() {
        return [
          {
            label: this.$t('dialog.km_h'),
            value: 0
          },
          {
            label: this.$t('dialog.mi_h'),
            value: 1
          },
          {
            label: this.$t('dialog.nmi_h'),
            value: 2
          },
        ]
      },
    }
  }
</script>

<style></style>
