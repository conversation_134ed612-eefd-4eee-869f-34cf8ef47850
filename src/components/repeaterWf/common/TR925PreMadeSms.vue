<template>
  <el-form
    v-model="preMadeSms"
    class="pre-made-sms"
    label-width="40px"
  >
    <el-row
      :gutter="20"
      class="no-margin-x"
    >
      <el-col :xs="24">
        <el-form-item
          v-for="(item, index) in preMadeSms"
          :key="index"
          class="sms-item"
          :label="index + 1 + ''"
        >
          <el-input
            :ref="getSmsRef(item.id)"
            v-model="item.content"
            resize="none"
            class="mgb-10 msgbox"
            type="textarea"
            rows="3"
            suffix-icon="date"
            maxlength="140"
            :placeholder="$t('dialog.msgcontentplaceholder')"
          />
          <el-icon
            class="del-icon"
            @click="deletePreMadeSms(index)"
          >
            <Delete />
          </el-icon>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import { DefModel } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'

  export default {
    name: 'TR925PreMadeSms',
    props: {
      repeaterData: {
        type: [Object, undefined]
      },
      repeater: {
        type: String,
        default: ''
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop
      },
      disabled: {
        type: Boolean,
        default: false
      },
      packageName: {
        type: String,
        default: ''
      },
      deviceModel: {
        type: String,
        default: DefModel
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop
      },
    },
    data() {
      return {
        preMadeSms: [],
        smsLimit: 10,
      }
    },
    methods: {
      getSmsRef(id) {
        return 'sms' + id
      },
      deletePreMadeSms(index) {
        // 清除当前索引的短信内容
        this.preMadeSms[index] = {
          id: index,
          content: ''
        }
      },
    },
    beforeMount() {
      // 组件挂载前生成多条空白短信数据
      const preMadeSms = []
      let smsLen = preMadeSms.length

      while (smsLen < this.smsLimit) {
        preMadeSms.push({
          id: smsLen,
          content: ''
        })

        smsLen = preMadeSms.length
      }

      this.preMadeSms = preMadeSms
    },
  }
</script>

<style>
  .pre-made-sms .el-form-item:not(:last-child) {
    margin-bottom: 10px;
  }

  .pre-made-sms .del-icon {
    color: #f56c6c;
    visibility: hidden;
    position: absolute;
    font-size: 20px;
    right: 0;
    top: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
  }

  .pre-made-sms .sms-item:hover .del-icon {
    visibility: visible;
  }

  .pre-made-sms .msgbox>textarea {
    padding-right: 30px;
  }

</style>
