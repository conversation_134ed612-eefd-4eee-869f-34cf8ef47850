<template>
  <section
    ref="bdContactContainer"
    class="bd-contact-wrap"
  >
    <div class="bd-contact-operation-buttons left">
      <el-button
        type="primary"
        :disabled="disAddBtn"
        @click="addContact"
        v-text="$t('dialog.add')"
      />
      <el-button
        type="danger"
        :disabled="selectedContact.length === 0"
        @click="deleteContact"
        v-text="$t('dialog.delete')"
      />
    </div>
    <el-table
      ref="bdContact"
      :data="bdContact"
      tooltip-effect="dark"
      style="width: 100%"
      :height="bdContactTableHeight"
      border
      highlight-current-row
      @selection-change="handleSelectionChange"
      @row-dblclick="rowDblclickHandler"
    >
      <el-table-column
        type="selection"
        width="40"
      />
      <el-table-column
        type="index"
        width="50"
      />
      <el-table-column
        :label="$t('dialog.contact')"
        prop="userName"
        min-width="130"
        sortable
      />
      <el-table-column
        prop="bdNum"
        :label="$t('dialog.beidouNumber')"
        min-width="130"
        sortable
      />
    </el-table>
    <el-dialog
      v-model="showContactSetting"
      :title="$t('dialog.beidouContact')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal="false"
      :fullscreen="fullscreen"
      top="0"
      width="30%"
      class="bd-contact-dlg drag-dialog"
      modal-class="drag-dialog-modal"
      @close="closeDlgFn"
    >
      <template #header>
        <div v-bfdrag>
          {{ $t('dataTable.emergency') }}
        </div>
      </template>
      <el-form
        ref="bd-contact-form"
        :model="oneContact"
        label-position="top"
        class="bd-contact-form"
        :rules="contactRules"
      >
        <el-row
          :gutter="20"
          class="no-margin-x"
        >
          <el-col :xs="24">
            <el-form-item
              :label="$t('dialog.contact')"
              prop="userName"
            >
              <el-input
                v-model="oneContact.userName"
                :maxlength="16"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24">
            <el-form-item
              :label="$t('dialog.beidouNumber')"
              prop="bdNum"
            >
              <el-input-number
                v-model="oneContact.bdNum"
                :min="1"
                :max="16777215"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label-width="0"
              class="center actions bd-contact-form-operation"
            >
              <el-button
                type="primary"
                :disabled="disAddBtn"
                @click="saveOneContact"
                v-text="saveBtnLabel"
              />
              <el-button
                type="primary"
                :disabled="disAddBtn"
                @click="saveOneContactAndContinue"
                v-text="$t('dialog.keepAdding')"
              />
              <el-button
                type="danger"
                @click="cancelSaveContact"
                v-text="$t('dialog.cancel')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </section>
</template>

<script>
  import { DefModel } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'
  import { cloneDeep, merge } from 'lodash'
  import validateRules from '@/utils/validateRules'

  const OneContact = {
    indexId: 0,
    sortId: 0,
    bdNum: 1,
    userName: '',
    inUse: 1,
  }

  export default {
    name: 'TR925BdContact',
    props: {
      repeaterData: {
        type: [Object, undefined]
      },
      repeater: {
        type: String,
        default: ''
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop
      },
      disabled: {
        type: Boolean,
        default: false
      },
      packageName: {
        type: String,
        default: ''
      },
      deviceModel: {
        type: String,
        default: DefModel
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop
      },
    },
    data() {
      return {
        bdContactTableHeight: '300',
        // 所有联系人数据
        bdContact: [],
        selectedContact: [],
        oneContact: {
          ...OneContact
        },
        // 控制编辑联系人对话框
        showContactSetting: false,
        // 标记已经使用的索引id
        usedIndexIds: {},
        // 标记当前对话框是否处于修改数据状态
        isEdit: false,
        curEditIndexId: -1,
      }
    },
    methods: {
      // 表格选中行事件
      handleSelectionChange(val) {
        this.selectedContact = val
      },
      // 双击表格行弹出窗口以修改数据
      rowDblclickHandler(row, event, option) {
        this.isEdit = true
        this.showContactSetting = true
        this.curEditIndexId = row.indexId
        this.restoreOneContact(row)
      },
      // 弹出添加联系人对话框
      addContact() {
        this.showContactSetting = true
        this.restoreOneContact()
        this.autoFillValue()
      },
      // 删除联系人
      deleteContact() {
        const newBdContact = cloneDeep(this.bdContact)
        for (let i = 0; i < this.selectedContact.length; i++) {
          const item = this.selectedContact[i]

          // 找到相同索引数据id，删除数据，回收索引id
          for (let k = 0; k < newBdContact.length; k++) {
            const bdContact = newBdContact[k]
            if (item.indexId === bdContact.indexId) {
              delete this.usedIndexIds[item.indexId]
              newBdContact.splice(k, 1)
              break
            }
          }
        }

        this.bdContact = newBdContact
      },
      getIndexId() {
        let id = 0
        while (id < 100) {
          if (typeof this.usedIndexIds[id] === 'undefined') {
            this.usedIndexIds[id] = 1
            return id
          }

          id++
        }
        return -1
      },
      // 恢复表单默认参数配置
      restoreOneContact(data = OneContact) {
        this.oneContact = { ...data }
      },
      autoFillValue() {
        this.oneContact.sortId = this.oneContact.indexId = this.getIndexId()
        this.oneContact.bdNum = this.oneContact.indexId + 1
        this.oneContact.userName = `${this.$t('dialog.beidouContact')} ${this.oneContact.bdNum}`
      },
      // 将数据添加到联系人数组集合
      saveToBdContact() {
        this.bdContact.push({ ...this.oneContact })
      },
      updateBdContact(data = this.oneContact) {
        for (let i = 0; i < this.bdContact.length; i++) {
          const item = this.bdContact[i]
          if (item.indexId === data.indexId) {
            this.bdContact[i] = merge(item, data)
            break
          }
        }
        this.bdContact = [...this.bdContact]
      },
      // 保存当前添加的联系人，并关闭对话框
      saveOneContact() {
        this.$refs['bd-contact-form'].validate((valid) => {
          if (!valid) {
            return false
          }

          this.showContactSetting = false

          // 判断是添加还是保存
          if (this.isEdit) {
            this.updateBdContact()
          } else {
            this.saveToBdContact()
          }
        })
      },
      // 保存并继续添加联系人
      saveOneContactAndContinue() {
        this.$refs['bd-contact-form'].validate((valid) => {
          if (!valid) {
            return false
          }

          this.saveToBdContact()
          this.restoreOneContact()
          this.autoFillValue()
        })
      },
      // 取消添加联系人
      cancelSaveContact() {
        if (!this.showContactSetting) {
          return
        }

        this.showContactSetting = false

        if (this.isEdit) {
          // 如果处于编辑修改数据状态，则取消该标记
          this.isEdit = false
          this.curEditIndexId = -1
        } else {
          // 删除当前的索引数据
          delete this.usedIndexIds[this.oneContact.indexId]
        }

        this.$refs['bd-contact-form'].clearValidate()
      },
      closeDlgFn() {
        this.cancelSaveContact()
      },
      getBdContact(indexId) {
        for (let i = 0; i < this.bdContact.length; i++) {
          const item = this.bdContact[i]
          if (item.indexId === indexId) {
            return item
          }
        }

        return undefined
      },
      resetUsedIndex() {
        this.usedIndexIds = this.bdContact.map((item) => {
          return { [item.indexId]: 1 }
        }).reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
      }
    },
    computed: {
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      disAddBtn() {
        return this.bdContact.length === 100
      },
      contactRules() {
        const uniqueRule = (prop, message) => {
          return {
            validator: (rule, value, callback) => {
              const tempData = this.bdContact.filter((item) => {
                // 如果处于编辑修改状态，数据未变化也通过验证
                const valid = item[prop] === value
                return (valid && this.isEdit && this.curEditIndexId === item.indexId) ? false : valid
              })

              if (tempData.length > 0) {
                callback(new Error(message))
              } else {
                callback()
              }
            },
            trigger: ['blur', 'change']
          }
        }

        return {
          userName: [
            validateRules.required(),
            uniqueRule('userName', this.$t('msgbox.bdUserNameUnique'))
          ],
          bdNum: [
            validateRules.required(),
            uniqueRule('bdNum', this.$t('msgbox.bdNumberUnique'))
          ],
        }
      },
      saveBtnLabel() {
        return this.isEdit ? this.$t('dialog.save') : this.$t('dialog.add')
      },
    },
  }
</script>

<style>
  .bd-contact-wrap {
    padding: 10px;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .bd-contact-wrap>.el-table {
    flex: auto;
    height: 100% !important;
  }

  .bd-contact-operation-buttons {
    margin-bottom: 10px;
  }

  .bd-contact-wrap .bd-contact-dlg .el-dialog__body {
    height: unset;
  }
</style>
