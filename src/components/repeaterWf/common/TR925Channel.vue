<template>
  <el-form
    ref="channelSetting"
    :model="oneChannel"
    label-position="top"
    :rules="channelRules"
    class="channel-setting"
  >
    <el-row
      :gutter="20"
      type="flex"
    >
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item
          :label="$t('dialog.chName')"
          prop="chName"
        >
          <el-input
            v-model="oneChannel.chName"
            :maxlength="16"
          />
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.chType')">
          <el-select
            v-model="oneChannel.chType"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in channelTypeList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.defWorkingTimeSlot')">
          <el-select
            v-model="defWorkingTimeSlot"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in defWorkingTimeSlotList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.channelBandwidth')">
          <el-select
            v-model="channelBandwidth"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in channelBandwidthList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.squelchCalibration')">
          <el-select
            v-model="squelchCalibration"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in squelchCalibrationList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.squelchLevel')">
          <el-input-number
            v-model="squelchLevel"
            :min="0"
            :max="15"
          />
        </el-form-item>
      </el-col>

      <el-col
        v-show="!onlyAnalog"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.colorCodes')">
          <el-input-number
            v-model="oneChannel.digitalch.colourCodes"
            :min="0"
            :max="15"
          />
        </el-form-item>
      </el-col>
      <el-col
        v-show="onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.rssiThreshold')">
          <el-input-number
            v-model="rssiThreshold"
            :min="-130"
            :max="-40"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row
      :gutter="20"
      type="flex"
    >
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item>
          <el-checkbox v-model="autoMonitoring">
            <span v-text="$t('dialog.enableAutoMonitoring')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyAnalog"
        :xs="24"
        :sm="12"
      >
        <el-form-item>
          <el-checkbox v-model="voiceFrameRelayCheck">
            <span v-text="$t('dialog.voiceFrameRelayCheck')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row
      :gutter="20"
      type="flex"
    >
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item
          :label="$t('dialog.rxFrequency')"
          prop="rxFrequency"
        >
          <FrequencyMhz
            v-model="oneChannel.rxFrequency"
            :maxlength="9"
          />
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item
          :label="$t('dialog.txFrequency')"
          prop="txFrequency"
        >
          <FrequencyMhz
            v-model="oneChannel.txFrequency"
            :maxlength="9"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row
      :gutter="20"
      type="flex"
    >
      <el-col
        v-show="!onlyAnalog"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.defCommunicationAddress')">
          <el-select
            v-model="oneChannel.digitalch.defaultContactId"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in defaultContactIdList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item
          :label="$t('dialog.txPower')"
          prop="txPower"
        >
          <el-select
            v-model="oneChannel.txPower"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in transmitPowerList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item
          :label="$t('dialog.customVehiclePlv')"
          prop="customVehiclePlv"
        >
          <el-input-number
            v-model="oneChannel.customVehiclePlv"
            :min="1"
            :max="25"
            :disabled="disCustomTxPower"
          />
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item
          :label="$t('dialog.customPiggybackPowerLeve')"
          prop="curstomPowerLv"
        >
          <el-input-number
            v-model="oneChannel.curstomPowerLv"
            :min="0"
            :max="50"
            :disabled="disCustomTxPower"
          />
        </el-form-item>
      </el-col>
      <el-col
        :xs="24"
        :sm="12"
      >
        <el-form-item
          :label="$t('dialog.sendTimeLimiter')"
          prop="totTime"
        >
          <el-input-number
            v-model="oneChannel.totTime"
            :min="0"
            :max="495"
            :step="15"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row
      :gutter="20"
      type="flex"
    >
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.vocalRule')">
          <el-select
            v-model="vocalRule"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in vocalRuleList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.tailSelection')">
          <el-select
            v-model="tailSelection"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="!isRxSubtone"
          >
            <el-option
              v-for="(item, i) in tailSelectionList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item>
          <el-checkbox v-model="tailCancellation">
            <span v-text="$t('dialog.tailCancellation')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.plosive')">
          <el-select
            v-model="plosive"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="!isTxSubtone"
          >
            <el-option
              v-for="(item, i) in tailSelectionList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.receivingSubtoneType')">
          <el-select
            v-model="receivingSubtoneType"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in subsonicTypeList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.transmitSubtoneDigital')">
          <el-select
            v-model="transmitSubtoneDigital"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in subsonicTypeList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.receivingAnalogSubtoneCode')">
          <el-select
            v-model="oneChannel.analogch.rxAnalogSubtone"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="!isRxSubtone"
          >
            <el-option
              v-for="(item, i) in analogSubtoneList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.receivingDigitalSubtoneCode')">
          <el-select
            v-model="oneChannel.analogch.rxDigitalSubtone"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="isRxSubtone"
          >
            <el-option
              v-for="(item, i) in digitalSubtoneList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.transmitAnalogSubtoneCodes')">
          <el-select
            v-model="oneChannel.analogch.txAnalogSubtone"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="!isTxSubtone"
          >
            <el-option
              v-for="(item, i) in analogSubtoneList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        v-show="!onlyDigital"
        :xs="24"
        :sm="12"
      >
        <el-form-item :label="$t('dialog.transmitDigitalSubtoneCodes')">
          <el-select
            v-model="oneChannel.analogch.txDigitalSubtone"
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="isTxSubtone"
          >
            <el-option
              v-for="(item, i) in digitalSubtoneList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

  import { DefModel } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'
  import { cloneDeep, merge } from 'lodash'
  import bfproto from '@/modules/protocol'
  import { defineAsyncComponent } from 'vue'

  const TableId = bfproto.lookupEnum('TableId')
  const Digitalch = {
    defaultContactId: 0,
    colourCodes: 0,
    transferDelay: 0,
    rssiThreshold: 90,
    paraOne: 16,
  }
  const Analogch = {
    paraOne: 48,
    paraTow: 64,
    paraThree: 5,
    rxAnalogSubtone: 67,
    rxDigitalSubtone: 23,
    txAnalogSubtone: 67,
    txDigitalSubtone: 23,
  }
  const OneChannel = {
    // 值范围：0-3775
    chId: 0,
    chName: '',
    chType: 0,
    customVehiclePlv: 15,
    txPower: 1,
    curstomPowerLv: 10,
    paraOne: 9,
    totTime: 120,
    rxFrequency: 400000000,
    txFrequency: 400000000,
    digitalch: { ...Digitalch },
    analogch: { ...Analogch },
  }

  export default {
    name: 'TR925Channel',
    props: {
      repeaterData: {
        type: [Object, undefined]
      },
      repeater: {
        type: String,
        default: ''
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop
      },
      disabled: {
        type: Boolean,
        default: false
      },
      packageName: {
        type: String,
        default: ''
      },
      deviceModel: {
        type: String,
        default: DefModel
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop
      },
      dataId: {
        type: Number,
        default: -1
      },
      tableId: {
        type: Number,
        default: TableId.RepeaterMainZone
      }
    },
    data() {
      return {
        oneChannel: { ...OneChannel }
      }
    },
    methods: {
      // 计算指定字节数值
      setSpecifiedNumberBitValue({ originVal = 0, maxVal = 0, value = 0, bit = 0 }) {
        // 先置空指定位的数据，(originVal | maxVal) ^ maxVal)
        // 再将需要计算的值左移，(value << bit)
        // 最后按位或上数据
        return ((originVal | maxVal) ^ maxVal) | (value << bit)
      },
      getSpecifiedNumberBitValue({ originVal = 0, maxVal = 0, bit = 0 }) {
        return (originVal & maxVal) >> bit
      },
      getOneChannelById(dataId) {
        for (const k in this.channels) {
          const item = this.channels[k]
          if (item.chId === dataId) {
            return item
          }
        }

        return undefined
      },
    },
    computed: {
      wfSettings() {
        return this.repeaterData && this.repeaterData.writeFrequencySetting
      },
      channels() {
        return this.wfSettings && this.wfSettings[TableId[TableId.RepeaterOneChannel]]
      },
      channelRules() {
        return {}
      },
      channelTypeList() {
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0
          },
          {
            label: this.$t('dialog.analogChannel'),
            value: 1
          },
          {
            label: this.$t('dialog.digitalAnalogChannel'),
            value: 2
          },
        ]
      },
      transmitPowerList() {
        return [
          {
            label: this.$t('dialog.tr925LowPower'),
            value: 0
          },
          {
            label: this.$t('dialog.tr925MediumPower'),
            value: 1
          },
          {
            label: this.$t('dialog.tr925HighPower'),
            value: 2
          },
          {
            label: this.$t('dialog.customPower'),
            value: 3
          },
        ]
      },
      disCustomPower() {
        return this.oneChannel.txPower !== 3
      },
      channelBandwidthList() {
        return [
          {
            label: '12.5k',
            value: 0
          },
          {
            label: '25k',
            value: 1
          },
        ]
      },
      analogSubtoneList() {
        return [
          { label: '62.5', value: 625 },
          { label: '67', value: 670 },
          { label: '69.3', value: 693 },
          { label: '71.9', value: 719 },
          { label: '74.4', value: 744 },
          { label: '77', value: 770 },
          { label: '79.7', value: 797 },
          { label: '82.5', value: 825 },
          { label: '85.4', value: 854 },
          { label: '88.5', value: 885 },
          { label: '91.5', value: 915 },
          { label: '94.8', value: 948 },
          { label: '97.4', value: 974 },
          { label: '100', value: 1000 },
          { label: '103.5', value: 1035 },
          { label: '107.2', value: 1072 },
          { label: '110.9', value: 1109 },
          { label: '114.8', value: 1148 },
          { label: '118.8', value: 1188 },
          { label: '123', value: 1230 },
          { label: '127.3', value: 1273 },
          { label: '131.8', value: 1318 },
          { label: '136.5', value: 1365 },
          { label: '141.3', value: 1413 },
          { label: '146.2', value: 1462 },
          { label: '151.4', value: 1514 },
          { label: '156.7', value: 1567 },
          { label: '159.8', value: 1598 },
          { label: '162.2', value: 1622 },
          { label: '165.5', value: 1655 },
          { label: '167.9', value: 1679 },
          { label: '171.3', value: 1713 },
          { label: '173.8', value: 1738 },
          { label: '177.3', value: 1773 },
          { label: '179.9', value: 1799 },
          { label: '183.5', value: 1835 },
          { label: '186.2', value: 1862 },
          { label: '189.9', value: 1899 },
          { label: '192.8', value: 1928 },
          { label: '196.6', value: 1966 },
          { label: '199.5', value: 1995 },
          { label: '203.5', value: 2035 },
          { label: '206.5', value: 2065 },
          { label: '210.7', value: 2107 },
          { label: '218.1', value: 2181 },
          { label: '225.7', value: 2257 },
          { label: '229.1', value: 2291 },
          { label: '233.6', value: 2336 },
          { label: '241.8', value: 2418 },
          { label: '250.3', value: 2503 },
          { label: '254.1', value: 2541 }
        ]
      },
      digitalSubtoneList() {
        return [
          { label: '023', value: 23 },
          { label: '025', value: 25 },
          { label: '026', value: 26 },
          { label: '031', value: 31 },
          { label: '032', value: 32 },
          { label: '043', value: 43 },
          { label: '047', value: 47 },
          { label: '051', value: 51 },
          { label: '054', value: 54 },
          { label: '065', value: 65 },
          { label: '071', value: 71 },
          { label: '072', value: 72 },
          { label: '073', value: 73 },
          { label: '074', value: 74 },
          { label: '114', value: 114 },
          { label: '115', value: 115 },
          { label: '116', value: 116 },
          { label: '125', value: 125 },
          { label: '131', value: 131 },
          { label: '132', value: 132 },
          { label: '134', value: 134 },
          { label: '143', value: 143 },
          { label: '152', value: 152 },
          { label: '155', value: 155 },
          { label: '156', value: 156 },
          { label: '162', value: 162 },
          { label: '165', value: 165 },
          { label: '172', value: 172 },
          { label: '174', value: 174 },
          { label: '205', value: 205 },
          { label: '223', value: 223 },
          { label: '226', value: 226 },
          { label: '243', value: 243 },
          { label: '244', value: 244 },
          { label: '245', value: 245 },
          { label: '251', value: 251 },
          { label: '261', value: 261 },
          { label: '263', value: 263 },
          { label: '265', value: 265 },
          { label: '271', value: 271 },
          { label: '306', value: 306 },
          { label: '311', value: 311 },
          { label: '315', value: 315 },
          { label: '331', value: 331 },
          { label: '343', value: 343 },
          { label: '346', value: 346 },
          { label: '351', value: 351 },
          { label: '364', value: 364 },
          { label: '365', value: 365 },
          { label: '371', value: 371 },
          { label: '411', value: 411 },
          { label: '412', value: 412 },
          { label: '413', value: 413 },
          { label: '423', value: 423 },
          { label: '431', value: 431 },
          { label: '432', value: 432 },
          { label: '445', value: 445 },
          { label: '464', value: 464 },
          { label: '465', value: 465 },
          { label: '466', value: 466 },
          { label: '503', value: 503 },
          { label: '506', value: 506 },
          { label: '516', value: 516 },
          { label: '532', value: 532 },
          { label: '546', value: 546 },
          { label: '565', value: 565 },
          { label: '606', value: 606 },
          { label: '612', value: 612 },
          { label: '624', value: 624 },
          { label: '627', value: 627 },
          { label: '631', value: 631 },
          { label: '632', value: 632 },
          { label: '654', value: 654 },
          { label: '662', value: 662 },
          { label: '664', value: 664 },
          { label: '703', value: 703 },
          { label: '712', value: 712 },
          { label: '723', value: 723 },
          { label: '731', value: 731 },
          { label: '732', value: 732 },
          { label: '734', value: 734 },
          { label: '743', value: 743 },
          { label: '754', value: 754 }
        ]
      },

      // RSSI阈值 值范围：0-90 表现为：-130～-40
      rssiThreshold: {
        get() {
          return this.oneChannel.digitalch.rssiThreshold - 130
        },
        set(val) {
          this.oneChannel.digitalch.rssiThreshold = val + 130
        },
      },

      // paraOne 参数自动计算
      // bit0= 静噪校准 bit1~2=默认工作时隙 bit3=自动监听 bit4~7=保留
      squelchCalibration: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.paraOne,
            maxVal: 0x01,
            bit: 0
          })
        },
        set(val) {
          this.oneChannel.paraOne = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.paraOne,
            maxVal: 0x01,
            value: val,
            bit: 0
          })
        },
      },
      defWorkingTimeSlot: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.paraOne,
            maxVal: 0x04,
            bit: 1
          })
        },
        set(val) {
          // 先置空指定位数据，再按位或上数据，0x06为该配置的最大值
          this.oneChannel.paraOne = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.paraOne,
            maxVal: 0x04,
            value: val,
            bit: 1
          })
        },
      },
      autoMonitoring: {
        get() {
          return (this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.paraOne,
            maxVal: 0x08,
            bit: 3
          }) & 0x01) === 1
        },
        set(val) {
          this.oneChannel.paraOne = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.paraOne,
            maxVal: 0x08,
            value: val,
            bit: 3
          })
        },
      },
      squelchCalibrationList() {
        return [
          {
            label: this.$t('dialog.bandpassFilter'),
            value: 0
          },
          {
            label: this.$t('dialog.electricalAdjustmentFilter'),
            value: 1
          },
        ]
      },
      defWorkingTimeSlotList() {
        return [
          {
            label: this.$t('dialog.timeSlot', { num: 1 }),
            value: 0
          },
          {
            label: this.$t('dialog.timeSlot', { num: 2 }),
            value: 1
          },
          {
            label: this.$t('dialog.simulation'),
            value: 2
          },
        ]
      },

      // digitalch.paraOne
      // bit0= 加密使能 bit1~3=加密类型
      // bit4=语音帧中转校验 bit5~7=保留
      voiceFrameRelayCheck: {
        get() {
          return (this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.digitalch.paraOne,
            maxVal: 0x10,
            bit: 4
          }) & 0x01) === 1
        },
        set(val) {
          this.oneChannel.digitalch.paraOne = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.digitalch.paraOne,
            maxVal: 0x10,
            value: val,
            bit: 4
          })
        },
      },

      // analogch.paraOne
      // bit0= 信道带宽 bit1~3=保留
      // bit4~7=静噪等级
      channelBandwidth: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraOne,
            maxVal: 0x01,
            bit: 0
          })
        },
        set(val) {
          this.oneChannel.analogch.paraOne = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraOne,
            maxVal: 0x01,
            value: val,
            bit: 0
          })
        },
      },
      squelchLevel: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraOne,
            maxVal: 0xF0,
            bit: 4
          })
        },
        set(val) {
          this.oneChannel.analogch.paraOne = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraOne,
            maxVal: 0xF0,
            value: val,
            bit: 4
          })
        },
      },

      // analogch.paraTow
      // bit0~1= 保留 bit2~3=发声规则
      // bit4=尾音选择 bit5=尾音消除 bit6~7=接收亚音类型
      vocalRule: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0x08,
            bit: 2
          })
        },
        set(val) {
          this.oneChannel.analogch.paraTow = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0x08,
            value: val,
            bit: 2
          })
        },
      },
      tailSelection: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0x16,
            bit: 4
          })
        },
        set(val) {
          this.oneChannel.analogch.paraTow = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0x16,
            value: val,
            bit: 4
          })
        },
      },
      tailCancellation: {
        get() {
          return (this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0x20,
            bit: 5
          }) & 0x01) === 1
        },
        set(val) {
          this.oneChannel.analogch.paraTow = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0x20,
            value: val,
            bit: 5
          })
        },
      },
      receivingSubtoneType: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0xC0,
            bit: 6
          })
        },
        set(val) {
          this.oneChannel.analogch.paraTow = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraTow,
            maxVal: 0xC0,
            value: val,
            bit: 6
          })
        },
      },
      vocalRuleList() {
        return [
          {
            label: this.$t('dialog.standardEnableAndMute'),
            value: 0
          },
          {
            label: this.$t('dialog.versusEnableAndMute'),
            value: 1
          },
          {
            label: this.$t('dialog.versusEnableOrMute'),
            value: 2
          },
        ]
      },
      tailSelectionList() {
        return [
          {
            label: this.$t('dialog.standard'),
            value: 0
          },
          {
            label: this.$t('dialog.nonStandard'),
            value: 1
          },
        ]
      },
      subsonicTypeList() {
        return [
          // {
          //   label: this.$t("dialog.nothing"),
          //   value: 0
          // },
          {
            label: this.$t('dialog.subAudio'),
            value: 1
          },
          {
            label: this.$t('dialog.subtoneDigital'),
            value: 2
          },
          {
            label: this.$t('dialog.reverseSubtoneDigital'),
            value: 3
          },
        ]
      },

      // analogch.paraThree
      // bit0~1= 发射亚音类型 bit2=爆破音 bit3~7=保留
      transmitSubtoneDigital: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraThree,
            maxVal: 0x03,
            bit: 0
          })
        },
        set(val) {
          this.oneChannel.analogch.paraThree = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraThree,
            maxVal: 0x03,
            value: val,
            bit: 0
          })
        },
      },
      plosive: {
        get() {
          return this.getSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraThree,
            maxVal: 0x04,
            bit: 2
          })
        },
        set(val) {
          this.oneChannel.analogch.paraThree = this.setSpecifiedNumberBitValue({
            originVal: this.oneChannel.analogch.paraThree,
            maxVal: 0x04,
            value: val,
            bit: 2
          })
        },
      },

      // 默认通信地址列表，DMR通讯录中组呼DMRID
      defaultContactIdList() {
        return []
      },
      disCustomTxPower() {
        return this.oneChannel.txPower !== 3
      },
      onlyDigital() {
        return this.oneChannel.chType === 0
      },
      onlyAnalog() {
        return this.oneChannel.chType === 1
      },
      hasDigitalAnalog() {
        return this.oneChannel.chType === 2
      },
      isRxSubtone() {
        return this.receivingSubtoneType === 1
      },
      isTxSubtone() {
        return this.transmitSubtoneDigital === 1
      },
    },
    components: {
      FrequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
    },
    watch: {
      dataId(val) {
        if (this.tableId !== TableId.RepeaterOneChannel || val < 0) {
          return
        }
        this.oneChannel = merge(this.oneChannel, OneChannel, cloneDeep(this.getOneChannelById(val) || {}))
      },
    }
  }
</script>

<style>
  .channel-setting .el-row {
    flex-wrap: wrap;
    align-items: flex-end;
  }
</style>
