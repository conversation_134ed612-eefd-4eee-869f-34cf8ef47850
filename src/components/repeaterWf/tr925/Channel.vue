<template>
  <el-form
    ref="channelSetting"
    :model="channelSetting"
    label-width="auto"
    :rules="channelSettingRules"
    class="tr925-channel-form"
  >
    <el-form-item
      :label="$t('dialog.chId')"
      prop="chId"
    >
      <el-input-number
        v-model="chId"
        :min="1"
        :max="99"
        :step="1"
        :disabled="queryCurChInfo"
      />
    </el-form-item>
    <el-form-item
      :label="$t('dialog.chName')"
      prop="chName"
    >
      <el-input
        v-model="channelSetting.chName"
        :maxlength="16"
        :disabled="!modify"
      />
    </el-form-item>
    <el-form-item
      :label="$t('dialog.chType')"
      prop="chType"
    >
      <el-select
        v-model="channelSetting.chType"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        :disabled="!modify"
      >
        <el-option
          v-for="(item, i) in channelTypes"
          :key="i"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      :label="$t('dialog.txPower')"
      prop="txPower"
    >
      <el-select
        v-model="channelSetting.txPower"
        :placeholder="$t('dialog.select')"
        filterable
        :no-match-text="$t('dialog.noMatchText')"
        :disabled="!modify"
      >
        <el-option
          v-for="(item, i) in txPowerTypes"
          :key="i"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      :label="$t('dialog.rxFrequency')"
      prop="rxFrequency"
    >
      <FrequencyMhz
        v-model="channelSetting.rxFrequency"
        :maxlength="9"
        :disabled="!modify"
      />
    </el-form-item>
    <el-form-item
      :label="$t('dialog.txFrequency')"
      prop="txFrequency"
    >
      <FrequencyMhz
        v-model="channelSetting.txFrequency"
        :maxlength="9"
        :disabled="!modify"
      />
    </el-form-item>
    <el-form-item
      :label="$t('dialog.colorCodes')"
      prop="colourCodes"
    >
      <el-input-number
        v-model="channelSetting.colourCodes"
        :min="0"
        :max="15"
        :step="1"
        :disabled="!modify"
      />
    </el-form-item>
    <el-form-item
      label=" "
      class="center actions"
    >
      <template v-if="queryCurChInfo">
        <el-button
          type="primary"
          :disabled="disabled"
          @click="queryRepeaterCurChInfo"
          v-text="$t('dialog.querySetting')"
        />
        <el-button
          v-if="modify"
          type="primary"
          :disabled="disabled"
          @click="writeInCurChInfo"
          v-text="$t('dialog.writeIn')"
        />
      </template>

      <template v-else>
        <el-button
          type="primary"
          class="channel-settings-btn"
          :disabled="disabled"
          @click="queryChannelSettings(0)"
          v-text="$t('dialog.queryAllChannel')"
        />
        <el-button
          type="primary"
          class="channel-settings-btn"
          :disabled="disabled"
          @click="queryChannelSetting"
          v-text="$t('dialog.querySetting')"
        />
        <el-button
          type="warning"
          class="channel-settings-btn"
          :disabled="disabled"
          @click="writeInChannelSetting"
          v-text="$t('dialog.writeIn')"
        />
        <el-button
          type="danger"
          class="channel-settings-btn"
          :disabled="disabled"
          @click="deleteChannelSetting"
          v-text="$t('dialog.delete')"
        />
      </template>
    </el-form-item>
  </el-form>
</template>

<script>
  import { cloneDeep, merge } from 'lodash'
  import i18n from '@/modules/i18n'
  import bfproto, { kcpPackageName } from '@/modules/protocol'
  import bfutil from '@/utils/bfutil'
  import { readStringU16, writeStringU16 } from '@/writingFrequency/interphone/common'
  import repeaterWfMod, { DefModel, TR925Models } from '@/writingFrequency/repeater'
  import { defineAsyncComponent } from 'vue'

  const ChannelSetting = {
    chId: 0,
    chName: `${i18n.global.t('dialog.channel')} 1`,
    chType: 0,
    colourCodes: 1,
    rxFrequency: 0,
    txFrequency: 0,
    txPower: 0,
  }
  const RepeaterCurChInfo = {
    zoneId: {
      // 一级区域ID
      mainZoneId: 0,
      // 二级区域ID
      subZoneId: 0,
      // 三级区域ID
      userZoneId: 0,
    },
    channelInfo: {
      ...ChannelSetting,
    },
    curstomPowerLv: 0,
  }

  export default {
    name: 'BfTr925Channel',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: kcpPackageName,
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
      queryCurChInfo: {
        type: Boolean,
        default: false,
      },
      modify: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        repeaterReplyPrefix: 'repeater',
        repeaterCurChInfo: cloneDeep(RepeaterCurChInfo),
      }
    },
    methods: {
      getAllChannelSetting() {
        if (!this.repeaterData) {
          return undefined
        }
        const writeFrequencySetting = this.repeaterData.writeFrequencySetting
        if (!writeFrequencySetting) {
          return undefined
        }
        const allChannelSetting = writeFrequencySetting.allChannelSetting
        if (!allChannelSetting) {
          return undefined
        }

        return cloneDeep(allChannelSetting)
      },
      setChannelSetting(data) {
        this.channelSetting = Object.assign({}, this.channelSetting, data)
      },
      getChannelSetting() {
        return Object.assign({}, this.channelSetting)
      },

      // 保存读取的信道数据
      saveChannelData(settings, isEnd) {
        // 同步本地中继所有信道数据
        let allChannelSetting = this.getAllChannelSetting()
        if (!allChannelSetting) {
          allChannelSetting = {}
        }
        allChannelSetting[settings.chId] = bfproto.copyFieldsFromProto(settings)
        this.saveMethod('allChannelSetting', allChannelSetting)

        if (this.channelSetting.chId === settings.chId) {
          this.setChannelSetting(settings)
        }

        // 全部查询完成，判断当前显示的信道是否拥有，没有则显示默认的第一个信道
        if (!isEnd) {
          return
        }

        if (allChannelSetting) {
          const chIds = Object.keys(allChannelSetting)
          if (!chIds.includes(this.channelSetting.chId + '')) {
            this.setChannelSetting(allChannelSetting[chIds.shift()])
          }
        }
      },
      // 信道设置
      queryChannelSettings(chNo) {
        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 5,
            tableId: 6,
            objIndex: chNo,
          },
          stream: !chNo,
        })

        // 全部查询信道，需要订阅相关消息事件
        if (options.stream) {
          bfglob.on(this.repeaterReplyPrefix + options.paraInt, (rpc_cmd_obj, isEnd) => {
            this.saveChannelData(rpc_cmd_obj.body, isEnd)
          })

          bfglob.once('failed:' + this.repeaterReplyPrefix + options.paraInt, (rpc_cmd_obj) => {
            bfglob.console.warn('queryChannelSettings failed', rpc_cmd_obj)
          })
        }

        repeaterWfMod.queryConfig(options)
          .then(res => {
            this.saveChannelData(res, true)
          })
          .catch(err => {
            bfglob.console.error('queryChannelSettings', err)
          })
      },
      queryChannelSetting() {
        this.queryChannelSettings(this.channelSetting.chId)
      },
      writeInChannelSetting() {
        this.$refs.channelSetting.validate((valid) => {
          if (!valid) {
            return false
          }

          const channelData = this.getChannelSetting()
          const options = merge(this.defQueryOption, {
            paraBin: {
              operation: 6,
              tableId: 6,
              objIndex: channelData.chId,
            },
          })

          repeaterWfMod.writeInData(channelData, options)
            .then(res => {
              return this.saveChannelData(channelData, true)
            })
            .catch(err => {
              bfglob.console.error('writeInCurChInfo', err)
            })
        })
      },
      deleteChannelConfigByChId(chId) {
        const allChannelSetting = this.getAllChannelSetting()
        if (!allChannelSetting) {
          return
        }

        // eslint-disable-next-line
        delete this.repeaterData.writeFrequencySetting.allChannelSetting[chId]
        // eslint-disable-next-line
        this.repeaterData.writeFrequencySetting.allChannelSetting = { ...this.repeaterData.writeFrequencySetting.allChannelSetting }
      },
      deleteChannelSetting() {
        this.$refs.channelSetting.validate((valid) => {
          if (!valid) {
            return false
          }

          const channelData = this.getChannelSetting()
          const options = merge(this.defQueryOption, {
            paraBin: {
              operation: 7,
              tableId: 6,
              objIndex: channelData.chId,
            },
          })

          repeaterWfMod.writeInData(channelData, options)
            .then(res => {
              // 删除本地对应的信道数据
              this.deleteChannelConfigByChId(channelData.chId)

              const allChannelSetting = this.getAllChannelSetting()
              const chIds = Object.keys(allChannelSetting)
              this.setChannelSetting(allChannelSetting[chIds.shift()])
            })
            .catch(err => {
              bfglob.console.error('deleteChannelSetting', err)
            })
        })
      },

      decodeChannelName(bytesName) {
        if (TR925Models.includes(this.deviceModel.slice(0, 6))) {
          if (Array.isArray(bytesName)) {
            bytesName = new Uint8Array(bytesName)
          }
          return readStringU16(bytesName, 0, bytesName.length)
        }
        return bytesName
      },
      encodeChannelName(strName) {
        if (TR925Models.includes(this.deviceModel.slice(0, 6))) {
          return writeStringU16(strName)
        }
        return strName
      },
      // 10:查询中继当前信道信息
      queryRepeaterCurChInfo() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 10,
          },
          decodeMsgType: 'RepeaterCurChInfo',
        })

        repeaterWfMod.queryConfig(options)
          .then(res => {
            // 解码bytes类型的信道名称
            res.channelInfo.chName = this.decodeChannelName(res.channelInfo.chName)
            this.repeaterCurChInfo = merge(this.repeaterCurChInfo, res)
            this.saveMethod('repeaterCurChInfo', this.repeaterCurChInfo)
          })
          .catch(err => {
            bfglob.console.error('queryRepeaterCurChInfo', err)
          })
      },

      // 13:修改当前信道信道
      writeInCurChInfo() {
        this.$refs.channelSetting.validate()
          .then(() => {
            const channelData = cloneDeep(this.repeaterCurChInfo)
            channelData.channelInfo.chName = this.encodeChannelName(channelData.channelInfo.chName)
            const options = merge(this.defQueryOption, {
              paraBin: {
                operation: 13,
              },
              decodeMsgType: 'RepeaterCurChInfo',
            })

            repeaterWfMod.writeInData(channelData, options)
              .then(res => {
                console.log('writeIn success', res)
                // return this.saveChannelData(channelData, true)
              })
              .catch(err => {
                bfglob.console.error('writeInCurChInfo', err)
              })
          })
      },
    },
    computed: {
      defQueryOption() {
        return {
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          paraBin: {
            operation: 5,
          },
          decodeMsgType: 'RepeaterOneChannel',
          packageName: this.packageName,
        }
      },
      channelTypes() {
        // 0-数字信道
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0,
          },
        ]
      },
      // 功率选项
      tr925PowerTypes() {
        // TR925: 0=低功率 1=中功率 2=高功率 3自定义功率
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.mid'),
            value: 1,
          },
          {
            label: this.$t('dialog.high'),
            value: 2,
          },
          {
            label: this.$t('dialog.customize'),
            value: 3,
          },
        ]
      },
      tr805005PowerTypes() {
        // BF8100: 0=低功率 1=高功率
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.high'),
            value: 1,
          },
        ]
      },
      txPowerTypes() {
        if (TR925Models.includes(this.deviceModel.slice(0, 6))) {
          return this.tr925PowerTypes
        }
        return this.tr805005PowerTypes
      },
      repeaterInfo() {
        return (this.repeaterData && this.repeaterData.writeFrequencySetting &&
          this.repeaterData.writeFrequencySetting.repeaterInfo) || {}
      },
      channelSettingRules() {
        const frequencyValidator = (rule, value, callback) => {
          if (!value) {
            callback(new Error(this.$t('dialog.requiredRule')))
          } else if (isNaN(value)) {
            callback(new Error(this.$t('msgbox.mustNumber')))
          } else if (!this.repeaterInfo.lowFrequency || !this.repeaterInfo.highFrequency) {
            // 没有获取到高、低频信息，不做规则验证
            callback()
          } else if (parseFloat(value) < parseFloat(this.repeaterInfo.lowFrequency)) {
            callback(new Error(this.$t('msgbox.lowFrequency')))
          } else if (parseFloat(value) > parseFloat(this.repeaterInfo.highFrequency)) {
            callback(new Error(this.$t('msgbox.highFrequency')))
          } else {
            callback()
          }
        }
        return {
          txFrequency: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                frequencyValidator(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
          rxFrequency: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                frequencyValidator(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
          chName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
          ],
        }
      },
      channelSetting: {
        get() {
          return this.repeaterCurChInfo.channelInfo
        },
        set(val) {
          Object.assign(this.repeaterCurChInfo, val)
        },
      },
      chId: {
        get() {
          return this.channelSetting.chId + 1
        },
        set(val) {
          Object.assign(this.repeaterCurChInfo, {
            chId: val - 1,
          })
        },
      },
    },
    components: {
      FrequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
    },
  }
</script>

<style></style>
