<template>
  <el-main
    class="!flex flex-col items-center w-full repeater-write-freq"
    :class="$i18n.locale"
  >
    <el-form class="!flex-none !h-auto w-sm repeater-select-form">
      <el-form-item :label="$t('repeaterWriteFreq.repeaterModel')">
        <el-select
          v-model="repeaterModel"
          :placeholder="$t('dialog.select')"
          filterable
          clearable
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="(item, i) in repeaterModelList"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('repeaterWriteFreq.repeaterDevice')">
        <el-select
          v-model="repeater"
          :placeholder="$t('dialog.select')"
          filterable
          clearable
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="item in repeaterList"
            :key="item.label"
            :label="item.label"
            :value="item.dmrId"
            :disabled="item.disabled"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!--使用keep-alive缓存组件数据、状态，以便下次快速渲染-->
    <keep-alive>
      <div class="!flex-auto w-full repeater-config-container">
        <component
          :is="autoComponent"
          :repeaterModel="selectedRepeaterModel"
          :repeater="repeater"
          :repeaterData="repeaterData"
          :getRepeaterId="getRepeaterId"
          :saveMethod="setWriteFrequencySetting"
        />
      </div>
    </keep-alive>
  </el-main>
</template>

<script>
  import { cloneDeep, merge } from 'lodash'
  import { ControllerTypes } from '@/utils/bfutil'
  import { getModelName } from '@/writingFrequency/modelInfo'
  import RepeaterWfMixin from '@/utils/repeaterWfMixin'
  import { getRepeaterModelList, getRepeaterModelName } from '@/writingFrequency/customModelConfig'
  import * as Models from '@/writingFrequency/interphone/models'
  import { TR925Models } from '@/writingFrequency/repeater'
  import { DZ1480, DZ148SVT } from '@/writingFrequency/interphone/models'
  import { useRouteParams } from '@/router'
  import { markRaw } from 'vue'

  // 上一个选中的中继缓存，以便在切换型号时，能够还原
  const lastCheckedRepeater = {}
  const customModelList = getRepeaterModelList()
  const supportModels = customModelList.length > 0 ? customModelList : [
    Models.TR805005,
    Models.TR900M,
    Models.TR90FR,
    Models.TR925M,
    Models.DZ1480,
    Models.DZ148SVT,
    Models.TR850M,
    Models.BR105M,
  ]
  const defaultModel = Models.TR805005
  const { getRouteParams } = useRouteParams()

  const metaPages = import.meta.glob([
    '@/components/repeaterWf/modelView/*.vue'
  ])

  export default {
    name: 'RepeaterWriteFrequency',
    mixins: [RepeaterWfMixin],
    data() {
      return {
        repeaterData: undefined,
        repeater: '',

        globalRepeaters: bfglob.gcontrollers.getAll(),
        onlineRepeater: [],
        offlineRepeater: [],
        repeaterIds: [],

        repeaterInfo: {
          deviceModel: '',
          version: '',
          lowFrequency: 0,
          highFrequency: 0,
          sn: '',
        },
        autoComponent: undefined,

        repeaterModel: supportModels.includes(defaultModel) ? defaultModel : (supportModels[0] ?? ''),
      }
    },
    mounted() {
      bfglob.on('update_controller_stats', this.update_controller_stats)
    },
    methods: {
      getModel6Bit(model) {
        return model.slice(0, 6)
      },
      update_controller_stats(repeater) {
        // stats: 0断开，1连接，3报警
        if (this.repeaterData && this.repeaterData.dmrId === repeater.dmrId) {
          this.repeaterData.ctrlStats = repeater.ctrlStats
        }
      },
      setRepeaterData(dmrId) {
        this.repeaterData = bfglob.gcontrollers.getDataByIndex(dmrId)
      },

      getRepeaterId() {
        return this.repeaterIds.shift()
      },
      // 保存配置数据
      setWriteFrequencySetting(key, data) {
        const _key = 'writeFrequencySetting'
        if (typeof this.repeaterData[_key] === 'undefined') {
          this.repeaterData[_key] = {}
        }

        this.repeaterData[_key][key] = cloneDeep(data)

        return this.repeaterData[_key][key]
      },

      // 动态加载组件
      dynamicLoadingComponent(src) {
        if (typeof src !== 'string' || !src) {
          return new Promise(resolve => {
            resolve(undefined)
          })
        }

        return metaPages[`/src/components/repeaterWf/modelView/${src}.vue`]().then(res => {
          return markRaw(res.default || res)
        })
      },
    },
    computed: {
      repeaterModelTypeList() {
        const modelTypeList = {}
        const setModelTypeList = (model, option) => {
          if (!modelTypeList[model]) {
            modelTypeList[model] = []
          }
          modelTypeList[model].push(option)
        }
        // 控制器类型 0:中继 2:电话网关 1:同播中继 3：同播控制器 4: 虚拟集群控制器 5: 虚拟集群中继
        const canUseType = [0, 3, 4]
        for (const k in this.globalRepeaters) {
          const item = this.globalRepeaters[k]
          // 过滤非中继类型的设备
          if (!canUseType.includes(item.controllerType)) {
            continue
          }
          // 因为中继写频要区分型号，不在线的中继无法确定型号，需要过滤
          // ctrlStats: 0断开，1连接，3报警,-1为初始化值
          if (item.ctrlStats === 0 || item.ctrlStats === -1) {
            continue
          }

          // 生成在线中继数据，包含中继的型号，以便按型号生成对应的下拉列表
          const option = {
            label: `${item.selfId} / ${item.dmrId}`,
            dmrId: item.dmrId,
            rid: item.rid,
            disabled: false,
            model: item.model,
          }

          // 跳过型号有异常的数据
          if (item.model.length < 6) {
            continue
          }
          // 兼容中继包含2字节功能码的类型
          const model = this.getModel6Bit(item.model)
          setModelTypeList(model, option)

          // 兼容R7F的型号机型
          switch (model) {
            case Models.RR850M:
              setModelTypeList(Models.TR850M, option)
              break
            case Models.RR900M:
              setModelTypeList(Models.TR900M, option)
              break
            default:
              break
          }
        }

        return modelTypeList
      },
      // 按机型返回不同的中继列表
      repeaterList() {
        // 中继型号为6字节ASCII码+2字节的功能码组成，需要兼容同一个型号不同功能码的型号
        const model = this.getModel6Bit(this.repeaterModel)
        return this.repeaterModelTypeList[model] || []
      },
      // 没有选中继设备，或中继不在线，则禁用按钮功能
      notRepeater() {
        return !this.repeater || !this.repeaterData || this.repeaterData.ctrlStats !== 1
      },
      // 当前支持写频的中继型号列表
      repeaterModelList() {
        return supportModels.map(model => {
          return {
            value: model,
            label: getRepeaterModelName(model) || getModelName(model),
          }
        }).sort((a, b) => {
          return a.label.localeCompare(b.label)
        })
      },
      selectedRepeaterModel() {
        const repeaterOption = this.repeaterList.find(item => item.dmrId === this.repeater)
        return repeaterOption?.model ?? this.repeaterModel
      },
    },
    watch: {
      repeaterIds: {
        immediate: true,
        handler(val) {
          // id个数少于3个时才去请求
          const idsLen = val.length
          if (idsLen > 3) {
            return
          }

          // 请求中继写频id，作为rpc.para_int的值
          const repeaterIdsUrl = '/repeater-op-no'
          fetch(repeaterIdsUrl)
            .then(res => res.text())
            .then(data => {
              // 返回id的初始值，表示后面的10个id均可用
              let id = parseInt(data)
              let limit = 10
              const repeaterIds = []
              while (limit > 0) {
                repeaterIds.push(id++)
                limit--
              }
              this.repeaterIds = this.repeaterIds.concat(repeaterIds)
            })
            .catch(err => {
              // 抛出错误
              throw err
            })
        },
      },
      repeater(val) {
        this.repeaterInfo.deviceModel = ''

        if (val) {
          // 找到对应的中继
          this.setRepeaterData(val)
        } else {
          this.repeaterData = undefined
        }
      },
      'repeaterData.writeFrequencySetting.repeaterInfo': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.repeaterInfo = merge(this.repeaterInfo, val)
          }
        },
      },
      // 中继型号变更，切换对应的写频组件
      repeaterModel: {
        immediate: true,
        handler(val, oldVal) {
          let model = val
          // TR925M等机型功能与TR900M一样，加载TR900M写频组件
          if (TR925Models.some(m => model.startsWith(m))) {
            model = Models.TR900M
          }

          // 切换型号，清除选中的中继
          if (oldVal) {
            lastCheckedRepeater[oldVal] = this.repeater
          }
          this.repeater = ''

          this.dynamicLoadingComponent(model)
            .then(res => {
              this.autoComponent = res
              // 组件加载后，恢复该型号选中的中继
              this.repeater = lastCheckedRepeater[val] || ''
            })
        },
      },
    },
    beforeUnmount() {
      bfglob.off('open_vrepeaterWriteFrequency', this.open_vrepeaterWriteFrequency)
      bfglob.off('update_controller_stats', this.update_controller_stats)
    },
    activated() {
      this.$route.params = getRouteParams(this.$route.name)
      if (this.$route.params.fromController) {
        const {
          rid,
          parentRid
        } = this.$route.params
        const data = bfglob.gcontrollers.get(rid)
        const parent = bfglob.gcontrollers.get(parentRid)
        if (!data || !parent) return

        const parentisOnline = parent.ctrlStats === 1
        const parentDmrId = parent.dmrId
        const isSvtRepeater = parent.controllerType === ControllerTypes.VCController
        this.repeaterModel = isSvtRepeater ? DZ148SVT : DZ1480

        if (parentisOnline) {
          this.repeater = parentDmrId
          // 打开同播中继操作界面
          if (isSvtRepeater) {
            bfglob.emit('openSvtRepeaterOperation', data)
          } else {
            bfglob.emit('openSimulcastOperation', data)
          }
        }
      }
    },
  }
</script>

<style lang="scss">
  .el-main.repeater-write-freq {

    &>.el-form,
    &>.repeater-config-container {
      max-width: 800px;
    }

    &:has(.dz148-wrapper)>.repeater-config-container {
      max-width: 1152px;
    }

    .repeater-config-container .el-tabs.el-tabs--left>.el-tabs__content {
      border-left: 2px solid var(--el-border-color);
      margin-left: -11px;
      padding-left: 11px;
    }

    .repeaterCmd.el-tabs {
      width: 100%;
      border-top: 1px solid #ddd;
      display: flex;

      .el-tabs__content {
        padding: 12px;
      }

      &.el-tabs--left .el-tabs__content {
        height: 100%;
      }

      .el-tab-pane {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .el-form {
          // tailwindcss w-sm
          width: 384px;

          .el-form-item.actions .el-form-item__content {
            justify-content: center;

            &>.el-button {
              min-width: 32%;
            }
          }
        }

        &.repeaterCurChSet .el-form {
          .el-input-number {
            width: 100%;
          }
        }
      }
    }
  }
</style>
