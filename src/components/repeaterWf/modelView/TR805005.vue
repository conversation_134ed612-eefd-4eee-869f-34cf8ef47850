<template>
  <el-tabs
    v-model="settingRadio"
    tab-position="left"
    class="repeaterCmd tr805005"
    :class="{ 'is-mobile': isMobile }"
  >
    <el-tab-pane
      lazy
      :label="$t('dialog.repeaterInfo')"
      name="1"
      class="repeaterInfo"
    >
      <RepeaterInfo
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :disabled="notRepeater"
        :saveMethod="saveMethod"
      />
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.generalSetting')"
      name="2"
      class="commonSetting"
    >
      <el-form
        ref="commonSetting"
        :model="commonSetting"
        label-width="auto"
        :rules="commonSettingRules"
      >
        <el-form-item
          :label="$t('dialog.repeaterName')"
          prop="devName"
        >
          <el-input
            v-model="commonSetting.devName"
            :maxlength="8"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.repeaterId')"
          prop="repeaterId"
        >
          <el-input
            v-model.number="commonSetting.repeaterId"
            :maxlength="8"
          />
        </el-form-item>
        <el-form-item label="DMRID">
          <generateDmrId
            v-model="commonSetting.dmrid"
            isDec
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.hangTime')"
          prop="hangTime"
        >
          <el-input-number
            v-model="commonSetting.hangTime"
            :min="1200"
            :max="6000"
            :step="30"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.squelchLevel')"
          prop="squelchLevel"
        >
          <el-input-number
            v-model="commonSetting.squelchLevel"
            :min="0"
            :max="16"
            :step="1"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.defaultChannel')"
          prop="defaultChannel"
        >
          <el-input-number
            v-model="commonSetting.defaultChannel"
            :min="1"
            :max="30"
            :step="1"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.soundTip')"
          prop="soundTip"
        >
          <el-switch
            v-model="soundTip"
            :active-text="$t('dialog.on')"
            :inactive-text="$t('dialog.off')"
          />
        </el-form-item>
        <el-form-item
          label=" "
          class="center actions"
        >
          <el-button
            type="primary"
            :disabled="notRepeater"
            @click="queryCommonSetting"
            v-text="$t('dialog.querySetting')"
          />
          <el-button
            type="warning"
            :disabled="notRepeater"
            @click="writeInCommonSetting"
            v-text="$t('dialog.writeIn')"
          />
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.buttonDefinition')"
      name="3"
      class="buttonDefinition"
    >
      <RepeaterKey
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
      />
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.channelSetting')"
      name="4"
      class="channelSetting"
    >
      <el-tabs
        class="w-full"
        v-model="channelSettingModel"
        type="border-card"
      >
        <el-tab-pane
          :label="$t('dialog.channelList')"
          name="list"
        >
          <el-table
            :data="channelDatas"
            border
            highlight-current-row
            max-height="350px"
            style="width: 100%"
            @row-dblclick="showSpecifiedChannel"
          >
            <el-table-column
              v-for="(column, i) in channelTableColumns"
              :key="i"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
            >
              <template #default="scope">
                <span
                  v-if="column.prop == 'txPower' && scope.row[column.prop] == 0"
                  v-text="$t('dialog.low')"
                />
                <span
                  v-else-if="column.prop == 'txPower' && scope.row[column.prop] == 1"
                  v-text="$t('dialog.high')"
                />
                <span
                  v-else-if="column.prop == 'rxFrequency' || column.prop == 'txFrequency'"
                  v-text="hz2Mhz(scope.row[column.prop])"
                />
                <span
                  v-else
                  v-text="scope.row[column.prop]"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane
          :label="$t('dialog.channelSetting')"
          name="setting"
        >
          <TR805005Channel
            ref="channelSetting"
            :disabled="notRepeater"
            :repeater="repeater"
            :repeaterData="repeaterData"
            :getRepeaterId="getRepeaterId"
            :saveMethod="saveMethod"
            modify
          />
        </el-tab-pane>
      </el-tabs>
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.networkSetting')"
      name="5"
      class="networkSetting"
    >
      <NetworkSetting
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
      />
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.serverSetting')"
      name="6"
      class="serverSetting"
    >
      <ServerSetting
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
      />
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.curChannelInfo')"
      name="10"
      class="repeaterCurChInfo"
    >
      <TR805005Channel
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
        queryCurChInfo
      />
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.switchChannel')"
      name="11"
      class="repeaterCurChSet"
    >
      <SwitchChannel
        class="w-full"
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
      />
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.switchTransmitPower')"
      name="12"
      class="repeaterCurPowerSet"
    >
      <SwitchTransmitPower
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
      />
    </el-tab-pane>
    <el-tab-pane
      lazy
      :label="$t('dialog.restartRepeater')"
      name="7"
      class="restartRepeater"
    >
      <Restart
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
  import { cloneDeep, merge } from 'lodash'
  import RepeaterInfo from '@/components/repeaterWf/common/RepeaterInfo'
  import { kcpPackageName } from '@/modules/protocol'
  import bfutil from '@/utils/bfutil'
  import repeaterWfMod from '@/writingFrequency/repeater'
  import { defineAsyncComponent } from 'vue'

  const CommonSetting = {
    devName: '',
    dmrid: 0,
    repeaterId: '',
    hangTime: 5000,
    soundTip: 1,
    squelchLevel: 5,
    defaultChannel: 1,
  }

  export default {
    name: 'TR805005',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
    },
    data() {
      return {
        channelSettingModel: 'setting',
        settingRadio: '1',

        commonSetting: {
          ...CommonSetting,
        },
        channelDatas: [],
      }
    },
    methods: {
      getFormName(settingRadio = this.settingRadio) {
        let name = ''
        switch (settingRadio) {
          case '2':
            name = 'commonSetting'
            break
        }

        return name
      },
      getDecodeMsgType(settingRadio = this.settingRadio) {
        let decodeMsgType = 'RepeaterInfo'
        switch (settingRadio) {
          case '2':
            decodeMsgType = 'RepeaterCommonSetting'
            break
        }

        return decodeMsgType
      },

      saveConfig(data) {
        const propName = this.getFormName()
        const val = merge(this[propName], data)

        this[propName] = val
        this.saveMethod(propName, val)

        return this[propName]
      },

      // 常规设置
      queryCommonSetting() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            tableId: 2,
          },
        })

        repeaterWfMod.queryConfig(options)
          .then(res => {
            return this.saveConfig(res)
          })
          .catch(err => {
            bfglob.console.error('queryCommonSetting', err)
          })
      },
      writeInCommonSetting() {
        this.$refs.commonSetting.validate((valid) => {
          if (!valid) {
            return false
          }
          // 判断dmrid是否已变更，如果已变更则用旧的dmrid
          let dmrId = this.commonSetting.dmrid
          const oldDmrid = this.repeaterData.writeFrequencySetting.commonSetting.dmrid
          if (dmrId !== oldDmrid) {
            dmrId = oldDmrid
          }

          const options = merge(this.defQueryOption, {
            sid: dmrId,
            paraBin: {
              operation: 6,
              tableId: 2,
            },
          })

          repeaterWfMod.writeInData(this.commonSetting, options)
            .then(res => {
              return this.saveConfig(this.commonSetting)
            })
            .catch(err => {
              bfglob.console.error('writeInCommonSetting', err)
            })
        })
      },

      showSpecifiedChannel(row) {
        if (!this.$refs.channelSetting && !this.$refs.channelSetting.setChannelSetting) {
          return
        }

        this.$refs.channelSetting.setChannelSetting(row)
        this.channelSettingModel = 'setting'
      },
      hz2Mhz(val) {
        return bfutil.frequencyHz2Mhz(val)
      },
    },
    computed: {
      fullscreen() {
        return !(this.$root.layoutLevel > 0)
      },
      isEn() {
        return this.$i18n.locale === 'en'
      },
      isMobile() {
        return this.$root.layoutLevel === 0
      },
      commonSettingRules() {
        return {
          devName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                // 检测输入的字符串是否有中文
                bfutil.cannotIncludeChineseRule(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
          repeaterId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                // 检测输入的字符串只能为数字或者空值
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
        }
      },
      channelTableColumns() {
        return [
          {
            prop: 'chId',
            label: this.$t('dialog.chId'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '110px' : '80px',
          },
          {
            prop: 'chName',
            label: this.$t('dialog.chName'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '130px' : '100px',
          },
          {
            prop: 'rxFrequency',
            label: this.$t('dialog.rxFrequency'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '230px' : '130px',
          },
          {
            prop: 'txFrequency',
            label: this.$t('dialog.txFrequency'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '230px' : '130px',
          },
          {
            prop: 'txPower',
            label: this.$t('dialog.txPower'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '140px' : '100px',
          },
        ]
      },
      // 没有选中继设备，或中继不在线，则禁用按钮功能
      notRepeater() {
        return !this.repeater || !this.repeaterData || this.repeaterData.ctrlStats !== 1
      },

      // 常规设置计算属性
      soundTip: {
        get() {
          return this.commonSetting.soundTip === 1
        },
        set(val) {
          const soundTip = val ? 1 : 0
          if (this.commonSetting.soundTip !== soundTip) {
            this.commonSetting.soundTip = soundTip
          }
          return val
        },
      },

      defQueryOption() {
        return {
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          paraBin: {
            operation: 5,
          },
          decodeMsgType: this.getDecodeMsgType(),
          packageName: kcpPackageName,
        }
      },
    },
    watch: {
      'repeaterData.writeFrequencySetting.allChannelSetting': {
        immediate: true,
        deep: true,
        handler(val) {
          // 生成表格需要的数据数组
          const data = []
          for (const k in val) {
            data.push(cloneDeep(val[k]))
          }
          this.channelDatas = data
        },
      },
      'repeaterData.writeFrequencySetting.commonSetting': {
        immediate: true,
        deep: true,
        handler(val) {
          this.commonSetting = merge({}, val || CommonSetting)
        },
      },
    },
    components: {
      generateDmrId: defineAsyncComponent(() => import('@/components/common/generateDmrId')),
      RepeaterInfo,
      Restart: defineAsyncComponent(() => import('@/components/repeaterWf/common/Restart')),
      SwitchTransmitPower: defineAsyncComponent(() => import('@/components/repeaterWf/common/SwitchTransmitPower')),
      SwitchChannel: defineAsyncComponent(() => import('@/components/repeaterWf/common/SwitchChannel')),
      NetworkSetting: defineAsyncComponent(() => import('@/components/repeaterWf/common/NetworkSetting')),
      RepeaterKey: defineAsyncComponent(() => import('@/components/repeaterWf/common/RepeaterKey')),
      ServerSetting: defineAsyncComponent(() => import('@/components/repeaterWf/common/ServerSetting')),
      TR805005Channel: defineAsyncComponent(() => import('@/components/repeaterWf/common/TR805005Channel')),
    },
  }
</script>

<style lang="scss">
  .el-tabs.repeaterCmd.tr805005 {
    .el-tab-pane.commonSetting .el-form {
      width: 432px;
    }

    .el-tab-pane.channelSetting>.el-tabs {
      width: 100%;
    }
  }
</style>
