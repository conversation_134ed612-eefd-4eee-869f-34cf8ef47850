<template>
  <el-main
    id="bfnotes"
    class="note-container gap-2"
    @click="noteTitleClick"
  >
    <el-card
      ref="notes"
      class="box-card notes flex-1"
    >
      <div class="notesbox">
        <div
          v-for="(item, index) in notes"
          :key="index"
          class="note_text"
        >
          <span
            class="note_time"
            v-text="item.time"
          />
          <span
            class="note_content"
            v-html="item.content"
          />
        </div>
      </div>
    </el-card>
    <el-card
      v-if="showSystemLog"
      class="system-log-card flex-1"
    >
      <template #header>
        <div class="flex justify-between">
          <span>{{ dialogTitle }}</span>
          <el-button
            icon="close"
            circle
            @click="showSystemLog = false"
          />
        </div>
      </template>
      <system-log />
    </el-card>
  </el-main>
</template>

<script>
  import { defineAsyncComponent } from 'vue'
  import bfTime from '@/utils/time'

  export default {
    name: 'BfNotes',
    data() {
      return {
        notes: [],
        noteTitleClickCount: 0,
        noteTitleClickTimer: null,
        showSystemLog: false,
      }
    },
    methods: {
      noteTitleClick() {
        if (this.noteTitleClickTimer === null) {
          // 2秒内连续点击标题5次以上,则打开系统日志窗口
          this.noteTitleClickTimer = setTimeout(() => {
            this.noteTitleClickCount = 0
            this.noteTitleClickTimer = null
          }, 2 * 1000)
        }
        // 统计点击次数
        this.noteTitleClickCount++
        if (this.noteTitleClickCount >= 5) {
          this.noteTitleClickCount = 0
          clearTimeout(this.noteTitleClickTimer)
          this.noteTitleClickTimer = null

          setTimeout(() => {
            this.showSystemLog = true
          }, 100)
        }
      },
      addnote(msg) {
        this.notes.push(
          Object.freeze({ time: bfTime.nowLocalTime(), content: msg }),
        )
        // 保持日志滚动条在底部
        this.scrollbar_keep_bottom()
      },
      // 定时清除日志，只保留最后的200条记录
      checked_notes_logs() {
        const len = this.notes.length
        if (len > 400) {
          this.notes.splice(0, len - 200)
        }
      },
      scrollbar_keep_bottom() {
        var notes = this.$refs.notes
        if (!notes) {
          return
        }
        var el = notes.$el
        if (!el) {
          return
        }
        el.scrollTop = el.scrollHeight
      },
    },
    mounted() {
      bfglob.off('addnote')
      this.notes = bfglob.cacheNotes
      delete bfglob.cacheNotes
      bfglob.on('addnote', function(msg) {
        this.addnote(msg)
      }.bind(this))
      // 监听日志滚动条保持在底部消息
      bfglob.on('notes_scrollbar_keep_bottom', function() {
        setTimeout(this.scrollbar_keep_bottom, 50)
      }.bind(this))
      bfglob.emit('notes_scrollbar_keep_bottom')
      bfglob.emit('notesLoaded')
      bfglob.on('remove-tag', (tag) => {
        if (tag.nav.index === 'notes') {
          this.showSystemLog = false
        }
      })
    },
    activated() {
      bfglob.emit('notes_scrollbar_keep_bottom')
    },
    computed: {
      fullscreen() {
        return this.bfmaxi ? true : !(this.$root.layoutLevel > 0)
      },
      dialogTitle() {
        return this.$t('nav.systemLog')
      },
    },
    beforeMount() {
      // 10分钟检测一次日志数量
      setInterval(this.checked_notes_logs, 10 * 60 * 1000)
    },
    components: {
      SystemLog: defineAsyncComponent(() => import('@/components/secondary/systemLog.vue')),
    }
  }
</script>

<style lang='scss'>

  .note-container {
    display: flex;
  }

  .system-log-card {
    .el-card__body {
      padding: 0;
    }

    .el-card__header {
      padding: 4px 4px 4px 12px;

      span {
        font-size: 18px;
        line-height: 28px;
      }
    }

    height: 100%;
  }

  .box-card.notes {
    overflow: auto;
    height: 100%;
    margin: 0 auto;
    box-shadow: unset;
    max-width: 1024px;
  }

  .notesbox {
    text-align: left;
    line-height: 22px;
  }

  .note_text {
    display: flex;
  }

  .note_time {
    flex: none;
    margin-right: 6px;
  }

  .note_content {
    flex: auto;
  }
</style>
