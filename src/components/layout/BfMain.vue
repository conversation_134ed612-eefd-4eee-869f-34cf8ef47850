<template>
  <el-main class="flex page-main">
    <bf-map class="flex-auto mapbox-container" />
    <bf-tree
      class="flex-none"
      :treeId="treeId"
    />
  </el-main>
</template>

<script>
  import { defineAsyncComponent } from 'vue'
  import { defaultTreeId } from '@/utils/bftree'

  export default {
    name: 'BfMain',
    data() {
      return {
        treeId: defaultTreeId,
      }
    },
    components: {
      bfMap: defineAsyncComponent(() => import('@/components/layout/BfMap.vue')),
      bfTree: defineAsyncComponent(() => import('@/components/layout/BfTree.vue'))
    }
  }
</script>

<style lang="scss">
  .el-main.page-main {
    height: calc(100vh - var(--head-width));
    display: flex;
    padding: 0;
  }
</style>
