<template>
  <el-scrollbar
    ref="scrollContainer"
    :vertical="false"
    class="tags-view-wrapper"
    @wheel.prevent="handleScroll"
  >
    <section class="flex gap-2 items-center p-0.5 h-full w-full quick-routing-tags-container">
      <router-link
        v-for="tag in tags"
        :key="tag.route.path"
        class="cursor-pointer tags-view-item"
        :to="tag.route.path"
      >
        <el-tag
          :closable="!isAffix(tag.route)"
          :type="isActive(tag.route) ? 'primary' : 'info'"
          :effect="isActive(tag.route) ? 'light' : 'plain'"
          @close.stop.prevent="closeSelectedTag(tag)"
        >
          {{ tag.nav.text() }}
        </el-tag>
      </router-link>
    </section>
  </el-scrollbar>
</template>

<script>
  export default {
    name: 'QuickRoutingTags',
    emits: ['remove'],
    props: {
      tags: {
        type: Array,
        required: true,
      },
    },
    methods: {
      // 处理鼠标滚轮事件，让导航条横向滚动
      handleScroll(e) {
        const eventDelta = -e.wheelDelta || -e.deltaY * 60
        const $scrollWrapper = this.scrollWrapper
        $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta
      },
      closeSelectedTag(tag) {
        this.$emit('remove', tag)
        bfglob.emit('remove-tag', tag)
      },
      isAffix(route) {
        return route.meta?.affix ?? false
      },
      isActive(route) {
        return route.name === this.$route.name
      },
    },
    computed: {
      // el-scrollbar组件内部滚动条组件实例
      scrollWrapper() {
        return this.$refs.scrollContainer.wrapRef
      },
    },
  }
</script>

<style lang="scss">
  .el-scrollbar.tags-view-wrapper {
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 34px;

    @media (min-width: 2560px) {
      height: 54px;

      .el-scrollbar__view {
        height: 44px;
      }
    }
  }

</style>
