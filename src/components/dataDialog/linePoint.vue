<template>
  <data-form-editor
    ref="formEditor"
    class="page-line-point"
    editor-class="line-point-editor"
    :title="dlgTitle"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :getFormRef="() => $refs.linePointDataEditorForm"
    :before-action="beforeAction"
    @row-dblclick="tableRowDoublebClick"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form
        ref="linePointDataEditorForm"
        :model="formData"
        :label-width="labelWidth"
        :rules="rules"
      >
        <el-form-item
          prop="orgId"
          class="form-item-ellipsis"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.parentOrg')"
            >{{ $t('dialog.parentOrg') }}</span>
          </template>
          <el-select
            v-model="formData.orgId"
            filterable
            clearable
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="item in selOrgList"
              :key="item.rid"
              :label="item.label"
              :value="item.rid"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="pointId"
          class="form-item-ellipsis"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.serialNo')"
            >{{ $t('dialog.serialNo') }}</span>
          </template>
          <el-input
            v-model="formData.pointId"
            :maxlength="16"
          />
        </el-form-item>
        <el-form-item
          prop="pointName"
          class="form-item-ellipsis"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.pointName')"
            >{{ $t('dialog.pointName') }}</span>
          </template>
          <el-input
            v-model="formData.pointName"
            :maxlength="16"
          />
        </el-form-item>
        <el-form-item
          prop="pointRfid"
          class="form-item-ellipsis input-with-select"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.pointRfid')"
            >{{ $t('dialog.pointRfid') }}</span>
          </template>
          <el-input
            v-model="formData.pointRfid"
            :maxlength="10"
            @change="val => pointRfidChange(formData, val)"
          >
            <template #prepend>
              <el-select
                v-model="formData.rfid"
                :no-match-text="$t('dialog.noMatchText')"
                :no-data-text="$t('msgbox.emptyText')"
                @change="(val) => checkedPointRfid(formData, val)"
              >
                <template #prefix>
                  <el-tooltip
                    placement="top"
                    :content="$t('dialog.readRfidFormDev')"
                  >
                    <div class="h-full flex items-center">
                      <i class="mdi mdi-help-circle-outline" />
                    </div>
                  </el-tooltip>
                </template>

                <el-option
                  v-for="(item, index) in readRfidList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  class="flex gap-2 items-center"
                >
                  <span v-text="item.value" />
                  <span
                    class="text-xs text-gray-400"
                    v-text="item.dmrId"
                  />
                  <span
                    class="text-xs text-gray-400"
                    v-text="item.readTime"
                  />
                </el-option>
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="form-item-ellipsis line-point-lon-lat-container">
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.lngLat')"
            >{{ $t('dialog.lngLat') }}</span>
          </template>
          <lon-lat
            v-model:lon="formData.lon"
            v-model:lat="formData.lat"
          />
        </el-form-item>
        <el-form-item
          prop="startShowLevel"
          class="form-item-ellipsis"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.showLevel')"
            >{{ $t('dialog.showLevel') }}</span>
          </template>
          <el-input-number
            v-model="formData.startShowLevel"
            :min="1"
            :max="18"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.linePointType')"
          prop="pointType"
        >
          <el-select
            v-model="formData.pointType"
            :placeholder="$t('dialog.select')"
          >
            <el-option
              v-for="(item, index) in pointTypes"
              :key="index"
              :label="item.label"
              :value="item.value"
              class="flex gap-2 justify-between items-center"
            >
              <span v-text="item.label" />
              <img
                class="line-point-type-select-icon"
                :src="item.fileContent"
                alt=""
              >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="gpsPointRadius"
          class="form-item-ellipsis"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.gpsRadius')"
            >{{ $t('dialog.gpsRadius') }}</span>
          </template>
          <el-input-number
            v-model="formData.gpsPointRadius"
            :min="formData.pointType !== 3 ? 0 :10"
            :max="100"
            :step="5"
            :disabled="formData.pointType !== 3"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.notes')"
          prop="note"
        >
          <el-input
            v-model="formData.note"
            type="textarea"
            resize="none"
            :rows="3"
            :maxlength="128"
          />
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil from '@/utils/bfutil'

  import maputil from '@/utils/map'
  import bfNotify from '@/utils/notify'
  import validateRules from '@/utils/validateRules'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import DataFormEditor, { EditStatus } from '@/components/common/DataFormEditor.vue'
  import { cloneDeep } from 'lodash'
  import { useRouteParams } from '@/router'
  import { defineAsyncComponent } from 'vue'

  const dbSubject = `db.${bfglob.sysId}`
  const defaultData = {
    orgId: bfutil.getBaseDataOrgId(),
    pointId: bfutil.getBaseSelfId(),
    pointName: '',
    pointRfid: '',
    lon: '',
    lat: '',
    startShowLevel: 15,
    pointType: 1,
    gpsPointRadius: 30,
    note: '',

    rfid: '',
    lonLat: {},
  }
  const { getRouteParams } = useRouteParams()

  export default {
    mixins: [vueMixin],
    data() {
      return {
        dataTable: {
          name: 'linePointTable',
          body: bfutil.objToArray(bfglob.glinePoints.getAll()),
        },
        selOrgList: bfglob.gorgData.getList(),
        readRfidList: [],
      }
    },
    methods: {
      async onDelete(row) {
        await this.delete_linePoint_data(row, dbCmd.DB_LINE_POINT_DELETE)
      },
      async onUpdate(row, done) {
        const isOk = await this.update_linePoint_data(row, dbCmd.DB_LINE_POINT_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        const isOk = await this.add_linePoint_data(row, dbCmd.DB_LINE_POINT_INSERT)
        if (!isOk) return
        if (addNewCb) {
          const __data = this.getNewData()
          __data.orgId = row.orgId
          __data.pointId = bfutil.customNumberIncrement(row.pointId)
          __data.startShowLevel = row.startShowLevel
          __data.pointType = row.pointType
          // 重置标签页数据
          bfutil.resetForm(this, 'linePointDataEditorForm')
          this.readRfidList = []
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(defaultData)
      },

      pointRfidChange(formData, val) {
        formData.pointRfid = val.padStart(10, '0').toUpperCase()
      },
      /**
       * 表格行双击事件
       * @param {DataRow} row
       * @param {jQuery.Event} e
       */
      tableRowDoublebClick(row, e) {
        maputil.dbclickJumpToMarker(row, 2)
      },
      add_linePoint_data(data, add_cmd) {
        const msgObj = {
          ...data,
          rid: uuid(),
          mapDisplayName: data.pointId,
          gpsPointRadius: data.pointType === 3 ? (parseInt(data.gpsPointRadius) || 50) : 0,
          imgOrColorPoint: 1,
          pointImg: '*************-2222-2222-************',
          colorR: 255,
          colorG: 0,
          colorB: 0,
          lastLpalarmTime: '1970-01-01',
        }

        return bfproto.sendMessage(add_cmd, msgObj, 'db_line_point', dbSubject).then(rpc_cmd_obj => {
          bfglob.console.log('add device res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
            bfglob.emit('add_global_linePointData', msgObj)

            this.readRfidList = this.readRfidList.filter(item => item.value !== msgObj.pointRfid)

            // 添加查询日志
            const note = this.$t('dialog.add') + msgObj.pointId + ' / ' + msgObj.pointName +
              this.$t('msgbox.linePointData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_line_point_point_rfid_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatPointRfid'))
            } else {
              bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            }
          }
          return Promise.resolve(isOk)
        }).catch(err => {
          bfglob.console.warn('add linePoint timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
          return Promise.resolve(false)
        })
      },
      update_linePoint_data(data, up_db_cmd) {
        const msgObj = {
          ...data,
          gpsPointRadius: data.pointType === 3 ? (parseInt(data.gpsPointRadius) || 50) : 0,
          imgOrColorPoint: 1,
          pointImg: '*************-2222-2222-************',
          colorR: 255,
          colorG: 0,
          colorB: 0,
          lastLpalarmTime: data.lastLpalarmTime || '1970-01-01',
        }

        return bfproto.sendMessage(up_db_cmd, msgObj, 'db_line_point', dbSubject).then(rpc_cmd_obj => {
          bfglob.console.log('update linePoint res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
            // 更新全局组织机构数据
            bfglob.emit('update_global_linePointData', msgObj)

            // 添加查询日志
            const note = this.$t('dialog.update') + msgObj.pointId + ' / ' + msgObj.pointName +
              this.$t('msgbox.linePointData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_line_point_point_rfid_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatPointRfid'))
            } else {
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            }
          }
          return Promise.resolve(isOk)
        }).catch(err => {
          bfglob.console.warn('update linePoint timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          return Promise.resolve(false)
        })
      },
      delete_linePoint_data(data, del_cmd) {
        return bfproto.sendMessage(del_cmd, data, 'db_line_point', dbSubject).then(rpc_cmd_obj => {
          bfglob.console.log('delete linePoint res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
            bfglob.emit('delete_global_linePointData', data)

            // 添加查询日志
            const note = this.$t('dialog.delete') + data.pointId + ' / ' + data.pointName +
              this.$t('msgbox.linePointData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_line_detail_point_id_fkey')) {
              bfNotify.warningBox(this.$t('msgbox.delLinePointError'))
            } else {
              bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            }
          }
          return Promise.resolve(isOk)
        }).catch(err => {
          bfglob.console.warn('delete linePoint timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          return Promise.resolve(false)
        })
      },
      // 从对讲机读取rfid卡号
      checkedPointRfid(formData, val) {
        formData.pointRfid = val
        formData.rfid = ''
      },
      setAutoCompleteRfidList(rfid_obj) {
        const index = this.readRfidList.findIndex(item => item.value === rfid_obj.value)
        if (index === -1) {
          this.readRfidList.push(rfid_obj)
        } else {
          this.readRfidList[index] = rfid_obj
        }
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = bfutil.objToArray(bfglob.glinePoints.getAll())
      },
      onShowgpsLinePointdetail(params) {
        this.$nextTick(() => {
          this.$refs.formEditor?.$refs.dataTable?.instance.search(params.rfid).draw()
        })
      },
      beforeAction(status, row) {
        if (status === EditStatus.Edit && row.pointType !== 3) {
          // 非虚拟点， gpsPointRadius为""， 该属性绑定的input-number,只能是数字
          row.gpsPointRadius = row.gpsPointRadius ? row.gpsPointRadius : 0
        }
        return Promise.resolve(true)
      }
    },
    mounted() {
      bfglob.on('add_global_linePointData', this.upsetDataTableBody)
      bfglob.on('update_global_linePointData', this.upsetDataTableBody)
      bfglob.on('delete_global_linePointData', this.upsetDataTableBody)
      bfglob.on('show_gpsLinePoint_detail', this.onShowgpsLinePointdetail)
    },
    components: {
      DataFormEditor,
      LonLat: defineAsyncComponent(() => import('@/components/common/lonLat.vue')),
    },
    watch: {
      fullscreen: {
        handler(newVal) {
          bfglob.emit(this.dataTable.name, newVal)
        },
      },
    },
    computed: {
      dthead() {
        return [
          {
            title: this.$t('dialog.picture'),
            data: 'picture',
            render: function (data, type, row, meta) {
              if (row.fileContent) {
                return `<img src='${row.fileContent}' class='custorm_pic_style' alt="">`
              } else {
                return `<b style='${row.background}' class='mapPoint_bg'></b>`
              }
            },
            width: '50px',
          },
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.serialNo'),
            data: 'pointId',
            width: '80px',
          },
          {
            title: this.$t('dialog.pointName'),
            data: 'pointName',
            width: this.isFR ? '160px' : this.isEN ? '130px' : '100px',
          },
          {
            title: this.$t('dialog.pointRfid'),
            data: 'pointRfid',
            width: this.isFR ? '160px' : this.isEN ? '120px' : '90px',
          },
          {
            title: this.$t('dialog.linePointType'),
            data: null,
            width: '100px',
            render: (data, type, row, meta) => {
              data.typeName = bfutil.getPointTypeName(data.pointType)
              return data.typeName
            },
          },
          {
            title: this.$t('dialog.showLevel'),
            data: 'startShowLevel',
            width: this.isFR ? '140px' : this.isEN ? '100px' : '80px',
          },
          {
            title: this.$t('dialog.gpsRadius'),
            data: 'gpsPointRadius',
            width: this.isFR || this.isEN ? '130px' : '110px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '150px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('dialog.linePointTitle')
      },
      rules() {
        return {
          orgId: [
            validateRules.required('blur'),
          ],
          pointId: [
            validateRules.required('blur'),
          ],
          pointName: [
            validateRules.required('blur'),

          ],
          pointRfid: [
            validateRules.required('blur'),
            validateRules.hex('blur', this.$t('msgbox.mustBeHexChar')),
          ],
          lon: [
            validateRules.required('blur'),
            {
              validator: function (rule, value, callback) {
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: 'blur',
            },

          ],
          lat: [
            validateRules.required('blur'),
            {
              validator: function (rule, value, callback) {
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: 'blur',
            },
          ],
        }
      },
      pointTypes() {
        const pointTypes = [
          {
            value: 1,
            label: this.$t('dialog.Nsource'),
            fileContent: bfutil.getPointTypeFileContent(1),
          },
          {
            value: 2,
            label: this.$t('dialog.Hsource'),
            fileContent: bfutil.getPointTypeFileContent(2),
          },
          {
            value: 3,
            label: this.$t('dialog.Gsource'),
            fileContent: bfutil.getPointTypeFileContent(3),
          },
        ]

        if (bfglob.sysIniConfig.iotEnable) {
          pointTypes.push({
            value: 4,
            label: this.$t('dialog.BaseStationPoint'),
            fileContent: bfutil.getPointTypeFileContent(4),
          })
        }

        return pointTypes
      },
      labelWidth() {
        return '160px'
      },
    },
    activated() {
      // 订阅dc01命令消息，添加巡查点rfid设置下拉列表
      bfglob.on('linePoint_readRfidList', this.setAutoCompleteRfidList)
      this.$route.params = getRouteParams(this.$route.name)
      if (this.$route.params.rfid) {
        this.onShowgpsLinePointdetail(this.$route.params)
      }
    },
    deactivated() {
      bfglob.off('linePoint_readRfidList', this.setAutoCompleteRfidList)
    },
  }
</script>

<style lang="scss">
  .data-form-dialog.el-dialog.line-point-editor {
    .el-form {
      max-width: 500px;
    }

    .input-with-select .el-input-group__prepend {
      background-color: var(--el-fill-color-blank);

      .el-select {
        width: 136px;
      }
    }

    .form-item-label {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .page-line-point {
    .custorm_pic_style {
      width: 22px;
      height: 22px;
      margin: 0 auto;
      border-radius: 50%;
    }
  }

  .line-point-type-select-icon {
    width: 22px;
    height: 22px;
  }
</style>
