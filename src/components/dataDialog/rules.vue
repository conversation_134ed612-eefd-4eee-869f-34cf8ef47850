<template>
  <data-form-editor
    ref="formEditor"
    class="page-line-rules"
    editor-class="line-rules-editor"
    :title="$t('dialog.ruleTitle')"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :getFormRef="() => $refs.lineRulesDataEditorForm"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form
        ref="lineRulesDataEditorForm"
        :model="formData"
        :label-width="labelWidth"
        :rules="rules"
        :validate-on-rule-change="false"
        class="grid grid-cols-2 gap-x-2"
      >
        <el-form-item
          :label="$t('dialog.parentOrg')"
          prop="orgId"
        >
          <el-select
            v-model="formData.orgId"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="item in selOrgList"
              :key="item.rid"
              :label="item.label"
              :value="item.rid"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('dialog.serialNo')"
          prop="ruleId"
        >
          <el-input
            v-model="formData.ruleId"
            :maxlength="16"
          />
        </el-form-item>
        <el-form-item
          prop="ruleName"
          class="form-item-ellipsis"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.ruleName')"
            >{{ $t('dialog.ruleName') }}</span>
          </template>
          <el-input
            v-model="formData.ruleName"
            :maxlength="16"
          />
        </el-form-item>
        <el-form-item
          prop="ruleLineRid"
          class="form-item-ellipsis"
        >
          <template #label>
            <span
              class="form-item-label"
              :title="$t('dialog.insLine')"
            >{{ $t('dialog.insLine') }}</span>
          </template>
          <el-select
            v-model="formData.ruleLineRid"
            :placeholder="$t('dialog.select')"
          >
            <el-option
              v-for="item in selLineList"
              :key="item.rid"
              :label="item.label"
              :value="item.rid"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('dialog.checkDate')"
          class="col-span-2 checkDate"
        >
          <el-form-item prop="day_1">
            <el-checkbox
              v-model="formData.day_1"
              :label="$t('dialog.Mon')"
            />
          </el-form-item>
          <el-form-item prop="day_2">
            <el-checkbox
              v-model="formData.day_2"
              :label="$t('dialog.Tues')"
            />
          </el-form-item>
          <el-form-item prop="day_3">
            <el-checkbox
              v-model="formData.day_3"
              :label="$t('dialog.Wed')"
            />
          </el-form-item>
          <el-form-item prop="day_4">
            <el-checkbox
              v-model="formData.day_4"
              :label="$t('dialog.Thur')"
            />
          </el-form-item>
          <el-form-item prop="day_5">
            <el-checkbox
              v-model="formData.day_5"
              :label="$t('dialog.Fri')"
            />
          </el-form-item>
          <el-form-item prop="day_6">
            <el-checkbox
              v-model="formData.day_6"
              :label="$t('dialog.Sat')"
            />
          </el-form-item>
          <el-form-item prop="day_7">
            <el-checkbox
              v-model="formData.day_7"
              :label="$t('dialog.Sun')"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item
          :label="$t('dialog.checkStartTime')"
          prop="checkStartTime"
        >
          <el-time-picker
            v-model="formData.checkStartTime"
            :placeholder="$t('dialog.checkStartTime')"
            format="HH:mm"
            value-format="HH:mm"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.checkCount')"
          prop="checkCount"
        >
          <el-input-number
            v-model="formData.checkCount"
            :min="1"
            :max="999"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.haveruntime')"
          prop="checkAllTime"
        >
          <el-input-number
            v-model="formData.checkAllTime"
            :min="0"
            :max="999"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.effectType')"
          prop="ruleEffectiveType"
        >
          <el-select
            v-model="formData.ruleEffectiveType"
            @change="(val) => checkedRuleEffectiveType(formData, val)"
          >
            <el-option
              v-for="(item, index) in effectiveTypes"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('dialog.effectStartTime')"
          prop="ruleEffectiveStart"
        >
          <el-date-picker
            v-model="formData.ruleEffectiveStart"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled="formData.ruleEffectiveType === 0"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.effectEndTime')"
          prop="ruleEffectiveEnd"
        >
          <el-date-picker
            v-model="formData.ruleEffectiveEnd"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled="formData.ruleEffectiveType === 0"
          />
        </el-form-item>
        <el-form-item
          :label="$t('dialog.notes')"
          prop="note"
          class="col-span-2"
        >
          <el-input
            v-model="formData.note"
            type="textarea"
            resize="none"
            :rows="3"
            :maxlength="128"
          />
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil from '@/utils/bfutil'

  import bfNotify from '@/utils/notify'
  import bfTime from '@/utils/time'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import validateRules from '@/utils/validateRules'

  const dbSubject = `db.${bfglob.sysId}`
  const defDate = '2000-01-01 00:00:00'
  const effectiveMask = 'YYYY-MM-DD 00:00:00'
  const DefaultData = {
    orgId: bfutil.getBaseDataOrgId(),
    ruleId: bfutil.getBaseSelfId(),
    ruleName: '',
    ruleLineRid: '',
    checkStartTime: bfTime.nowLocalTime('HH:mm'),
    checkAllTime: 60,
    checkCount: 1,
    ruleEffectiveType: 0,
    ruleEffectiveStart: '',
    ruleEffectiveEnd: '',
    note: '',
    day_1: true,
    day_2: true,
    day_3: true,
    day_4: true,
    day_5: true,
    day_6: false,
    day_7: false,
  }

  export default {
    name: 'BfRules',
    mixins: [vueMixin],
    data() {
      return {
        dataTable: {
          name: 'rulesTable',
          body: bfutil.objToArray(bfglob.gruleMaster.getAll()),
        },
        selOrgList: bfglob.gorgData.getList(),
        selLineList: bfglob.glineMaster.getList(),
      }
    },
    methods: {
      async onDelete(row) {
        await this.delete_rule_data(row, dbCmd.DB_RFID_RULE_MASTER_DELETE)
      },
      async onUpdate(row, done) {
        if (!this.checkedRuleEffectiveTime(row)) {
          return
        }
        const isOk = await this.update_rule_data(row, dbCmd.DB_RFID_RULE_MASTER_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        if (!this.checkedRuleEffectiveTime(row)) {
          return
        }
        const isOk = await this.add_rule_data(row, dbCmd.DB_RFID_RULE_MASTER_INSERT)
        if (!isOk) return
        if (addNewCb) {
          const __data = this.getNewData()
          __data.orgId = row.orgId
          __data.ruleId = bfutil.customNumberIncrement(row.ruleId)
          // 重置标签页数据
          bfutil.resetForm(this, 'lineRulesDataEditorForm')
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return { ...DefaultData }
      },

      checkedRuleEffectiveTime(data) {
        // 时间段有效返回ture,否则返回false
        if (data.ruleEffectiveType === 1) {
          // 有效时间段，检查时间段的有效性
          const mask = 'YYYY-MM-DD'
          if (bfTime.getLocalTimeString(data.ruleEffectiveStart, mask) >=
            bfTime.getLocalTimeString(data.ruleEffectiveEnd, mask)) {
            bfNotify.messageBox(this.$t('msgbox.ruleDateAlert'), 'error')
            return false
          } else {
            return true
          }
        } else {
          return true
        }
      },
      add_rule_data(data, add_cmd) {
        const msgObj = {
          ...data,
          rid: uuid(),
          ruleEffectiveStart: data.ruleEffectiveStart || defDate,
          ruleEffectiveEnd: data.ruleEffectiveEnd || defDate,
        }

        return bfproto.sendMessage(add_cmd, msgObj, 'db_rfid_rule_master', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('add rule res:', rpc_cmd_obj)
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              bfglob.emit('add_global_ruleMasterData', msgObj)

              DefaultData.orgId = msgObj.orgId
              DefaultData.ruleId = bfutil.customNumberIncrement(msgObj.ruleId)

              // 添加查询日志
              const note = this.$t('dialog.add') + msgObj.ruleId + ' / ' + msgObj.ruleName +
                this.$t('msgbox.ruleData')
              bfglob.emit('addnote', note)
            } else {
              bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            }
            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.warn('add rule timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            return Promise.resolve(false)
          })
      },
      update_rule_data(data, up_db_cmd) {
        const msgObj = {
          ...data,
          ruleEffectiveStart: data.ruleEffectiveStart || defDate,
          ruleEffectiveEnd: data.ruleEffectiveEnd || defDate,
        }

        return bfproto.sendMessage(up_db_cmd, msgObj, 'db_rfid_rule_master', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('update rule res:', rpc_cmd_obj)
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
              // 更新全局组织机构数据
              bfglob.emit('update_global_ruleMasterData', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.update') + msgObj.ruleId + ' / ' + msgObj.ruleName +
                this.$t('msgbox.ruleData')
              bfglob.emit('addnote', note)
            } else {
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            }
            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.warn('update rule timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            return Promise.resolve(false)
          })
      },
      delete_rule_data(data, del_cmd) {
        return bfproto.sendMessage(del_cmd, data, 'db_rfid_rule_master', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('update rule res:', rpc_cmd_obj)
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_ruleMasterData', data)

              // 添加查询日志
              const note = this.$t('dialog.delete') + data.ruleId + ' / ' + data.ruleName +
                this.$t('msgbox.ruleData')
              bfglob.emit('addnote', note)
            } else {
              bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            }
            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.warn('delete rule timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            return Promise.resolve(false)
          })
      },
      checkedRuleEffectiveType(formData, val) {
        if (val === 0) {
          formData.ruleEffectiveStart = ''
          formData.ruleEffectiveEnd = ''
        } else if (val === 1) {
          formData.ruleEffectiveStart = bfTime.nowLocalTime(effectiveMask)
          formData.ruleEffectiveEnd = bfTime.getLocalTimeString(
            bfTime.getYearAfterTheSpecifiedTime(new Date(), effectiveMask))
        }
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = bfutil.objToArray(bfglob.gruleMaster.getAll())
      },
    },
    mounted() {
      bfglob.on('add_global_ruleMasterData', this.upsetDataTableBody)
      bfglob.on('update_global_ruleMasterData', this.upsetDataTableBody)
      bfglob.on('delete_global_ruleMasterData', this.upsetDataTableBody)
    },
    components: {
      DataFormEditor,
    },
    watch: {
      fullscreen: {
        handler(newVal) {
          bfglob.emit(this.dataTable.name, newVal)
        },
      },
    },
    computed: {
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            // data: 'orgShortName',
            width: '90px',
            render: (data, type, row, meta) => {
              const org = bfglob.gorgData.get(row.orgId)
              return org?.orgShortName ?? ''
            },
          },
          {
            title: this.$t('dialog.serialNo'),
            data: 'ruleId',
            width: '90px',
          },
          {
            title: this.$t('dialog.ruleName'),
            data: 'ruleName',
            width: this.isFR ? '130px' : '100px',
          },
          {
            title: this.$t('dialog.lineName'),
            data: 'lineName',
            width: this.isFR ? '130px' : '100px',
          },
          {
            title: this.$t('dialog.Mon'),
            data: 'day1',
            width: this.isFR ? '60px' : this.isEN ? '40px' : '20px',
          },
          {
            title: this.$t('dialog.Tues'),
            data: 'day2',
            width: this.isFR ? '60px' : this.isEN ? '40px' : '20px',
          },
          {
            title: this.$t('dialog.Wed'),
            data: 'day3',
            width: this.isFR ? '60px' : this.isEN ? '40px' : '20px',
          },
          {
            title: this.$t('dialog.Thur'),
            data: 'day4',
            width: this.isFR ? '60px' : this.isEN ? '40px' : '20px',
          },
          {
            title: this.$t('dialog.Fri'),
            data: 'day5',
            width: this.isFR ? '60px' : this.isEN ? '40px' : '20px',
          },
          {
            title: this.$t('dialog.Sat'),
            data: 'day6',
            width: this.isFR ? '60px' : this.isEN ? '40px' : '20px',
          },
          {
            title: this.$t('dialog.Sun'),
            data: 'day7',
            width: this.isFR ? '80px' : this.isEN ? '40px' : '20px',
          },
          {
            title: this.$t('dialog.checkStartTime'),
            data: 'checkStartTime',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.haveruntime'),
            data: 'checkAllTime',
            width: this.isEN ? '110px' : '90px',
          },
          {
            title: this.$t('dialog.checkCount'),
            data: 'checkCount',
            width: this.isFR ? '120px' : this.isEN ? '110px' : '90px',
          },
          {
            title: this.$t('dialog.effectType'),
            data: null,
            width: '100px',
            render: (data, type, row, meta) => {
              if (row.ruleEffectiveType === 1) {
                // 有效时间段
                row.ruleEffectiveTypeName = this.$t('dialog.timeSegment')
              } else {
                // 总是有效
                row.ruleEffectiveTypeName = this.$t('dialog.always')
              }

              return row.ruleEffectiveTypeName
            },
          },
          {
            title: this.$t('dialog.effectStartTime'),
            data: 'ruleEffectiveStart',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.effectEndTime'),
            data: 'ruleEffectiveEnd',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '150px',
          },
        ]
      },
      rules() {
        return {
          orgId: [
            validateRules.required(),
          ],
          ruleId: [
            validateRules.required(),
          ],
          ruleName: [
            validateRules.required(),
          ],
          ruleLineRid: [
            validateRules.required(),
          ],
          checkStartTime: [
            validateRules.required(),
          ],
          checkCount: [
            validateRules.required(),
          ],
          checkAllTime: [
            validateRules.required(),
          ],
        }
      },
      effectiveTypes() {
        return [
          {
            value: 0,
            label: this.$t('dialog.always'),
          },
          {
            value: 1,
            label: this.$t('dialog.timeSegment'),
          },
        ]
      },
      labelWidth() {
        return '120px'
      },
    },
  }
</script>

<style lang="scss">
  .el-dialog.data-form-dialog.line-rules-editor {
    &:not(.is-fullscreen).el-form {
      width: 550px;
    }

    .el-form-item.checkDate .el-form-item__content {
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
      align-items: center;
      height: 100%;

      &>.el-form-item {
        margin-bottom: 0;

        .el-checkbox__label {
          padding-left: 6px;
        }
      }
    }
  }
</style>
