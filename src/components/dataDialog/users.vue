<template>
  <div class="user-manage">
    <data-form-editor
      ref="formEditor"
      class="page-users"
      editor-class="user-editor"
      :title="$t('dialog.usersDataTitle')"
      :tableName="dataTable.name"
      :data="dataTable.body"
      :column="dthead"
      :getNewData="getNewData"
      :beforeAction="beforeAction"
      :getFormRef="() => $refs.userDataEditorForm"
      @row-delete="onDelete"
      @row-update="onUpdate"
      @row-new="onNew"
      @close="onCloseEditor"
    >
      <template #form="{ formData }">
        <el-form
          ref="userDataEditorForm"
          :model="formData"
          :label-width="labelWidth"
          :rules="rules"
          class="grid grid-cols-1 data-editor-form"
        >
          <el-form-item
            :label="$t('dialog.picture')"
            class="select-image-form-item"
          >
            <el-tooltip
              effect="dark"
              :content="$t('dialog.clickCheckImg')"
              placement="top"
            >
              <div
                class="image-preview"
                @click="openSelectImage"
              >
                <img
                  :src="image.fileContent"
                  class="h-full w-full"
                >
              </div>
            </el-tooltip>
            <el-tooltip
              effect="dark"
              :content="$t('dialog.clearCheckedImg')"
              placement="top"
            >
              <el-button
                circle
                icon="delete"
                class="clear_user_btn"
                @click="clearUserImage"
              />
            </el-tooltip>
          </el-form-item>
          <el-form-item
            :label="$t('dialog.parentOrg')"
            prop="orgId"
          >
            <el-select
              v-model="formData.orgId"
              :placeholder="$t('dialog.select')"
              filterable
              clearable
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="item in selOrgList"
                :key="item.rid"
                :label="item.label"
                :value="item.rid"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('dialog.serialNo')"
            prop="selfId"
          >
            <el-input
              v-model="formData.selfId"
              :maxlength="16"
            />
          </el-form-item>
          <el-form-item
            :label="$t('dialog.userName')"
            prop="userName"
          >
            <el-input
              v-model="formData.userName"
              :maxlength="32"
            />
          </el-form-item>
          <el-form-item
            :label="$t('dialog.postName')"
            prop="userTitle"
          >
            <el-select
              v-model="formData.userTitle"
              clearable
              filterable
              :placeholder="$t('dialog.select')"
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="item in selJobsList"
                :key="item.rid"
                :label="item.label"
                :value="item.rid"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('dialog.userPhone')"
            prop="userPhone"
          >
            <el-input
              v-model="formData.userPhone"
              :maxlength="32"
            />
          </el-form-item>
          <el-form-item
            :label="$t('dialog.userRfid')"
            prop="userRfid"
          >
            <el-input
              v-model="formData.userRfid"
              :maxlength="16"
            />
          </el-form-item>
          <el-form-item
            :label="$t('dialog.notes')"
            prop="note"
          >
            <el-input
              v-model="formData.note"
              type="textarea"
              resize="none"
              :rows="3"
              :maxlength="128"
            />
          </el-form-item>
          <el-form-item :label="$t('dialog.imbeSn')">
            <el-input
              v-model="imbeSn"
              type="textarea"
              resize="none"
              :rows="3"
              :maxlength="256"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </el-form-item>
        </el-form>
      </template>

      <template #form-footer="{ onClose, onConfirm, isNewStatus }">
        <div class="flex justify-center gap-3 dialog-footer">
          <el-button
            class="w-32"
            @click="onClose"
          >
            {{ $t('dialog.cancel') }}
          </el-button>
          <el-button
            class="w-32"
            type="primary"
            @click="onConfirm(isNewStatus)"
            v-text="$t('dialog.confirm')"
          />
          <template v-if="!isNewStatus">
            <el-button
              class="w-32"
              type="warning"
              @click="open_privelege_dialog"
              v-text="$t('dialog.privelege')"
            />
          </template>
        </div>
      </template>
    </data-form-editor>

    <user-privelege
      v-if="privelegeLoaded"
      v-model:row="editRow"
      v-model:setting="userSettings"
      v-model:visible="privelegeVisible"
    />

    <bf-custom-image
      v-if="customImageLoaded"
      ref="addData_image"
      v-model="image"
      v-model:visible="imageVisible"
    />
  </div>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil from '@/utils/bfutil'
  import bfCrypto from '@/utils/crypto'
  import bfNotify from '@/utils/notify'
  import bfUserSettings from '@/utils/userSettings'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import { prochatUserRid } from '@/utils/prochatDeviceInfoList'
  import { cloneDeep } from 'lodash'
  import BfCustomImage from '@/components/common/customImage.vue'
  import { defineAsyncComponent } from 'vue'

  const dbSubject = `db.${bfglob.sysId}`
  const defaultImage = {
    rid: '*************-2222-2222-************',
    fileName: 'default_user.png',
    fileContent: bfutil.default_user(),
    hash: bfCrypto.sha256(bfutil.default_user()),
  }
  const defaultData = {
    rid: '',
    orgId: bfutil.getBaseDataOrgId(),
    selfId: bfutil.getBaseSelfId(),
    userName: '',
    userTitle: '',
    userPhone: '',
    userRfid: '',
    userImage: '',
    userLoginName: '',
    userLoginPass: '',
    userSetting: '{}',
    note: '',
  }

  export default {
    name: 'BfUsers',
    mixins: [vueMixin],
    data() {
      return {
        dataTable: {
          name: 'usersTable',
          body: bfutil.objToArray(bfglob.guserData.getAll()),
        },
        image: { ...defaultImage },
        imageVisible: false,
        customImageLoaded: false,
        selOrgList: bfglob.gorgData.getList(),
        selJobsList: bfglob.gjobsData.getList(),
        imbeSn: '',

        // 权限对话框中使用的参数
        privelegeLoaded: false,
        privelegeVisible: false,
        editRow: {
          rid: '',
          orgId: '',
        },
        // 编辑的用户的配置
        userSettings: {},
      }
    },
    methods: {
      onCloseEditor() {
        this.privelegeLoaded = false
        this.customImageLoaded = false
      },
      async onDelete(row) {
        if (row.selfId === 'prochat') {
          bfNotify.messageBox(this.$t('msgbox.canNotDeleteProchatUser'), 'error')
          return
        }
        await this.delete_user_data(row, dbCmd.DB_USER_DELETE)
      },
      async onUpdate(row, done) {
        // 判断用户头像是否变更
        if (row.userImage !== this.image.rid) {
          const image = await this.add_user_image(row)
          row.userImage = image.rid
          row.fileContent = image.fileContent
        }

        if (row.rid === prochatUserRid) {
          bfNotify.messageBox(this.$t('msgbox.canNotEditProchatUserSeIfId'), 'error')
          return
        }

        const isOk = await this.update_user_data(row, dbCmd.DB_USER_UPDATE)
        if (!isOk) return
        done()
        this.clearEditCache()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        // 先添加头像，失败则用默认的
        const image = await this.add_user_image(row)
        row.userImage = image.rid
        row.fileContent = image.fileContent
        const isOk = await this.add_user_data(row, dbCmd.DB_USER_INSERT)
        if (!isOk) return
        if (addNewCb) {
          const __data = this.getNewData()
          if (this.$refs.addData_image) {
            this.$refs.addData_image.src = bfutil.default_user()
          }
          __data.orgId = row.orgId
          __data.selfId = bfutil.customNumberIncrement(row.selfId)
          // 重置标签页数据
          bfutil.resetForm(this, 'userDataEditorForm')
          addNewCb(__data)
          return
        }
        done()
        // 清除当前选中的图片
        this.clearUserImage()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(defaultData)
      },
      clearEditCache() {
        this.imbeSn = ''
        this.userSettings = {}
        this.editRow = { rid: '', orgId: '' }
      },
      // 缓存编辑时，需要辅助的一些参数
      saveEditCache(row) {
        this.editRow = row
        // 找到用户的头像数据
        this.image = bfglob.gimages.get(row.userImage) ?? { ...defaultImage }

        try {
          const settings = JSON.parse(row.userSetting ?? '{}')
          this.imbeSn = settings.imbeSn || ''
          this.userSettings = settings
        } catch (e) {
          bfglob.console.warn('[users.vue] JSON.parse userSetting failed:', e)
        }
      },
      /**
       * 编辑数据前置执行方法，允许拒绝编辑或添加新数据
       * @param {number} status Add: 1, Edit: 2
       * @param {Record<string, any>?} row
       * @returns {Promise<boolan>}
       */
      beforeAction(status, row) {
        // 超级管理员允许编辑用户数据
        if (!bfglob.userInfo.isSuper && bfutil.notEditDataPermission()) {
          return Promise.reject('No permission')
        }

        // 编辑时，需要检查是否编辑自己的数据
        if (status !== 1) {
          // 不能编辑内置的默认账号
          if (row.orgId === bfutil.DefOrgRid) {
            bfNotify.warningBox(this.$t('msgbox.canNotEditRootUserData'), 'warning')
            return Promise.reject('Can not builtin user')
          }

          if (row.rid === bfglob.userInfo.rid) {
            bfNotify.warningBox(this.$t('msgbox.notEditSefl'), 'warning')
            return Promise.reject('Can not edit self data')
          }
        }
        // 需要缓存下用户的账号和权限信息等
        if (status === 2 && row) {
          this.saveEditCache(row)
        } else {
          this.imbeSn = ''
        }

        return Promise.resolve(true)
      },

      // 添加用户头像数据
      add_user_image(userData) {
        // 如果是默认头像，则不添加数据
        if (this.image.rid === defaultImage.rid) {
          return Promise.resolve(this.image)
        }

        // 有设置头像，尝试添加，如果失败，则使用默认头像
        const msgObj = {
          ...this.image,
          rid: uuid(),
          orgId: userData.orgId,
        }

        return bfproto.sendMessage(dbCmd.DB_IMAGE_INSERT, msgObj, 'db_image', dbSubject)
          .then(rpc_cmd_obj => {
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfglob.gimages.set(msgObj.rid, msgObj)
              return Promise.resolve(msgObj)
            } else {
              return Promise.resolve({ ...defaultImage })
            }
          })
          .catch(err => {
            bfglob.console.warn('DB_IMAGE_INSERT catch', err)
            return Promise.resolve({ ...defaultImage })
          })
      },
      add_user_data(data, add_cmd) {
        const setting = { ...bfUserSettings, imbeSn: this.imbeSn }
        const msgObj = {
          allowLoginManage: true,
          ...data,
          rid: uuid(),
          userSetting: JSON.stringify(setting),
        }

        return bfproto.sendMessage(add_cmd, msgObj, 'db_user', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('add user res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              defaultData.selfId = bfutil.customNumberIncrement(msgObj.selfId)
              bfglob.emit('add_global_userData', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.add') + msgObj.selfId + ' / ' + msgObj.userName +
                this.$t('msgbox.userData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_user_self_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatNo'))
              } else if (rpc_cmd_obj.resInfo.includes('licence is full')) {
                bfNotify.warningBox(this.$t('msgbox.licenceIsFull'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add user timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            return Promise.resolve(false)
          })
      },
      update_user_data(data, up_db_cmd) {
        const msgObj = {
          allowLoginManage: true,
          ...data,
          // 把权限设置后的登录账号设置上
          userLoginName: this.editRow.userLoginName || data.userLoginName,
          userLoginPass: this.editRow.userLoginPass || data.userLoginPass,
        }

        try {
          // 把权限设置后的权限参数添加进来
          const setting = JSON.parse(data.userSetting ?? '{}')
          const userSetting = { ...bfUserSettings, ...setting, ...this.userSettings }
          if (this.imbeSn) {
            userSetting.imbeSn = this.imbeSn
          } else {
            delete userSetting.imbeSn
          }
          msgObj.userSetting = JSON.stringify(userSetting ?? bfglob.userInfo.setting)
        } catch (e) {
          bfglob.console.warn('[users.vue] update user data, encode imbeSn failed:', e)
        }

        return bfproto.sendMessage(up_db_cmd, msgObj, 'db_user', dbSubject).then(rpc_cmd_obj => {
          bfglob.console.log('update user res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
            // 更新全局组织机构数据
            bfglob.emit('update_global_userData', msgObj)

            // 添加查询日志
            const note = this.$t('dialog.update') + msgObj.selfId + ' / ' + msgObj.userName +
              this.$t('msgbox.userData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_user_self_id_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatNo'))
            } else if (rpc_cmd_obj.resInfo.includes('db_user_login_name_unique')) {
              bfNotify.warningBox(`"${msgObj.userLoginName}" ${this.$t('msgbox.loginNameUnique')}`)
            } else {
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            }
          }
          return Promise.resolve(isOk)
        }).catch(err => {
          bfglob.console.warn('update user timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          return Promise.resolve(false)
        })
      },
      delete_user_data(data, del_cmd) {
        const msgObj = {
          ...data,
        }

        return bfproto.sendMessage(del_cmd, msgObj, 'db_user', dbSubject).then(rpc_cmd_obj => {
          bfglob.console.log('delete user res:', rpc_cmd_obj)
          const isOk = rpc_cmd_obj.resInfo === '+OK'
          if (isOk) {
            bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
            bfglob.emit('delete_global_userData', msgObj)

            // 添加查询日志
            const note = this.$t('dialog.delete') + msgObj.selfId + ' / ' + msgObj.userName +
              this.$t('msgbox.userData')
            bfglob.emit('addnote', note)
          } else {
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          }
          return Promise.resolve(isOk)
        }).catch(err => {
          bfglob.console.warn('delete user timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          return Promise.resolve(false)
        })
      },
      // 自定义图片选择功能
      openSelectImage() {
        this.customImageLoaded = true
        this.imageVisible = true
      },
      clearUserImage() {
        this.image = { ...defaultImage }
      },
      //  更新用户权限功能
      open_privelege_dialog() {
        if (!bfglob.userInfo.isSuper && bfutil.notEditUserPermDataPermission()) {
          return
        }
        this.privelegeLoaded = true // 用于控制第一次加载
        this.privelegeVisible = true
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = bfutil.objToArray(bfglob.guserData.getAll())
      },
    },
    components: {
      DataFormEditor,
      BfCustomImage,
      userPrivelege: defineAsyncComponent(() => import('./userPrivelege.vue')),
    },
    watch: {
      fullscreen: {
        handler(newVal) {
          bfglob.emit(this.dataTable.name, newVal)
        },
      },
    },
    computed: {
      dthead() {
        return [
          {
            title: this.$t('dialog.picture'),
            data: 'fileContent',
            render: function (data, type, row, meta) {
              return `<img src='${row.fileContent}' class='custorm_pic_style'>`
            },
            width: '50px',
          },
          {
            title: this.$t('dialog.parentOrg'),
            // data: 'orgShortName',
            data: null,
            width: '100px',
            render: function (data, type, row, meta) {
              if (row.orgId === bfutil.DefOrgRid) return 'root'

              const org = bfglob.gorgData.get(row.orgId)
              return org?.orgShortName ?? ''
            },
          },
          {
            title: this.$t('dialog.serialNo'),
            data: 'selfId',
            width: '100px',
          },
          {
            title: this.$t('dialog.userName'),
            data: 'userName',
            width: this.isFR ? '130px' : '100px',
          },
          {
            title: this.$t('dialog.postName'),
            data: 'userTitleName',
            width: this.isFR ? '130px' : '100px',
          },
          {
            title: this.$t('dialog.userPhone'),
            data: 'userPhone',
            width: '100px',
          },
          {
            title: this.$t('dialog.userRfid'),
            data: 'userRfid',
            width: '100px',
          },
          {
            title: this.$t('dialog.userLoginName'),
            data: 'userLoginName',
            width: '100px',
          },
          {
            title: this.$t('dialog.imbeSn'),
            data: null,
            className: 'imbe-column',
            render: (_, type, row) => {
              try {
                const settings = JSON.parse(row.userSetting ?? '{}')
                const imbeSn = settings.imbeSn || ''
                return `<div class="imbe-column-content" title="${imbeSn}">${imbeSn}</div>`
              } catch (e) {
                bfglob.console.warn('[users.vue] datatable imbeSn column render failed:', e)
                return '<div class="imbe-column-content"></div>'
              }
            },
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '100px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('dialog.usersDataTitle')
      },
      rules() {
        return {
          orgId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'change',
            },
          ],
          selfId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              max: 16,
              message: this.$t('msgbox.maxLen') + '16' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          userName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              max: 32,
              message: this.$t('msgbox.maxLen') + '32' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          userPhone: [
            {
              max: 32,
              message: this.$t('msgbox.maxLen') + '32' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          userRfid: [
            {
              max: 16,
              message: this.$t('msgbox.maxLen') + '16' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          note: [
            {
              max: 128,
              message: this.$t('msgbox.maxLen') + '128' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
        }
      },
      labelWidth() {
        return this.isFR ? '130px' : this.isEN ? '120px' : '80px'
      },
    },
    mounted() {
      bfglob.on('add_global_userData', this.upsetDataTableBody)
      bfglob.on('update_global_userData', this.upsetDataTableBody)
      bfglob.on('delete_global_userData', this.upsetDataTableBody)
    },
  }
</script>

<style lang="scss">
  .el-form.data-editor-form {
    .el-form-item.select-image-form-item .el-form-item__content {
      display: flex;
      gap: 10px;
      align-items: center;

      .image-preview {
        width: 32px;
        height: 32px;
        border: 1px solid #66b1ff;
        border-radius: 4px;
        padding: 1px;
        cursor: pointer;
      }

      @media (min-width: 2560px) {
        .image-preview {
          width: 40px;
          height: 40px;
        }
      }
    }
  }

  .data-form-editor {
    .custorm_pic_style {
      display: inline-block;
      width: 28px;
      height: 28px;
      border-radius: 4px;
    }

    .imbe-column {
      max-width: 260px !important;

      .imbe-column-content {
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
</style>
