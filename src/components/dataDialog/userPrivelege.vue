<template>
  <el-dialog
    class="user-privelege"
    ref="dialog"
    v-model="dlgVisible"
    :title="$t('dialog.usersPrivelegeSet')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    @open="openDlgFn"
  >
    <TableTree
      :ref="treeId"
      :treeId="treeId"
      toolbar
      :option="treeOpts"
      :filterOption="filterOption"
      @loaded="treeLoaded"
    />
    <div class="divider">
      <!-- 分割线 -->
    </div>
    <el-form
      class="user-permission-form"
      label-position="top"
    >
      <!-- 各种权限配置 -->
      <el-form-item
        v-for="perm in permissions"
        :key="perm.label"
        class="checkbox-form-item"
      >
        <el-checkbox
          v-model="permissionSettings[perm.prop]"
          :label="perm.label"
          :disabled="perm.disabled"
        />
      </el-form-item>

      <!-- 登录账号和密码 -->
      <el-form-item
        :label="$t('dialog.userLoginName')"
        prop="userLoginName"
        class="mt-3"
      >
        <el-input
          v-model="userLoginName"
          clearable
          :placeholder="$t('dialog.userLoginName')"
          :maxlength="16"
        />
      </el-form-item>
      <el-form-item
        :label="$t('dialog.userLoginPass')"
        prop="userLoginPass"
      >
        <el-input
          v-model="userLoginPass"
          show-password
          clearable
          class="user-privelege-pw"
          :placeholder="$t('dialog.userLoginPass')"
          :maxlength="16"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-center gap-3 privelege-foot">
        <el-button
          class="w-32"
          @click="cancel_set_privelege"
          v-text="$t('dialog.cancel')"
        />
        <el-button
          type="primary"
          class="w-32"
          @click="confirm_set_privelege"
          v-text="$t('dialog.confirm')"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script>
  import TableTree from '@/components/common/tableTree'
  import { debounce } from 'lodash'
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import {
    addOneOrgNode,
    addOrgNodeToTree,
    createTreeOrgSource,
    delOneOrgNode,
    getOrgNodeTitle,
    updateOneOrgNode,
  } from '@/utils/bftree'
  import bfutil from '@/utils/bfutil'

  import { v1 as uuid } from 'uuid'
  import bfNotify from '@/utils/notify'
  import i18n from '@/modules/i18n'
  import bfprocess from '@/utils/bfprocess'
  import bfCrypto from '@/utils/crypto'

  const dbSubject = `db.${bfglob.sysId}`
  const Permission = {
    editData: false,
    sendCommand: false,
    editUserPerm: false,
    fullCallPerm: false,
    repeaterWf: false,
    // intercomWf: false,
    dynamicGroupPermission: false,
    allowLoginManage: true, // 默认允许登录管理
  }
  const PermissionKeys = Object.keys(Permission)
  const PermissionDataCache = new Map()
  const getPermissionSettings = (settings, keys = PermissionKeys) => {
    return keys.map(key => {
      return { [key]: settings[key] ?? false }
    }).reduce((p, c) => Object.assign(p, c), {})
  }

  export default {
    name: 'UserPrivelege',
    emits: ['update:row', 'update:setting', 'update:visible'],
    props: {
      row: {
        type: Object,
        required: true,
      },
      setting: {
        type: Object,
        required: true,
      },
      visible: {
        type: Boolean,
        required: true,
      },
    },
    data() {
      return {
        userLoginName: this.row.userLoginName,
        userLoginPass: '',
        permissionSettings: { ...Permission, ...getPermissionSettings(this.setting) },
        loginUserSettings: bfglob.userInfo.setting,
        treeId: 'privelege_list',
        nodeOption: {
          showCounter: false,
          selected: false,
          userPerm: true,
        },
        treeReady: false,
      }
    },
    methods: {
      /**
       * 更新用户指定字段参数
       * @param {Record<string, any>} field 需要更新的字段参数
       * @param {string} resInfo 指定更新字段名称，以","分隔
       * @returns {Promise<void>}
       */
      updateUserSpecifyData(fields, resInfo) {
        const msgObj = {
          ...fields,
          rid: this.userRid,
          orgId: this.row.orgId,
        }
        const msgOpts = {
          rpcCmdFields: {
            origReqId: 'rid',
            resInfo: 'org_id,rid,' + resInfo,
          },
        }

        return bfproto.sendMessage(dbCmd.DB_USER_PUPDATE, msgObj, 'db_user', dbSubject, msgOpts)
          .then(rpc_cmd_obj => {
            bfglob.console.log('updateUserSpecifyData res', rpc_cmd_obj)
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
            } else {
              bfNotify.messageBox(`${this.$t('msgbox.upError')} ${rpc_cmd_obj.resInfo}`, 'error')
            }

            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.warn('update user settings error:', err)
            bfNotify.messageBox(i18n.global.t('msgbox.upError'), 'error')
            return Promise.reject(err)
          })
      },
      async updateUserSpecifyFields() {
        const userSetting = {
          ...this.setting,
          ...this.permissionSettings,
        }

        const userLoginName = this.userLoginName
        let userLoginPass = this.userLoginPass
        if (userLoginName) {
          // 如果用户名没有变，且密码为空，则使用原密码，否则为新密码
          if (userLoginName === this.row.userLoginName && !userLoginPass) {
            userLoginPass = this.row.userLoginPass
          } else {
            // 如果密码没有输入，则默认为登录账号
            if (!userLoginPass) {
              userLoginPass = userLoginName
            }
            userLoginPass = bfCrypto.sha256(userLoginName + userLoginPass)
          }
        } else {
          // 用户名为空，则密码为空
          userLoginPass = ''
        }
        const fields = {
          userLoginName,
          userLoginPass,
          allowLoginManage: this.permissionSettings.allowLoginManage ?? true,
          userSetting: JSON.stringify(userSetting),
        }
        const isOk = await this.updateUserSpecifyData(fields, 'user_setting,user_login_name,user_login_pass,allow_login_manage')
        if (!isOk) return

        // 同步到父组件的数据
        const newRow = { ...this.row, ...fields }
        this.$emit('update:row', newRow)
        this.$emit('update:setting', userSetting)
        bfglob.emit('update_global_userData', newRow)
        // 关闭窗口
        this.dlgVisible = false
        // 重置登录账号数据
        // this.userLoginName = ''
        this.userLoginPass = ''
      },
      delete_sessionId(rid, del_db_cmd) {
        const msgObj = {
          rid: rid,
        }

        bfproto.sendMessage(del_db_cmd, msgObj, 'db_user_session_id', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete user sessionId res:', rpc_cmd_obj)
          }).catch(err => {
          bfglob.console.warn('delete user sessionId timeout:', err)
        })
      },
      get_sessionId(user_rid, db_cmd) {
        const msgObj = {
          userRid: user_rid,
        }
        const msgOpts = {
          rpcCmdFields: {
            origReqId: 'user_rid',
            resInfo: 'rid',
          },
          decodeMsgType: 'db_user_session_id_list',
        }

        return bfproto.sendMessage(db_cmd, msgObj, 'db_user_session_id', dbSubject, msgOpts)
          .then(rpc_cmd_obj => {
            bfglob.console.log('got user sessionId res:', rpc_cmd_obj)
            // const isOk = rpc_cmd_obj.resInfo === '+OK'
            return Promise.resolve(rpc_cmd_obj.body.rows)

            // if (rows.length > 0) {
            //   if (type === 'delete') {
            //     this.delete_sessionId(rows[0].rid, dbCmd.DB_USER_SESSION_ID_DELETE)
            //   }
            // }
          })
          .catch(err => {
            bfglob.console.warn('got user sessionId timeout:', err)
            return Promise.resolve([])
          })
      },
      delete_user_privelege(data) {
        const options = {
          orgId: this.row.orgId,
        }

        return bfproto.sendMessage(dbCmd.DB_USER_PRIVELEGE_DELETE, data, 'db_user_privelege', dbSubject, options)
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete user permissions res:', rpc_cmd_obj)
          }).catch(err => {
            bfglob.console.warn('delete user permissions timeout:', err)
          })
      },
      deletePrivelege(priveleges) {
        priveleges.forEach(item => this.delete_user_privelege(item))
      },
      addPrivelege(priveleges) {
        priveleges.forEach(item => bfprocess.add_user_privelege(item, dbCmd.DB_USER_PRIVELEGE_INSERT, this.row.orgId))
      },
      /**
       * 更新单位的权限数据，db_user_privelege
       * @param {Array<Record<string, any>>} priveleges 当前选中的单位权限
       * @returns {Promise<void>}
       */
      async updatePrivelege(priveleges) {
        try {
          const originPrivelege = bfglob.guserData.getPrivelege(this.userRid) ?? []
          // 在当前列表，不在store中，则添加
          const needAddList = priveleges.filter(item => !originPrivelege.find(v => v.rid === item.rid))
          this.addPrivelege(needAddList)

          // 在store中，不在当前列表，则删除
          const needDeleteList = originPrivelege.filter(item => !priveleges.find(v => v.rid === item.rid))
          this.deletePrivelege(needDeleteList)

          // 同步本地的权限
          bfglob.guserData.setPrivelege(this.userRid, needAddList)

          // 更新权限数据后，清除sessionId
          if (needAddList.length || needDeleteList.length) {
            const sessions = await this.get_sessionId(this.userRid, dbCmd.DB_USER_SESSION_ID_GETBY)
            if (sessions.length) {
              this.delete_sessionId(sessions[0].rid, dbCmd.DB_USER_SESSION_ID_DELETE)
            }
          }
        } catch (e) {
          bfglob.console.log('updatePrivelege catch:', e)
        }
      },
      // 防抖处理
      privelegeChange() {
        if (!this.dlgVisible) {
          return
        }
        this.getPrivelege()
      },
      addGlobalPrivelege(data) {
        if (this.userRid !== data.userRid) {
          return
        }
        this.privelegeChange()
      },
      updateGlobalPrivelege(data) {
        if (this.userRid !== data.userRid) {
          return
        }
        this.privelegeChange()
      },
      deleteGlobalPrivelege(data) {
        if (this.userRid !== data.userRid) {
          return
        }
        this.privelegeChange()
      },
      getIncludeChildrenIcon(includeChildren) {
        return includeChildren === 1 ? 'icon-select-all' : 'icon-select-all-off'
      },
      updateRenderTitle(node) {
        const nodeSource = bfglob.gorgData.get(node.key)
        const includeChildren = node.data?.includeChildren ?? 1
        node.title = getOrgNodeTitle(nodeSource, {
          userPerm: true,
          showCounter: false,
          includeChildren,
          includeChildrenIcon: this.getIncludeChildrenIcon(includeChildren),
          includeChildrenIconDisabled: node.unselectable,
        })
        node.renderTitle()
      },
      // 同步权限树节点包含子级时，所有子级节点状态
      syncChildrenWhenParentIncludeChildren(node, isIncludeChildren = true, unselectable = true) {
        const children = node.getChildren() ?? []
        children.forEach(child => {
          child.selected = unselectable
          child.unselectable = unselectable
          child.unselectableStatus = unselectable
          child.data.includeChildren = isIncludeChildren ? 1 : 0
          this.updateRenderTitle(child)

          // 如果先选择子节点，再选择包含子级的父节点，子节点的数据需要清除
          PermissionDataCache.delete(child.key)

          // 递归处理所有子节点
          this.syncChildrenWhenParentIncludeChildren(child, isIncludeChildren, unselectable)
        })
      },
      selectNodes(event, data) {
        const node = data.node
        if (!node) {
          return false
        }

        const extraData = node.data ?? {}
        const includeChildren = extraData.includeChildren ?? 1
        // 选中节点
        if (node.isSelected()) {
          this.syncPermissionDataCacheItem(node)
          // 包含子级
          if (includeChildren === 1) {
            this.syncChildrenWhenParentIncludeChildren(node, true, true)
          }
        } else {
          // 取消选中节点
          PermissionDataCache.delete(node.key)
          // 包含子级
          if (includeChildren === 1) {
            this.syncChildrenWhenParentIncludeChildren(node, true, false)
          }
        }

        this.updateRenderTitle(node)

        if (data.tree.getSelectedNodes().length === 0) {
          PermissionDataCache.clear()
        }
      },
      // 点击包含子级标志，同步节点状态事件
      processIncludeChildrenPermissionEvent(node) {
        if (!node || node.unselectable) {
          return false
        }

        const extraData = node.data ?? {}
        const { includeChildren = 1 } = extraData
        const isIncludeChildren = includeChildren === 1

        if (node.isSelected()) {
          this.syncPermissionDataCacheItem(node)
          // 如果选中，则同步子级节点状态
          this.syncChildrenWhenParentIncludeChildren(node, true, isIncludeChildren)
        } else {
          PermissionDataCache.delete(node.key)
        }

        this.updateRenderTitle(node)
      },
      syncPermissionDataCacheItem(node) {
        const includeChildren = node.data.includeChildren ?? 1
        let permData
        if (PermissionDataCache.has(node.key)) {
          permData = PermissionDataCache.get(node.key)
          permData.includeChildren = includeChildren
        } else {
          permData = {
            rid: uuid(),
            userRid: this.userRid,
            userOrg: node.key,
            includeChildren: includeChildren,
          }
        }
        PermissionDataCache.set(node.key, permData)
      },
      clickNode(event, data) {
        const excludeList = ['expander', 'prefix', 'checkbox']
        if (excludeList.includes(data.targetType)) {
          return true
        }
        const node = data.node
        if (!node) {
          return false
        }
        node.setActive()

        // 是否包含下级标识
        let includeChildren = 0

        try {
          // 读取包含子级参数，同步到节点扩展数据
          const userPermFlagEl = node.tr.querySelector('.user-perm-flag')
          includeChildren = parseInt(userPermFlagEl.dataset.includeChildren ?? '1')
          node.data.includeChildren = includeChildren
        } catch (e) {
          node.data.includeChildren = 0
        }

        // 点击的是包含子级标记icon，处理包含下级单位权限自定义icon点击事件
        const originTarget = event.originalEvent.target
        if (originTarget.className.includes('icon-select-all')) {
          includeChildren = includeChildren === 1 ? 0 : 1
          node.data.includeChildren = includeChildren
          this.processIncludeChildrenPermissionEvent(node)
          return true
        }

        node.setSelected(!node.isSelected())
      },
      dblclickNode(event, data) {
        if (!data.node) {
          return
        }
        data.node.setSelected(true)
      },
      treeLoaded() {
        const orgList = bfutil.objToArray(bfglob.gorgData.getAll())
        addOrgNodeToTree(this.treeId, createTreeOrgSource(orgList, this.nodeOption))
        this.treeReady = true
        this.init()
      },
      openDlgFn() {
        if (!this.treeReady) return
        this.init()
      },
      async confirm_set_privelege() {
        // 更新单位权限
        this.updatePrivelege([...PermissionDataCache.values()])
        // 更新用户指定的字段
        this.updateUserSpecifyFields()
      },
      cancel_set_privelege() {
        this.dlgVisible = false
      },
      resetIncludeChildrenStatus() {
        const rootNode = this.tableTree.getRootNode()
        const children = rootNode?.getChildren() ?? []
        const processNodeTitle = (children) => {
          for (let i = 0; i < children.length; i++) {
            const node = children[i]
            const nodeSource = bfglob.gorgData.get(node.key)
            const includeChildren = 1
            node.title = getOrgNodeTitle(nodeSource, {
              userPerm: true,
              showCounter: false,
              includeChildren,
              includeChildrenIcon: this.getIncludeChildrenIcon(includeChildren),
              includeChildrenIconDisabled: false,
            })
            node.unselectable = false
            node.unselectableStatus = false
            node.selected = false

            processNodeTitle(node.getChildren() ?? [])
          }
        }
        processNodeTitle(children)
        rootNode?.render()
      },
      getPrivelege() {
        // 清除权限列表树选中状态
        this.resetIncludeChildrenStatus()
        // 判断本地数据是否有该用户的权限数据
        const privelege = bfglob.guserData.getPrivelege(this.userRid)
        PermissionDataCache.clear()
        const selectedNodeFunc = (privelegeArr) => {
          for (let i = 0; i < privelegeArr.length; i++) {
            const item = privelegeArr[i]
            PermissionDataCache.set(item.userOrg, { ...item })
            const node = this.tableTree?.getNodeByKey(item.userOrg)
            if (!node) {
              continue
            }
            node.data.includeChildren = item.includeChildren
            node.setSelected(true)
            // 默认为包含子级，如果不包含子级，则需要更新title
            if (item.includeChildren === 0) {
              this.updateRenderTitle(node)
            }
          }
          setTimeout(() => {
            this.tableTree?.updateViewport()
          }, 200)
        }
        if (privelege) {
          selectedNodeFunc(privelege)
        } else {
          this.get_user_privelege(this.userRid, dbCmd.DB_USER_PRIVELEGE_GETBY)
            .then((data) => {
              selectedNodeFunc(data)
            })
            .catch((err) => {
              bfglob.console.error('get_user_privelege', err)
            })
        }
      },
      get_user_privelege(user_rid, db_cmd) {
        return new Promise((resolve, reject) => {
          const msgObj = {
            userRid: user_rid,
          }
          const msgOpts = {
            rpcCmdFields: {
              origReqId: 'user_rid',
              resInfo: '*',
            },
            decodeMsgType: 'db_user_privelege_list',
          }

          bfproto.sendMessage(db_cmd, msgObj, 'db_user_privelege', dbSubject, msgOpts).then(rpc_cmd_obj => {
            bfglob.console.log('get_user_privelege res:', rpc_cmd_obj)
            const db_user_privelege_list_obj = rpc_cmd_obj.body
            bfglob.guserData.setPrivelege(user_rid, db_user_privelege_list_obj.rows)
            resolve(db_user_privelege_list_obj.rows)
          }).catch(err => {
            bfglob.console.warn('获取用户权限数据超时', err)
          })
        })
      },
      init() {
        this.getPrivelege()
      },
      addOneOrgNode(orgData) {
        addOneOrgNode(this.treeId, orgData, {
          ...this.nodeOption,
          selected: false,
        })
      },
      delOneOrgNode(key) {
        delOneOrgNode(this.treeId, key)
        // 从权限列表中删除该单位
        PermissionDataCache.delete(key)
      },
      updateOneOrgNode(orgData) {
        updateOneOrgNode(this.treeId, orgData, this.nodeOption)
      },
    },
    computed: {
      userRid() {
        return this.row.rid
      },
      dlgVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      // 各种操作权限
      permissions() {
        return [
          // 编辑数据
          {
            label: this.$t('dialog.editData'),
            prop: 'editData',
            disabled: !this.isSuper && !this.loginUserSettings.editData,
          },
          // 编辑用户权限
          {
            label: this.$t('dialog.editUserPerm'),
            prop: 'editUserPerm',
            disabled: !this.isSuper && !this.loginUserSettings.editUserPerm,
          },
          /*          // 对讲机写频权限
                    {
                      label: this.$t('dialog.interPhoneWfPerm'),
                      prop: 'intercomWf',
                      disabled: !this.isSuper && !this.loginUserSettings.intercomWf,
                    },*/
          // 中继写频权限
          {
            label: this.$t('dialog.repeaterWfPerm'),
            prop: 'repeaterWf',
            disabled: !this.isSuper && !this.loginUserSettings.repeaterWf,
          },
          // 发送命令
          {
            label: this.$t('dialog.sendCommand'),
            prop: 'sendCommand',
            disabled: !this.isSuper && !this.loginUserSettings.sendCommand,
          },
          // 全呼权限
          {
            label: this.$t('dialog.fullCallPerm'),
            prop: 'fullCallPerm',
            disabled: !this.isSuper && !this.loginUserSettings.fullCallPerm,
          },
          // 动态组权限
          {
            label: this.$t('dynamicGroup.dynamicGroupPermission'),
            prop: 'dynamicGroupPermission',
            disabled: !this.isSuper && !this.loginUserSettings.dynamicGroupPermission,
          },
          // 允许登录调度管理网页
          {
            label: this.$t('dialog.allowLoginManage'),
            prop: 'allowLoginManage',
            disabled: !this.isSuper && !bfglob.userInfo.origData.allowLoginManage,
          },
        ]
      },
      isSuper() {
        return bfglob.userInfo.isSuper
      },
      treeOpts() {
        return {
          selectMode: 2,
          select: this.selectNodes,
          click: this.clickNode,
          dblclick: this.dblclickNode,
        }
      },
      filterOption() {
        return {
          leavesOnly: false,
        }
      },
      tableTree() {
        return this.$refs[this.treeId]
      },
    },
    watch: {
      'row.userLoginName'(val) {
        this.userLoginName = val
      },
      setting: {
        immediate: true,
        handler(val) {
          this.permissionSettings = { ...Permission, ...getPermissionSettings(val) }
          this.permissionSettings.allowLoginManage = this.row.allowLoginManage
        },
      },
    },
    components: {
      TableTree,
    },
    mounted() {
      bfglob.on('addOneOrgNode', this.addOneOrgNode)
      bfglob.on('delOneOrgNode', this.delOneOrgNode)
      bfglob.on('updateOneOrgNode', this.updateOneOrgNode)

      // 同步权限数据
      this.privelegeChange = debounce(this.privelegeChange, 350)
      bfglob.on('add_global_user_privelege', this.addGlobalPrivelege)
      bfglob.on('update_global_user_privelege', this.updateGlobalPrivelege)
      bfglob.on('delete_global_user_privelege', this.deleteGlobalPrivelege)
    },
    beforeUnmount() {
      bfglob.off('addOneOrgNode', this.addOneOrgNode)
      bfglob.off('delOneOrgNode', this.delOneOrgNode)
      bfglob.off('updateOneOrgNode', this.updateOneOrgNode)

      bfglob.off('add_global_user_privelege', this.addGlobalPrivelege)
      bfglob.off('update_global_user_privelege', this.updateGlobalPrivelege)
      bfglob.off('delete_global_user_privelege', this.deleteGlobalPrivelege)
    },
  }
</script>

<style lang="scss">
  .el-dialog.user-privelege {
    width: 560px;

    .el-dialog__body {
      height: 410px;
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid var(--el-border-color);
      padding: 0 10px;

      .fancytree-grid-wrapper {
        flex: auto;
        max-width: 50%;
        padding-top: 10px;
      }

      .divider {
        @extend .divider-vertical;
      }

      .el-form.user-permission-form {
        flex: auto;
        padding-top: 10px;

        .el-form-item {
          .el-form-item__label + .el-form-item__content {
            margin-top: 4px;
          }

          .user-privelege-pw .el-input__suffix-inner {
            display: flex;
          }

          &.checkbox-form-item {
            margin-bottom: 0;
          }
        }
      }
    }

    &.is-fullscreen {
      .el-dialog__body {
        flex-direction: column;

        .fancytree-grid-container {
          max-width: 100%;
        }

        .divider {
          @extend .divider-horizontal;
        }
      }
    }

    .user-perm-flag {
      margin-left: 6px;

      &[data-include-children="1"]:not(.disabled) {
        color: #409EFF;
      }
    }

    .divider-horizontal {
      border-top: 1px solid #dcdfe6;
      margin: 10px 0;
    }

    .divider-vertical {
      border-left: 1px solid #dcdfe6;
      margin: 0 10px;
    }
  }
</style>
