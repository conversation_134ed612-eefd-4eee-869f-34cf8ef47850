<template>
  <section class="flex w-full p-3 settings-box receiveGroupList">
    <el-card
      class="flex-auto group-list recevie-group-list"
      shadow="never"
    >
      <template #header>
        <span v-text="$t('dialog.receiveGroupList')" />
      </template>
      <span
        v-if="emptyList"
        v-text="$t('msgbox.emptyText')"
      />
      <DynamicScroller
        v-else
        class="scroller"
        key-field="groupId"
        :items="rxGroupList"
        :minItemSize="24"
      >
        <template #default="{ item, index, active }">
          <DynamicScrollerItem
            :item="item"
            :active="active"
            :data-index="index"
          >
            <div
              class="group-item"
              :class="{ 'selected': index === selectedIndex }"
              @click="selectedReceiveGroup(index)"
              v-text="item.groupName"
            />
          </DynamicScrollerItem>
        </template>
      </DynamicScroller>
    </el-card>
    <el-card
      class="flex-auto group-list listen-group-list"
      shadow="never"
    >
      <template #header>
        <span v-text="$t('dialog.listenGroup')" />
      </template>
      <span
        v-if="emptyGroup"
        v-text="$t('msgbox.emptyText')"
      />
      <div
        v-for="(item, index) in listenGroupLabels"
        v-else
        :key="index"
        class="group-item"
        v-text="item"
      />
    </el-card>
  </section>
</template>

<script>
  import { warningBox } from '@/utils/notify'
  import { cloneDeep, merge } from 'lodash'
  import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

  const noop = () => {
  }
  const OneReceiveGroup = {
    groupId: 0,
    listenGroup: [],
    count: 0,
    groupName: '',
  }

  export default {
    name: 'ReceiveGroup',
    emits: ['update:modelValue'],
    props: {
      modelValue: {
        type: Array,
        required: true,
      },
      getAddressName: {
        type: Function,
        default() {
          return noop
        },
      },
      getOriginAddress: {
        type: Function,
        default() {
          return noop
        },
      },
      getDefaultAddress: {
        type: Function,
        default() {
          return noop
        },
      },
      maxSize: {
        type: Number,
        default: 32,
      },
      channels: {
        type: Array,
        default() {
          return []
        },
      },
      addressTreeId: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        rxGroupList: [],
        selectedIndex: 0,
        groupListChIdMap: {},
        deRxGroup: {},
      }
    },
    methods: {
      selectedReceiveGroup(index = 0) {
        this.selectedIndex = index
      },
      asyncRxGroup(data) {
        if (!Array.isArray(data)) {
          return
        }

        const needDelList = []
        const list = data.map(v => {
          // 从读取回来的通讯录中查找接收组的dmrId
          const item = cloneDeep(v)
          const memberList = item.listenGroup.slice(0, item.count)
          const listenGroup = []
          for (let k = 0; k < memberList.length; k++) {
            const id = memberList[k]
            const oneBook = this.getOriginAddress(id)
            if (!oneBook) {
              continue
            }
            listenGroup.push(oneBook.dmrId)
          }
          item.listenGroup = listenGroup
          const cache = this.rxGroupListIndex[item.groupId]
          if (cache) {
            needDelList.push(cache)
          }
          return merge({}, cache, item)
        })
        while (needDelList.length) {
          this.rxGroupList.splice(this.rxGroupList.indexOf(needDelList.pop()), 1)
        }

        this.$nextTick(() => {
          this.rxGroupList = this.rxGroupList.concat(list)
          // 显示第一个接收组的数据
          if (this.selectedIndex === -1) {
            this.selectedIndex = 0
          }
        })
      },
      resetRxGroupList() {
        this.rxGroupList = []
        this.selectedIndex = -1
        this.groupListChIdMap = {}
        this.deRxGroup = {}
      },
      async showExceedLimitWarningMsg() {
        const currLen = Object.keys(this.deRxGroup).length
        const n = currLen - this.maxSize
        if (this.isExceedLimit && n > 0) {
          warningBox(this.$t('writeFreq.rxGroupExceedLimit', { max: this.maxSize, n }))
        }
      },
      initRxGroupList(channels) {
        return new Promise(resolve => {
          this.resetRxGroupList()

          // 缓存已经在通讯录选择节点的dmrId
          let cacheDmrId = []
          let groupId = 0
          const rxGroupList = []
          const groupListChIdMap = {}
          const deRxGroup = {}

          for (let i = 0; i < channels.length; i++) {
            const item = channels[i]
            if (!item.listenGroup || !item.listenGroup.length) {
              continue
            }
            // 缓存必选的通讯录节点DMRID
            cacheDmrId = cacheDmrId.concat(
              [...item.listenGroup, item.sendGroup].filter(dmrId => {
                return !cacheDmrId.includes(dmrId)
              }),
            )

            const receiveGroup = cloneDeep(OneReceiveGroup)
            const groupLabel = this.$t('dialog.rxGroup')
            const chanelLabel = this.$t('dialog.channel')
            receiveGroup.groupId = i
            receiveGroup.groupName = `${chanelLabel} ${item.no} ${groupLabel}`
            receiveGroup.listenGroup = item.listenGroup.sort()
            receiveGroup.count = receiveGroup.listenGroup.length
            receiveGroup.chId = item.no - 1
            rxGroupList.push(receiveGroup)

            groupListChIdMap[receiveGroup.chId] = receiveGroup.groupId
            if (this.isExceedLimit) {
              const cacheKey = receiveGroup.listenGroup.join('_')
              if (!(cacheKey in this.deRxGroup)) {
                deRxGroup[cacheKey] = cloneDeep(receiveGroup)
                deRxGroup[cacheKey].groupId = groupId++
              }
              const gid = deRxGroup[cacheKey].groupId
              groupListChIdMap[receiveGroup.chId] = gid >= this.maxSize ? 0xFF : gid
            }
          }

          this.rxGroupList = rxGroupList
          this.groupListChIdMap = groupListChIdMap
          this.deRxGroup = deRxGroup

          if (this.selectedIndex === -1) {
            this.selectedIndex = 0
          }

          // 向用户提示接收组超出上限
          this.showExceedLimitWarningMsg()

          // 将所有接收组和发射组在通讯录上设置选中且禁用状态
          bfglob.emit(`${this.addressTreeId}:unselectableNode`, cacheDmrId, () => {
            setTimeout(() => {
              resolve(this.rxGroupList)
            }, 0)
          })
        })
      },
      getGroupIdByChId(chId) {
        if (typeof chId !== 'number') {
          return 0xFF
        }

        if (chId in this.groupListChIdMap) {
          return this.groupListChIdMap[chId]
        }

        for (let i = 0; i < this.rxGroupList.length; i++) {
          const item = this.rxGroupList[i]
          if (item.chId === chId) {
            return item.groupId
          }
        }

        return 0xFF
      },
      getWriteRxGroupList() {
        let list = this.rxGroupList
        if (this.isExceedLimit) {
          list = Object.keys(this.deRxGroup)
            .map(key => this.deRxGroup[key])
            .slice(0, this.maxSize)
        }

        return list.map((item) => {
          const groupItem = cloneDeep(item)
          groupItem.listenGroup = groupItem.listenGroup.map((dmrId) => {
            return this.getDefaultAddress(dmrId)
          }).filter((v) => v !== 0xFFFF).sort()
          groupItem.count = groupItem.listenGroup.length

          return groupItem
        })
      },
    },
    watch: {
      modelValue: {
        deep: true,
        immediate: true,
        handler(val) {
          this.rxGroupList = val
        },
      },
      rxGroupList: {
        deep: true,
        handler(val) {
          this.$emit('update:modelValue', val)
        },
      },
    },
    computed: {
      emptyList() {
        return this.rxGroupList.length === 0
      },
      emptyGroup() {
        return this.listenGroupLabels.length === 0
      },
      rxGroupListIndex() {
        if (!Array.isArray(this.rxGroupList)) {
          return {}
        }
        return this.rxGroupList.map((data) => {
          const prop = data.groupId
          return { [prop]: data }
        }).reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
      },
      listenGroupLabels() {
        const data = this.rxGroupList[this.selectedIndex]
        if (!data) {
          return []
        }
        return (data.listenGroup || []).map((dmrId) => {
          const orgShortName = this.getAddressName(dmrId)
          return `${orgShortName}${orgShortName ? '-' : ''}${dmrId}`
        })
      },
      isExceedLimit() {
        return this.channels.length > this.maxSize
      },
    },
    components: {
      DynamicScroller,
      DynamicScrollerItem,
    },
  }
</script>

<style lang="scss">
  .settings-box.receiveGroupList {
    .group-list {
      display: flex;
      flex-direction: column;
      flex-basis: 50%;

      .el-card__header {
        padding: calc(var(--spacing) * 2);
        text-align: center;
        flex: none;
      }

      .el-card__body {
        flex: auto;
        overflow: auto;
      }

      .group-item {
        height: 28px;
        line-height: 28px;
        cursor: pointer;
        text-align: center;

        &.selected,
        &:hover,
        &:focus {
          color: #20a0ff;
        }
      }

      &.recevie-group-list {
        border-radius: var(--radius-sm) 0 0 var(--radius-sm);
      }

      &.listen-group-list {
        border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
      }
    }
  }
</style>
