<template>
  <el-form
    ref="siteForm"
    class="virtual-cluster-site-form"
    :model="siteInfo"
    label-position="top"
  >
    <el-form-item :label="$t('dialog.name')">
      <el-input
        v-model="siteInfo.name"
        :maxlength="16"
        @change="changeSiteInfoName"
      />
    </el-form-item>
    <div class="operations-container">
      <el-button
        type="primary"
        :disabled="!canAddFreq"
        @click="addFrequency"
      >
        {{ $t('operations.Add') }}
      </el-button>
      <el-button
        type="primary"
        :disabled="!canAddFreq || selectedIndex === -1"
        @click="insertFrequency(selectedIndex)"
      >
        {{ $t('operations.Insert') }}
      </el-button>
    </div>
    <el-table
      :data="siteInfo.frequency"
      :empty-text="$t('msgbox.emptyText')"
      highlight-current-row
      class="site-info-frequency-table"
      style="width: 100%"
      :cell-class-name="cellClassName"
      @row-click="onRowClick"
    >
      <el-table-column
        label=""
        width="80"
      >
        <template #default="scope">
          <el-icon
            v-if="selectedIndex === scope.$index"
            class="current-row-icon"
          >
            <CaretRight />
          </el-icon>
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.rxFrequency')">
        <template #default="scope">
          <el-form-item
            label-width="0"
            :prop="'frequency.' + scope.$index + '.rxFreq'"
            :rules="getFrequencyRules(scope.$index)"
          >
            <frequencyMhz
              v-model="scope.row.rxFreq"
              :maxlength="9"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.txFrequency')">
        <template #default="scope">
          <el-form-item
            label-width="0"
            :prop="'frequency.' + scope.$index + '.txFreq'"
            :rules="getFrequencyRules(scope.$index)"
          >
            <frequencyMhz
              v-model="scope.row.txFreq"
              :maxlength="9"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- 操作列,删除 -->
      <el-table-column
        label="#"
        width="80"
      >
        <template #default="scope">
          <el-button
            type="danger"
            icon="delete"
            circle
            :disabled="!canDeleteFrequency"
            @click.stop="deleteFrequency(scope.$index)"
          />
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script>
  import { frequencyMhz2Hz, frequencyHz2Mhz } from '@/utils/bfutil'
  import validateRules from '@/utils/validateRules'

  // 表格列class name
  const cellClassNames = ['index', 'rxFreq', 'txFreq', 'action']

  // 选中行id缓存, site id => selectedIndex
  const selectIndexCache = {}

  export default {
    name: 'SiteInfo',
    emits: ['update:modelValue', 'name-change'],
    props: {
      modelValue: {
        type: Object,
        default: () => ({}),
      },
      maxFreq: {
        type: Number,
        default: 16,
      },
      freqRange: {
        type: Object,
        default: () => ({
          min: frequencyMhz2Hz(400),
          max: frequencyMhz2Hz(480),
        }),
      },
    },
    data() {
      return {
        // 传递的站点信息参数, 默认有一个频率数据
        siteInfo: {
          id: 0,
          name: '',
          frequency: [],
        },
        // 默认选中第一行
        selectedIndex: 0,
      }
    },
    methods: {
      /**
       * 获取频率输入表单校验规则
       * @param {String} index 当前行的索引
       * @return {Array} 返回的规则集
       */
      getFrequencyRules(index) {
        return [
          // 必填
          validateRules.required(),
          // 有效范围
          validateRules.range('blur', this.freqRange.min, this.freqRange.max,
            `${frequencyHz2Mhz(this.freqRange.min)} ~ ${frequencyHz2Mhz(this.freqRange.max)}`,
          ),
          // 每行的频率的信息不能重复, 发射频率与接收频率可以相同
          {
            validator: (rule, value, callback) => {
              // 检验时, 跳过当前行的频率数据
              const frequency = this.siteInfo.frequency.filter((freq, idx) => idx !== index)
              const message = this.$t('writeFreq.freqRepeated')
              if (isNaN(value)) {
                return callback(new Error(message))
              }

              if (frequency.some(freq => freq.rxFreq === value || freq.txFreq === value)) {
                return callback(new Error(message))
              }

              callback()
            },
            trigger: 'blur',
          },
        ]
      },
      // 设置单元格class回调
      cellClassName({ /* row, column, rowIndex,  */
        columnIndex,
      }) {
        return `site-info-cell-${cellClassNames[columnIndex] || columnIndex}`
      },
      // 单击表格行时,设置选中该行
      onRowClick(row, column, event) {
        let clickRowIndex = this.siteInfo.frequency.findIndex(val => val === row)
        if (clickRowIndex === -1) {
          clickRowIndex = this.siteInfo.frequency.findIndex(
            val => val.rxFreq === row.rxFreq && val.txFreq === row.txFreq)
        }
        this.selectedIndex = clickRowIndex
      },
      // 站点信息名称变更,需要通知父组件更新树形菜单节点名称
      changeSiteInfoName(name) {
        if (!name) {
          name = `${this.$t('writeFreq.siteInfo')} ${this.siteInfo.id + 1}`
          this.siteInfo.name = name
        }
        this.$emit('name-change', this.siteInfo.id, name)
      },
      cloneFreq() {
        let rxFreq = this.freqRange.min
        let isUsed = this.checkFrequencyIsExist(rxFreq)
        while (isUsed) {
          rxFreq += frequencyMhz2Hz(2)
          if (rxFreq >= this.freqRange.max) {
            break
          }
          isUsed = this.checkFrequencyIsExist(rxFreq)
        }
        return {
          rxFreq,
          txFreq: Math.min(this.freqRange.max, rxFreq + frequencyMhz2Hz(1)),
        }
      },
      addFrequency() {
        if (!this.canAddFreq) { return }
        const freq = this.cloneFreq()
        this.siteInfo.frequency.push(freq)
        // 新增后,最后一行为选中行
        this.selectedIndex = this.siteInfo.frequency.length - 1
      },
      insertFrequency(index) {
        if (!this.canAddFreq) { return }
        const freq = this.cloneFreq()
        this.siteInfo.frequency.splice(index, 0, freq)
        // 选中行应该保留在插入前的行
        this.$nextTick(() => {
          this.selectedIndex = Math.min(index + 1, this.siteInfo.frequency.length - 1)
        })
      },
      deleteFrequency(index) {
        // 必须保留一个频率配置信息
        if (!this.canDeleteFrequency) { return }
        this.siteInfo.frequency.splice(index, 1)
        // 选中行应该保留在当前行
        this.$nextTick(() => {
          this.selectedIndex = Math.min(index, this.siteInfo.frequency.length - 1)
        })
      },
      /**
       * 判断指定的频率是否已经存在
       * @param {Number} value - 判断的频率值
       * @return {Boolean} - 存在返回true,否则返回false
       */
      checkFrequencyIsExist(value) {
        return this.siteInfo.frequency.some(freq => freq.rxFreq === value || freq.txFreq === value)
      },
    },
    watch: {
      modelValue: {
        handler(val) {
          // if (Object.keys(val).length === 0) {
          //   return
          // }
          this['siteInfo'] = val
          // 默认选中第一行频率
          this.$nextTick(() => {
            if (this.siteInfo.id in selectIndexCache) {
              this.selectedIndex = selectIndexCache[this.siteInfo.id]
            }
          })
        },
        deep: true,
        immediate: true,
      },
      siteInfo: {
        handler(val) {
          this.$emit('update:modelValue', val)
        },
        deep: true,
        immediate: true,
      },
      selectedIndex: {
        handler(val) {
          selectIndexCache[this.siteInfo.id] = val
        },
        immediate: true,
      },
    },
    computed: {
      // tableMaxHeight() {
      //   // return 'calc(100% - 32px - 65px - 8px)'
      //   return ''
      // },
      canDeleteFrequency() {
        return this.siteInfo.frequency?.length > 1
      },
      canAddFreq() {
        return this.siteInfo.frequency?.length < this.maxFreq
      },
    },
    components: {
      frequencyMhz: () =>
        import(
          '@/components/common/FrequencyMhz'
        ),
    },
  }
</script>

<style lang="scss">
  .virtual-cluster-site-form {

    .current-row-icon {
      margin-right: 6px;
    }

    .operations-container {
      text-align: center;
    }

    .site-info-cell-index .cell {
      text-align: right;
    }

    .site-info-frequency-table .el-form-item {
      margin-bottom: unset !important;
    }

    .el-form-item__error {
      position: relative;
      left: 80%;
      bottom: 24px;
      height: 0;
    }
  }
</style>
