<template>
  <el-form
    ref="siteForm"
    class="flex flex-col justify-center items-center virtual-cluster-site-form virtual-cluster-site-form-511svt"
    :model="siteInfo"
    label-width="auto"
  >
    <el-form-item :label="$t('dialog.name')">
      <el-input
        v-model="siteInfo.name"
        :maxlength="16"
        @change="changeSiteInfoName"
      />
    </el-form-item>
    <el-form-item class="operations-container">
      <el-button
        type="primary"
        class="w-32"
        :disabled="!canAddFreq"
        @click="addFrequency"
      >
        {{ $t('operations.Add') }}
      </el-button>
      <el-button
        type="primary"
        class="w-32"
        :disabled="!canAddFreq || selectedIndex === -1"
        @click="insertFrequency(selectedIndex)"
      >
        {{ $t('operations.Insert') }}
      </el-button>
    </el-form-item>
    <el-table
      :data="frequency"
      :empty-text="$t('msgbox.emptyText')"
      highlight-current-row
      class="site-info-frequency-table"
      height="calc(100% - 134px)"
      style="width: 100%"
      :cell-class-name="cellClassName"
      @row-click="onRowClick"
    >
      <el-table-column
        label=""
        width="80"
      >
        <template #default="scope">
          <el-icon
            v-if="selectedIndex === scope.$index"
            class="current-row-icon"
          >
            <CaretRight />
          </el-icon>

          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.rxFrequency')">
        <template #default="scope">
          <el-form-item
            label-width="0"
            :prop="'rxFreqList.' + scope.$index"
            :ref="'rxFreqList_' + scope.$index"
            :rules="getFrequencyRules(scope.$index, scope.row, 'txFreqList_' + scope.$index)"
          >
            <frequencyMhz
              v-model="scope.row.rxFreq"
              :maxlength="9"
              @update:model-value="val => rxFreqChange(val, scope.$index)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column :label="$t('dialog.txFrequency')">
        <template #default="scope">
          <el-form-item
            label-width="0"
            :prop="'txFreqList.' + scope.$index"
            :ref="'txFreqList_' + scope.$index"
            :rules="getFrequencyRules(scope.$index, scope.row, 'rxFreqList_' + scope.$index)"
          >
            <frequencyMhz
              v-model="scope.row.txFreq"
              :maxlength="9"
              @update:model-value="val => txFreqChange(val, scope.$index)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- 操作列,删除 -->
      <el-table-column
        label="#"
        width="80"
      >
        <template #default="scope">
          <el-button
            type="danger"
            icon="delete"
            circle
            :disabled="!canDeleteFrequency"
            @click.stop="deleteFrequency(scope.$index)"
          />
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script>
  import bfutil, { frequencyMhz2Hz, frequencyHz2Mhz } from '@/utils/bfutil'
  import validateRules from '@/utils/validateRules'

  // 表格列class name
  const cellClassNames = ['index', 'rxFreq', 'txFreq', 'action']

  // 选中行id缓存, site id => selectedIndex
  const selectIndexCache = {}

  export default {
    name: 'SiteInfo',
    emits: ['update:modelValue', 'name-change'],
    props: {
      modelValue: {
        type: Object,
        default: () => ({}),
      },
      maxFreq: {
        type: Number,
        default: 16,
      },
      freqRange: {
        type: Object,
        default: () => ({
          min: frequencyMhz2Hz(400),
          max: frequencyMhz2Hz(480),
        }),
      },
    },
    data() {
      return {
        // 传递的站点信息参数, 默认有一个频率数据
        siteInfo: {
          id: 0,
          count: 1,
          txFreqList: [bfutil.frequencyMhz2Hz(410)],
          rxFreqList: [bfutil.frequencyMhz2Hz(400)],
          name: 'site info 1',
        },
        // 默认选中第一行
        selectedIndex: 0,
      }
    },
    methods: {
      // 通过监听表单的input事件，同步更新对应的数据源
      rxFreqChange(value, index) {
        this.siteInfo.rxFreqList[index] = value
      },
      txFreqChange(value, index) {
        this.siteInfo.txFreqList[index] = value
      },
      /**
       * 获取频率输入表单校验规则
       * @param {String} index 当前行的索引
       * @param {Record<string, any>} row 当前行频率数据
       * @param {string} refName 当前行另一个频点FormItem组件的ref属性值
       * @return {Array} 返回的规则集
       */
      getFrequencyRules(index, row, refName) {
        return [
          // 必填
          validateRules.required(),
          // 有效范围
          validateRules.range('blur', this.freqRange.min, this.freqRange.max,
            `${frequencyHz2Mhz(this.freqRange.min)} ~ ${frequencyHz2Mhz(this.freqRange.max)}`,
          ),
          // 每行的频率的信息不能重复, 发射频率与接收频率可以相同
          {
            validator: (rule, value, callback) => {
              // 检验时, 跳过当前行的频率数据
              const frequency = this.frequency.filter((freq, idx) => idx !== index)
              const message = this.$t('writeFreq.freqRepeated')
              if (isNaN(value)) {
                return callback(new Error(message))
              }

              if (frequency.some(freq => freq.rxFreq === row.rxFreq && freq.txFreq === row.txFreq)) {
                return callback(new Error(message))
              }

              callback()
              // 清除另一个频率组件的校验错误消息
              const freqComp = Array.isArray(this.$refs[refName]) ? this.$refs[refName][0] : this.$refs[refName]
              if (freqComp?.validateState === 'error' && freqComp?.validateMessage === message) {
                freqComp.clearValidate()
              }
            },
            trigger: 'blur',
          },
        ]
      },
      // 设置单元格class回调
      cellClassName({ /* row, column, rowIndex,  */columnIndex }) {
        return `site-info-cell-${cellClassNames[columnIndex] || columnIndex}`
      },
      // 单击表格行时,设置选中该行
      onRowClick(row, column, event) {
        let clickRowIndex = this.frequency.findIndex(val => val === row)
        if (clickRowIndex === -1) {
          clickRowIndex = this.frequency.findIndex(
            val => val.rxFreq === row.rxFreq && val.txFreq === row.txFreq)
        }
        this.updateSelectedIndex(clickRowIndex)
      },
      updateSelectedIndex(index) {
        this.selectedIndex = index
        selectIndexCache[this.siteInfo.id] = index
      },
      // 站点信息名称变更,需要通知父组件更新树形菜单节点名称
      changeSiteInfoName(name) {
        if (!name) {
          name = `${this.$t('writeFreq.siteInfo')} ${this.siteInfo.id + 1}`
          this.siteInfo.name = name
        }
        this.$emit('name-change', this.siteInfo.id, name)
      },
      cloneFreq() {
        let rxFreq = this.freqRange.min
        // 以上最小接收频率为基准,计算下一个发射频率
        let txFreq = rxFreq + frequencyMhz2Hz(10)
        let isUsed = this.checkFrequencyIsExist(rxFreq, txFreq)
        while (isUsed) {
          rxFreq += frequencyMhz2Hz(1)
          txFreq = rxFreq + frequencyMhz2Hz(10)
          if (rxFreq >= this.freqRange.max) {
            break
          }
          isUsed = this.checkFrequencyIsExist(rxFreq, txFreq)
        }
        return {
          rxFreq,
          txFreq,
        }
      },
      addFrequency() {
        if (!this.canAddFreq) {
          return
        }
        const freq = this.cloneFreq()
        this.siteInfo.txFreqList.push(freq.txFreq)
        this.siteInfo.rxFreqList.push(freq.rxFreq)
        this.siteInfo.count = Math.min(this.siteInfo.txFreqList.length, this.siteInfo.rxFreqList.length)

        this.$nextTick(() => {
          // 新增后,最后一行为选中行
          this.updateSelectedIndex(this.siteInfo.count - 1)
        })
      },
      insertFrequency(index) {
        if (!this.canAddFreq) {
          return
        }
        const freq = this.cloneFreq()
        this.siteInfo.txFreqList.splice(index, 0, freq.txFreq)
        this.siteInfo.rxFreqList.splice(index, 0, freq.rxFreq)
        this.siteInfo.count = Math.min(this.siteInfo.txFreqList.length, this.siteInfo.rxFreqList.length)

        // 选中行应该保留在插入前的行
        this.$nextTick(() => {
          this.updateSelectedIndex(Math.min(index + 1, this.siteInfo.count - 1))
        })
      },
      deleteFrequency(index) {
        // 必须保留一个频率配置信息
        if (!this.canDeleteFrequency) {
          return
        }
        this.siteInfo.txFreqList.splice(index, 1)
        this.siteInfo.rxFreqList.splice(index, 1)
        this.siteInfo.count = Math.min(this.siteInfo.txFreqList.length, this.siteInfo.rxFreqList.length)

        // 选中行应该保留在当前行
        this.$nextTick(() => {
          this.updateSelectedIndex(Math.min(index, this.siteInfo.count - 1))
        })
      },
      /**
       * 判断指定的频率是否已经存在
       * @param {Number} rxFreq - 接收频率值
       * @param {Number} txFreq - 发射频率值
       * @return {Boolean} - 存在返回true,否则返回false
       */
      checkFrequencyIsExist(rxFreq, txFreq) {
        return this.frequency.some(freq => freq.rxFreq === rxFreq && freq.txFreq === txFreq)
      },
    },
    watch: {
      modelValue: {
        handler(val) {
          this['siteInfo'] = val
          // 默认选中第一行频率
          this.$nextTick(() => {
            if (this.siteInfo.id in selectIndexCache) {
              this.selectedIndex = selectIndexCache[this.siteInfo.id]
            } else {
              this.updateSelectedIndex(0)
            }
          })
        },
        deep: true,
        immediate: true,
      },
      siteInfo: {
        handler(val) {
          this.$emit('update:modelValue', val)
        },
        deep: true,
        immediate: true,
      },
    },
    computed: {
      frequency() {
        // 读取指定频点数量的频率信息，生成table需要的数组
        const list = []
        const count = this.siteInfo.count
        for (let i = 0; i < count; i++) {
          const freq = {
            txFreq: this.siteInfo.txFreqList[i],
            rxFreq: this.siteInfo.rxFreqList[i],
          }
          list.push(freq)
        }
        return list
      },
      canDeleteFrequency() {
        return this.siteInfo.count > 1
      },
      canAddFreq() {
        return this.siteInfo.count < this.maxFreq
      },
    },
    components: {
      frequencyMhz: () =>
        import(
          '@/components/common/FrequencyMhz'
        ),
    },
  }
</script>

<style lang="scss">
  @use '@/css/interphoneWf/tabsWf.scss' as *;

  .virtual-cluster-site-form {
    &>.el-form-item {
      @extend .form-item-width-xs;
    }

    .operations-container .el-form-item__content {
      text-align: center;
      justify-content: center;
    }

    .current-row-icon {
      margin-right: 6px;
    }

    .site-info-cell-index .cell {
      text-align: right;
    }

    .site-info-frequency-table .el-form-item {
      margin-bottom: unset !important;
    }
  }

  .virtual-cluster-site-form-511svt {
    .el-form-item__error {
      position: relative;
      left: 75%;
      top: -24px;
      height: 0;
    }
  }
</style>
