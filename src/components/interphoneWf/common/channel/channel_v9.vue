<template>
  <el-tabs
    v-model="channelDataTabs"
    class="channel-list-tabs settings-tabs-box h-full"
    type="border-card"
  >
    <el-tab-pane
      :label="$t('writeFreq.areaList')"
      name="areaList"
      class="h-full"
    >
      <el-table
        id="area-channel-table"
        ref="zoneTable"
        :data="zoneDataList"
        :default-sort="{ prop: 'areaId', order: 'ascending' }"
        border
        stripe
        highlight-current-row
        :row-class-name="zoneTableRowClassName"
        height="calc(100% - 1px)"
        style="width: 100%"
      >
        <el-table-column
          type="expand"
          label="#"
        >
          <template #default="props">
            <el-table
              ref="channelTable"
              :data="getZoneChannels(props.row.chIdList)"
              :default-sort="{ prop: 'chId', order: 'ascending' }"
              :row-class-name="channelTableRowClassName"
              border
              stripe
              highlight-current-row
              style="width: 100%"
              @row-click="zoneChannelListClick"
              @row-dblclick="zoneChannelListDblclick"
            >
              <el-table-column
                :label="$t('dialog.chId')"
                prop="chId"
                sortable
              />
              <el-table-column
                :label="$t('dialog.chName')"
                prop="chName"
                sortable
              />
              <el-table-column
                :label="$t('dialog.rxFrequency')"
                sortable
              >
                <template #default="scope">
                  <span v-text="frequencyHz2Mhz(scope.row.receivingFrequency)" />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('dialog.txFrequency')"
                sortable
              >
                <template #default="scope">
                  <span v-text="frequencyHz2Mhz(scope.row.transmittingFrequency)" />
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('writeFreq.areaId')"
          prop="areaId"
          sortable
        />
        <el-table-column
          :label="$t('dialog.areaName')"
          sortable
        >
          <template #default="scope">
            <el-input
              v-if="scope.row.editing"
              v-model="scope.row.areaName"
            />
            <span
              v-else
              v-text="scope.row.areaName"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('writeFreq.channelNumber')"
          sortable
        >
          <template #default="scope">
            <span v-text="scope.row.chIdList.length" />
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
    <template v-if="channelDataList.length > 0">
      <el-tab-pane
        :label="$t('dialog.channelSetting')"
        name="chSettings"
        class="h-full"
      >
        <el-form
          ref="channelData"
          class="channel-settings-form"
          :model="channelData"
          :rules="channelDataRules"
          label-position="top"
        >
          <el-row
            :gutter="20"
            class="no-margin-x"
            type="flex"
            align="middle"
          >
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item
                :label="$t('dialog.chName')"
                prop="chName"
              >
                <el-input
                  v-model="channelData.chName"
                  :maxlength="16"
                />
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.chType')">
                <el-select
                  v-model="channelData.chType"
                  @change="chTypeChanged"
                >
                  <el-option
                    v-for="item in chTypeList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.broadBand')">
                <el-select v-model="channelData.subChannelData.flagConfig.bandwidthFlag">
                  <el-option
                    v-for="item in bandwidthFlagList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.scanAndRoamList')">
                <el-select
                  v-model="channelData.scanList"
                  :disabled="disScanList"
                >
                  <el-option
                    v-for="item in chScanRoamGroupList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.powerAndFlag.autoScan"
                  :disabled="disAutoScan"
                >
                  <span v-text="$t('writeFreq.autoScanning')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.roamConfig.autoRoam"
                  :disabled="disAutoRoam"
                >
                  <span v-text="$t('writeFreq.autoRoaming')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.squelchLevel')">
                <el-input-number
                  v-model="channelData.powerAndFlag.squelchLevel"
                  step-strictly
                  :min="0"
                  :max="9"
                  :step="1"
                />
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.colorCodes')">
                <el-input-number
                  v-model="channelData.subChannelData.colorCode"
                  step-strictly
                  :min="0"
                  :max="colorCodeMax"
                  :step="1"
                />
              </el-form-item>
            </el-col>
            <el-col
              v-if="isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.roamConfig.ipSiteConnect"
                  :disabled="isSvtEnable || channelData.powerAndFlag.onlyReceiveSign"
                  @change="ipSiteConnectChange"
                >
                  <span v-text="$t('writeFreq.ipSiteConnection')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.alarmSettings.allowOfflineSign"
                  :disabled="isSvtEnable"
                >
                  <span v-text="$t('dialog.allowOffNetwork')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox v-model="channelData.powerAndFlag.onlyReceiveSign">
                  <span v-text="$t('dialog.receiveOnly')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox v-model="channelData.subChannelData.alarmSettings.priorityInterrupt">
                  <span v-text="$t('writeFreq.priorityInterrupt')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.funcSettings.TDMAThroughMode"
                  :disabled="!sameFreq"
                  @change="TDMAThroughModeChange"
                >
                  <span v-text="$t('writeFreq.TDMAThroughMode')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <!-- 联网模式，控制联网参数与虚拟集群 -->
            <el-col
              v-if="isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.networkingMode')">
                <el-select
                  v-model="networkingMode"
                  @change="netWorkingModeChange"
                >
                  <el-option
                    v-for="item in networkingModeList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.funcSettings.localCall"
                  :disabled="!isConnectNetworking && !isSvtEnable"
                >
                  <span v-text="$t('writeFreq.localCall')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <!-- 虚拟集群站点信息，只在联网模式为SVT时显示，信道10版本以上才有该参数 -->
            <el-col
              v-if="hasSVT && isDChannel & hasSvtSiteInfo"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.SVTSiteInfo')">
                <el-select
                  v-model="channelData.subChannelData.svtSiteInfo"
                  :disabled="!isSvtEnable"
                >
                  <el-option
                    v-for="item in SVTSiteInfoList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.chTimeSlotCalibrator')">
                <el-select
                  v-model="channelData.subChannelData.slotConfig.channelSlotCalibrator"
                  :disabled="!channelData.subChannelData.funcSettings.TDMAThroughMode"
                >
                  <el-option
                    v-for="item in chTimeSlotCalList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.slotMode')">
                <el-select
                  v-model="channelData.subChannelData.slotConfig.slot"
                  :disabled="isSvtEnable"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option
                    v-for="item in slotModeList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.virtualTimeSlot')">
                <el-select
                  v-model="channelData.subChannelData.slotConfig.virtualTimeSlot"
                  :placeholder="$t('dialog.select')"
                  :disabled="isSvtEnable || channelData.subChannelData.slotConfig.slot !== 2"
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option
                    v-for="(item, i) in virtualTimeSlotList"
                    :key="i"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.voicePriority')">
                <el-input-number
                  v-model="channelData.subChannelData.slotConfig.voicePriority"
                  step-strictly
                  :min="0"
                  :max="3"
                  :step="1"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 加密相关 -->
          <el-row
            v-if="!isAChannel"
            :gutter="20"
            class="no-margin-x"
            type="flex"
            align="middle"
          >
            <el-divider>
              <el-icon class="divider-icon">
                <CaretBottom />
              </el-icon>
              <span
                class="divider-label"
                v-text="$t('writeFreq.encryption')"
              />
            </el-divider>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.encryptConfig.enable"
                  :disabled="!encryptEnable"
                >
                  <span v-text="$t('dialog.enable')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.type')">
                <el-select
                  v-model="channelData.subChannelData.encryptConfig.type"
                  :placeholder="$t('dialog.select')"
                  :disabled="!channelEnableEncrypt"
                  :no-match-text="$t('dialog.noMatchText')"
                  @change="encryptTypeChange"
                >
                  <el-option
                    v-for="item in encryptTypes"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.encryptionAlgorithm')">
                <el-select
                  v-model="channelData.subChannelData.encryptConfig.encryptAlgorithm"
                  :placeholder="$t('dialog.select')"
                  :disabled="!channelEnableEncrypt"
                  :no-match-text="$t('dialog.noMatchText')"
                  @change="encryptAlgorithmChange"
                >
                  <el-option
                    v-for="item in encryptAlgorithmList"
                    :key="item.label + item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.keyList')">
                <el-select
                  v-model="channelData.subChannelData.encryptListId"
                  :placeholder="$t('dialog.select')"
                  :disabled="!channelEnableEncrypt"
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option
                    v-for="item in encryptKeyList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.encryptConfig.multiKeyDecrypt"
                  :disabled="!enableAdvancedEncryption"
                >
                  <span v-text="$t('writeFreq.multiKeyDecryption')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.encryptConfig.randomKeyEncrypt"
                  :disabled="!enableAdvancedEncryption"
                >
                  <span v-text="$t('writeFreq.randomKeyEncryption')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            :gutter="20"
            class="no-margin-x"
            type="flex"
            align="middle"
          >
            <el-divider>
              <el-icon class="divider-icon">
                <CaretBottom />
              </el-icon>
              <span
                class="divider-label"
                v-text="$t('dialog.receive')"
              />
            </el-divider>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item
                :label="$t('dialog.rxFrequency')"
                prop="receivingFrequency"
              >
                <frequencyMhz
                  v-model="channelData.receivingFrequency"
                  :maxlength="9"
                  @input="rxFreqChange"
                />
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <freqMapOffset
                v-model="freqOffset"
                v-model:dstFreq="channelData.transmittingFrequency"
                :srcFreq="channelData.receivingFrequency"
                :freqRange="[{ min: minFrequency, max: maxFrequency }]"
                @mapping="() => txFreqChange(channelData.transmittingFrequency)"
              />
            </el-col>
            <el-col
              v-if="!isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.decoding')">
                <el-select
                  v-model="channelData.subChannelData.subsonicDecode"
                  filterable
                  :allow-create="enableCustomCTCSS && isAChannel"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                  @change="subsonicDecodeChange"
                >
                  <el-option
                    v-for="item in subtoneCodeDataList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.tailSelection')">
                <el-select
                  v-model="channelData.subChannelData.soundConfig.receivingTailTone"
                  :disabled="subsonicDecodeIsDigitalCode"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option
                    v-for="item in tailToneList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.receiveGroupList')">
                <el-select
                  v-model="channelData.subChannelData.receiveGroup"
                  disabled
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option
                    v-for="item in receiveGroupList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.alarmSettings.emergencyAlertTip"
                  :disabled="isSvtEnable || isConnectNetworking"
                >
                  <span v-text="$t('writeFreq.emergencyAlarmIndication')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.alarmSettings.emergencyAlertConfirm"
                  :disabled="isSvtEnable || !channelData.subChannelData.alarmSettings.emergencyAlertTip"
                >
                  <span v-text="$t('writeFreq.emergencyAlarmConfirm')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.alarmSettings.emergencyCallTip"
                  :disabled="isSvtEnable || isConnectNetworking"
                >
                  <span v-text="$t('writeFreq.emergencyCallAlert')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row
            :gutter="20"
            class="no-margin-x"
            type="flex"
            align="middle"
          >
            <el-divider>
              <el-icon class="divider-icon">
                <CaretBottom />
              </el-icon>
              <span
                class="divider-label"
                v-text="$t('dialog.emission')"
              />
            </el-divider>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item
                :label="$t('dialog.txFrequency')"
                prop="transmittingFrequency"
              >
                <frequencyMhz
                  v-model="channelData.transmittingFrequency"
                  :maxlength="9"
                  :disabled="onlyReceive"
                  @input="txFreqChange"
                />
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.encoding')">
                <el-select
                  v-model="channelData.subChannelData.subsonicEncode"
                  filterable
                  :allow-create="enableCustomCTCSS && isAChannel"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                  :disabled="onlyReceive"
                  @change="subsonicEncodeChange"
                >
                  <el-option
                    v-for="item in subtoneCodeDataList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.plosive')">
                <el-select
                  v-model="channelData.subChannelData.soundConfig.plosive"
                  :disabled="subsonicEncodeIsDigitalCode"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option
                    v-for="item in tailToneList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.sendGroup')">
                <el-select
                  v-model="channelData.subChannelData.defaultAddress"
                  disabled
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option
                    v-for="item in defaultAddressList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.emergencySystem')">
                <el-select
                  v-model="channelData.subChannelData.emergencySysId"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                  :disabled="isSvtEnable || isConnectNetworking || onlyReceive"
                >
                  <el-option
                    v-for="item in emergencySysIdList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.txPower')">
                <el-select
                  v-model="channelData.powerAndFlag.powerSign"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                  :disabled="onlyReceive"
                >
                  <el-option
                    v-for="item in txPowerTypes"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.sendTimeLimiter')">
                <el-input-number
                  v-model="channelData.transmissionLimit"
                  step-strictly
                  :min="15"
                  :max="495"
                  :step="15"
                  :disabled="onlyReceive"
                />
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.totPwdUpdateDelay')">
                <el-input-number
                  v-model="channelData.totKeyUpdateDelay"
                  step-strictly
                  :min="0"
                  :max="255"
                  :step="5"
                  :disabled="onlyReceive"
                />
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('dialog.permissionConditions')">
                <el-select
                  v-model="channelData.subChannelData.alarmSettings.permitConditions"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                  :disabled="onlyReceive"
                >
                  <el-option
                    v-for="item in permissionConditionsList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isAChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.funcSettings.singleCallRes"
                  :disabled="onlyReceive"
                >
                  <span v-text="$t('dialog.singleCallConfirm')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item :label="$t('writeFreq.busyChannelLock')">
                <el-select
                  v-model="channelData.subChannelData.flagConfig.busyChannelLock"
                  :placeholder="$t('dialog.select')"
                  :no-match-text="$t('dialog.noMatchText')"
                  :disabled="onlyReceive"
                >
                  <el-option
                    v-for="item in busyChannelLockList"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              v-if="!isDChannel"
              :xs="24"
              :sm="12"
            >
              <el-form-item>
                <el-checkbox
                  v-model="channelData.subChannelData.soundConfig.tailCancellation"
                  :disabled="onlyReceive"
                >
                  <span v-text="$t('dialog.tailCancellation')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
    </template>
  </el-tabs>
</template>

<script>
  import { cloneDeep, merge } from 'lodash'
  import bfutil from '@/utils/bfutil'
  import validateRules from '@/utils/validateRules'
  import {
    AnalogCodeDataOptions,
    AnalogDigitalCodeDataOptions,
    decodeSubsonic,
    encodeSubsonic2String,
    subtoneIsDigitalCode,
  } from '@/writingFrequency/interphone/subtoneCode'
  import { getDefaultChannelParam, getDefaultChannel } from './channel'
  import { checkModel } from '@/writingFrequency/interphone/common'
  import { defineAsyncComponent } from 'vue'

  // 400MHz
  const DefaultFrequency = 400
  const NetworkModeType = {
    None: 0,
    SDC: 1,
    SVT: 2,
  }

  export default {
    name: 'ChannelV9',
    props: {
      // 单位为MHz
      minFrequency: {
        type: Number,
        default: 400, // 400MHz
      },
      maxFrequency: {
        type: Number,
        default: 480, // 480MHz
      },

      zoneDataList: {
        type: Array,
        default: function() {
          return []
        },
      },
      channelDataList: {
        type: Array,
        default: function() {
          return []
        },
      },
      roamList: {
        type: Array,
        default: function() {
          return []
        },
      },
      scanList: {
        type: Array,
        default: function() {
          return []
        },
      },
      rxGroupList: {
        type: Array,
        default: function() {
          return []
        },
      },
      digitAlarmList: {
        type: Array,
        default: function() {
          return []
        },
      },
      selectedAddressBook: {
        type: Array,
        default: function() {
          return []
        },
      },
      originAddressBook: {
        type: Array,
        default: function() {
          return []
        },
      },
      virtualCluster: {
        type: Object,
        default: function() {
          return {}
        },
      },

      // 密钥列表
      encryptEnable: {
        type: Boolean,
        default: false,
      },
      encryptXORList: {
        type: Array,
        default: () => [],
      },
      encryptARC4List: {
        type: Array,
        default: () => [],
      },
      encryptAES256List: {
        type: Array,
        default: () => [],
      },
      // 支持SVT虚拟集群的信道，才会有SVT联网模式
      hasSVT: {
        type: Boolean,
        default: true,
      },
      // 信道的版本号
      version: {
        type: Number,
        default: 10,
      },
      siteInfoList: {
        type: Array,
        default: () => [],
      },
      // 机型信息
      model: {
        type: String,
        default: '',
      },
      // 是否支持自定义亚音
      enableCustomCTCSS: {
        type: Boolean,
        default: true,
      },
      // 默认解码亚音参数
      defaultDecodeCTCSS: {
        type: Number,
        default: 0x0670,
      },
      // 默认编码亚音参数
      defaultEncodeCTCSS: {
        type: Number,
        default: 0x0670,
      },
    },
    data() {
      return {
        channelDataTabs: 'areaList',
        channelData: getDefaultChannel(this.version, 0),
        // 频率偏移值
        freqOffset: 0,
      }
    },
    methods: {
      // 频率变更，则置‘无’
      rxFreqChange(value) {
        this.channelData.subChannelData.svtSiteInfo = 0xff
      },
      // 频率变更，则置‘无’
      txFreqChange(value) {
        this.channelData.subChannelData.svtSiteInfo = 0xff
      },
      // 提供给父组件调用内容的el-form表单校验规则
      validate(callback) {
        if (typeof callback === 'function') {
          this.$refs.channelData?.validate((valid) => {
            callback(valid)
          })
          return
        }

        return Promise.resolve(this.$refs.channelData?.validate() ?? true)
      },
      TDMAThroughModeChange(value) {
        // TDMA直通模式为true，如果彩色码大于14，则修改为14
        if (value && this.channelData.subChannelData.colorCode > this.colorCodeMax) {
          this.channelData.subChannelData.colorCode = this.colorCodeMax
        }
      },
      frequencyHz2Mhz: bfutil.frequencyHz2Mhz,
      frequencyMhz2Hz: bfutil.frequencyMhz2Hz,

      updateParentProp(prop, value) {
        this.$emit(`update:${prop}`, value)
      },
      zoneTableRowClassName({
        row,
        rowIndex,
      }) {
        const cls = (row.chIdList && row.chIdList.length) ? 'has-details' : 'no-details'
        return `zone-row zone-${rowIndex} ${cls}`
      },
      channelTableRowClassName({ rowIndex }) {
        return `channel-row channel-${rowIndex}`
      },
      getChannelById(id) {
        for (let i = 0; i < this.channelDataList.length; i++) {
          const channel = this.channelDataList[i]
          if (channel.chId === id) {
            return channel
          }
        }
        return undefined
      },
      getZoneChannels(chIdList = []) {
        return chIdList.map((chId) => {
          return this.getChannelById(chId)
        }).filter(channel => !!channel)
      },
      setChannelData(data) {
        this.channelData = data
        // 纯模拟信道可自定义参数，为确保参数正确显示，先将参数设置为0,再重置
        if (this.channelData.chType === 1) {
          // 纯模拟信道可自定义参数，需要将值转换成字符串显示
          let subsonicDecode = this.channelData.subChannelData.subsonicDecode
          let subsonicEncode = this.channelData.subChannelData.subsonicEncode
          const codeList = this.subtoneCodeDataList
          if (this.enableCustomCTCSS) {
            if (codeList.filter(item => item.value === subsonicDecode).length === 0 && typeof subsonicDecode ===
              'number') {
              subsonicDecode = decodeSubsonic(subsonicDecode)
            }
            if (codeList.filter(item => item.value === subsonicEncode).length === 0 && typeof subsonicEncode ===
              'number') {
              subsonicEncode = decodeSubsonic(subsonicEncode)
            }
          }

          this.channelData.subChannelData.subsonicDecode = 0
          this.channelData.subChannelData.subsonicEncode = 0
          this.$nextTick(() => {
            this.channelData.subChannelData.subsonicDecode = subsonicDecode
            this.channelData.subChannelData.subsonicEncode = subsonicEncode
          })
        }
      },
      zoneChannelListClick(row) {
        // if (row.chId === this.channelData.chId) { return }
        const channelDataForm = this.$refs.channelData
        if (!channelDataForm) {
          return
        }
        // 如果信道设置的数据在信道列表中，则需要验证表单通过后再切换信道
        if (this.channelDataList.includes(this.channelData)) {
          channelDataForm.validate()
            .then(() => {
              this.setChannelData(row)
            })
            .catch(() => {
            })
        } else {
          // 默认初始化信道，直接赋值以同步数据
          this.setChannelData(row)
        }
      },
      zoneChannelListDblclick(row) {
        this.zoneChannelListClick(row)
        this.channelDataTabs = 'chSettings'
      },

      chTypeChanged(val) {
        const defSubChannelData = cloneDeep(getDefaultChannelParam(this.version, val))
        this.channelData['subChannelData'] = merge(defSubChannelData,
          this.channelData.subChannelData,
        )
        // 非数字信道，有亚音频参数，需要重置为默认值
        // 为确保纯模拟信道自定义参数正确显示，先将亚音频置为0,再重置参数
        if (val > 0) {
          this.channelData.subChannelData.subsonicDecode = 0
          this.channelData.subChannelData.subsonicEncode = 0
          this.$nextTick(() => {
            this.channelData.subChannelData.subsonicDecode = this.defaultDecodeCTCSS
            this.channelData.subChannelData.subsonicEncode = this.defaultEncodeCTCSS
          })
        }
      },
      clearRoamListSelectedChannel(chId) {
        const roamList = [...this.roamList]
        for (let i = 0; i < roamList.length; i++) {
          const roam = roamList[i]
          const index = roam.memberList.findIndex((val) => val === chId)
          if (index > -1) {
            roam.memberList.splice(index, 1)
            roam.memberCount--
          }
        }

        this.updateParentProp('roamList', roamList)
      },
      ipSiteConnectChange() {
        this.channelData.scanList = 0xFF
        // 将该信道从漫游组列表中清除
        this.clearRoamListSelectedChannel(this.channelData.chId)
      },
      isConnectNetworkingChange(val) {
        this.channelData.scanList = 0xFF
        if (val) {
          this.channelData.powerAndFlag.autoScan = false
          if (this.channelData.subChannelData.alarmSettings) {
            this.channelData.subChannelData.alarmSettings.emergencyAlertTip = false
            this.channelData.subChannelData.alarmSettings.emergencyAlertConfirm = false
            this.channelData.subChannelData.alarmSettings.emergencyCallTip = false
          }
          this.channelData.subChannelData.emergencySysId = 0xFF
          // 联网与虚拟集群只能2选1
          if (this.channelData.subChannelData.funcSettings) {
            this.channelData.subChannelData.funcSettings.svtEnable = false
          }
        }
      },
      svtEnableChange(value) {
        if (value) {
          // 联网与虚拟集群只能2选1
          if (this.channelData.subChannelData.funcSettings) {
            this.channelData.subChannelData.funcSettings.networking = false
          }
          if (this.channelData.subChannelData.slotConfig) {
            this.channelData.subChannelData.slotConfig.slot = 2
            this.channelData.subChannelData.slotConfig.virtualTimeSlot = 0
          }

          // 取消报警相关设置
          if (this.channelData.subChannelData.alarmSettings) {
            this.channelData.subChannelData.alarmSettings.emergencyAlertTip = false
            this.channelData.subChannelData.alarmSettings.emergencyAlertConfirm = false
            this.channelData.subChannelData.alarmSettings.emergencyCallTip = false
          }
          this.channelData.subChannelData.emergencySysId = 0xFF
        } else {
          if (!this.hasSVT) {
            return
          }

          // 取消选择时，需要将虚拟集群中包含该信道的数据删除
          const virtualCluster = { ...this.virtualCluster }
          const index = virtualCluster.siteList.findIndex(
            v => v === this.channelData.chId,
          )
          if (index !== -1) virtualCluster.siteList.splice(index, 1)
          this.updateParentProp('virtualCluster', virtualCluster)
        }
      },
      netWorkingModeChange(value) {
        // 不是虚拟集群SVT模式，重置站点信息选项
        if (value !== NetworkModeType.SVT) {
          this.channelData.subChannelData.svtSiteInfo = 0xff
        }

        // 不设置联网模式
        if (value === NetworkModeType.None) {
          this.isConnectNetworkingChange(false)
          this.svtEnableChange(false)

          if (this.channelData.subChannelData.funcSettings) {
            this.channelData.subChannelData.funcSettings.localCall = false
          }
          return
        }

        // SDC模式，以前的联网模式
        if (value === NetworkModeType.SDC) {
          this.isConnectNetworkingChange(true)
          this.svtEnableChange(false)
          return
        }

        // SVT模式，新加的虚拟集群模式
        if (value === NetworkModeType.SVT) {
          this.svtEnableChange(true)
          this.isConnectNetworkingChange(false)
        }
      },
      encryptTypeChange(value) {
        // 加密类型变更, 需要重置算法及密钥列表
        if (this.channelData.subChannelData.encryptConfig) {
          this.channelData.subChannelData.encryptConfig.encryptAlgorithm = 0
        }
        this.channelData.subChannelData.encryptListId = 0xFF

        // 重置多密钥加、解密参数
        if (value === 0 && this.channelData.subChannelData.encryptConfig) {
          this.channelData.subChannelData.encryptConfig.multiKeyDecrypt = false
          this.channelData.subChannelData.encryptConfig.randomKeyEncrypt = false
        }
      },
      encryptAlgorithmChange() {
        // 加密算法变更, 需要重置密钥列表
        this.channelData.subChannelData.encryptListId = 0xFF
      },
      subsonicDecodeChange(val) {
        if (this.isAChannel) {
          this.channelData.subChannelData.subsonicDecode = encodeSubsonic2String(val)
        }
      },
      subsonicEncodeChange(val) {
        if (this.isAChannel) {
          this.channelData.subChannelData.subsonicEncode = encodeSubsonic2String(val)
        }
      },
    },
    computed: {
      colorCodeMax() {
        return this.channelData.subChannelData.funcSettings?.TDMAThroughMode ? 14 : 15
      },
      channelDataRules() {
        const minFrequency = this.minFrequency ?? DefaultFrequency
        const maxFrequency = this.maxFrequency ?? DefaultFrequency + 80
        const min = bfutil.frequencyMhz2Hz(minFrequency)
        const max = bfutil.frequencyMhz2Hz(maxFrequency)
        const smg = `${minFrequency} ~ ${maxFrequency}`
        const frequency = [
          validateRules.required(['blur']),
          validateRules.range(['blur'], min, max, smg),
        ]
        return {
          chName: [
            validateRules.required(['blur']),
          ],
          receivingFrequency: frequency,
          transmittingFrequency: frequency,
        }
      },
      chTypeList() {
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0,
          },
          {
            label: this.$t('dialog.analogChannel'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.digitalCompatibleAnalog'),
            value: 2,
          },
          {
            label: this.$t('writeFreq.analogCompatibleDigital'),
            value: 3,
          },
        ]
      },
      isDChannel() {
        return this.channelData.chType === 0
      },
      isAChannel() {
        return this.channelData.chType === 1
      },
      isD2AChannel() {
        return this.channelData.chType === 2
      },
      isA2DChannel() {
        return this.channelData.chType === 3
      },
      bandwidthFlagList() {
        return [
          {
            label: this.$t('writeFreq.broadBand'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.narrowBand'),
            value: 1,
          },
        ]
      },
      hasSvtSiteInfo() {
        return this.version === 10
      },
      SVTSiteInfoList() {
        const rxFreq = this.channelData.receivingFrequency
        const txFreq = this.channelData.transmittingFrequency
        const list = this.siteInfoList
          .filter(item => {
            // 判断站点信息中对应的频率是否与当前信道的频率相同，如果不同，重置信道的站点信息配置参数
            const txFreqIndex = item.txFreqList.findIndex(freq => freq === txFreq)
            return txFreqIndex > -1 && item.rxFreqList[txFreqIndex] === rxFreq
          })
          .map(item => {
            return {
              label: item.name,
              value: item.id,
            }
          })
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
          ...list,
        ]
      },
      isSvtEnable() {
        return this.channelData.subChannelData.funcSettings?.svtEnable ?? false
      },
      isConnectNetworking() {
        return this.channelData.subChannelData?.funcSettings?.networking ?? false
      },
      ipSiteConnect() {
        return this.channelData.roamConfig?.ipSiteConnect ?? false
      },
      disScanList() {
        return (this.isConnectNetworking && !this.ipSiteConnect) || this.isSvtEnable
      },
      disAutoScan() {
        return this.ipSiteConnect || this.channelData.scanList === 0xFF || this.isSvtEnable
      },
      disAutoRoam() {
        return !this.ipSiteConnect || this.channelData.scanList === 0xFF || this.isSvtEnable
      },
      chScanRoamGroupList() {
        const list = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
        ]
        if (this.channelData.chType === 0 && this.ipSiteConnect) {
          return list.concat(this.roamList.map(item => {
            return {
              label: item.name,
              value: item.roamId,
            }
          }))
        }

        return list.concat(
          this.scanList.map((item) => {
            return {
              label: item.name,
              value: item.groupId,
            }
          }),
        )
      },
      sameFreq() {
        return this.channelData.receivingFrequency === this.channelData.transmittingFrequency
      },
      networkingMode: {
        get() {
          if (!this.channelData.subChannelData.funcSettings?.networking &&
            !this.channelData.subChannelData.funcSettings?.svtEnable) {
            return 0
          }

          if (this.channelData.subChannelData.funcSettings?.networking) {
            return 1
          }

          if (this.channelData.subChannelData.funcSettings?.svtEnable) {
            return 2
          }

          return 0
        },
        set(value) {
          if (!this.channelData.subChannelData.funcSettings) {
            return
          }

          switch (value) {
            case 0:
              this.channelData.subChannelData.funcSettings.networking = false
              this.channelData.subChannelData.funcSettings.svtEnable = false
              break
            case 1:
              this.channelData.subChannelData.funcSettings.networking = true
              this.channelData.subChannelData.funcSettings.svtEnable = false
              break
            case 2:
              this.channelData.subChannelData.funcSettings.svtEnable = true
              this.channelData.subChannelData.funcSettings.networking = false
              break
          }
        },
      },
      // 510SDC,511SDC因为R7F芯片使用了该版本的信道协议，需要屏蔽SVT联网模式
      networkingModeList() {
        const list = [
          {
            label: this.$t('dialog.nothing'),
            value: NetworkModeType.None,
          },
          {
            label: 'SDC',
            value: NetworkModeType.SDC,
          },
        ]

        if (!this.sameFreq && this.hasSVT) {
          list.push({
            label: 'SVT',
            value: NetworkModeType.SVT,
          })
        }

        return list
      },
      chTimeSlotCalList() {
        return [
          {
            label: this.$t('dataTable.fail'),
            value: 0,
          },
          {
            label: this.$t('dataTable.pass'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.firstChoice'),
            value: 2,
          },
        ]
      },
      // 虚拟时隙
      slotModeList() {
        return [
          {
            label: this.$t('dialog.timeSlot', { num: 1 }),
            value: 0,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 2 }),
            value: 1,
          },
          {
            label: this.$t('dialog.virtualCluster'),
            value: 2,
          },
        ]
      },
      virtualTimeSlotList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 1 }),
            value: 1,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 2 }),
            value: 2,
          },
        ]
      },
      channelEnableEncrypt() {
        return this.encryptEnable && (this.channelData.subChannelData.encryptConfig?.enable ?? false)
      },
      encryptTypes() {
        // 加密类型，0 基础，1 高级 default = 0
        return [
          {
            label: this.$t('writeFreq.base'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.advanced'),
            value: 1,
          },
        ]
      },
      encryptAlgorithmList() {
        switch (this.channelData.subChannelData.encryptConfig?.type) {
          case 0:
            // 基础类型：0 异或，1 增强异或，2 ARC4，3 AES256，default=0
            return [
              {
                label: this.$t('writeFreq.xor'),
                value: 0,
              },
              {
                label: this.$t('writeFreq.enhancedXor'),
                value: 1,
              },
              {
                label: 'ARC4',
                value: 2,
              },
              {
                label: 'AES256',
                value: 3,
              },
            ]
          case 1:
            // 高级类型：0 ARC4，1 AES256
            return [
              {
                label: 'ARC4',
                value: 0,
              },
              {
                label: 'AES256',
                value: 1,
              },
            ]
          default:
            return []
        }
      },
      encryptKeys() {
        // 密钥列表序号，根据加密类型不同选择不同密钥列表
        // 基础类型：对应基础密钥列表
        // 高级类型：ARC4对应ARC4密钥列表；AES256对应AES256列表
        // {[类型]: {[算法]: 密钥列表}}
        return {
          0: {
            0: this.encryptXORList,
            1: this.encryptXORList,
            2: this.encryptXORList,
            3: this.encryptXORList,
          },
          1: {
            0: this.encryptARC4List,
            1: this.encryptAES256List,
          },
        }
      },
      encryptKeyList() {
        // 根据加密类型和算法返回加密列表候选
        const type = this.channelData.subChannelData.encryptConfig.type
        const encryptAlgorithm = this.channelData.subChannelData.encryptConfig.encryptAlgorithm
        const keys = this.encryptKeys[type][encryptAlgorithm] || []
        const defaultList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
        ]

        // 密钥列表序号，根据加密类型不同选择不同密钥列表
        // value是密钥序号，而非是密钥ID
        return defaultList.concat(
          keys.map(item => {
            return {
              label: item.name,
              value: item.index,
            }
          }),
        )
      },
      enableAdvancedEncryption() {
        return this.channelEnableEncrypt && this.channelData.subChannelData.encryptConfig?.type === 1
      },
      isTD818SVT() {
        return checkModel(['818SVT00'], this.model)
      },
      subtoneCodeDataList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFFFF,
          },
        ]
        /**
         * 默认的亚音，信道类型为key，亚音编码列表为值
         * @type {{[key:number|string]: Array<any>>}}
         */
        const SubtoneCodeData = {
          0: def,
          1: def.concat(AnalogCodeDataOptions),
          // 2、3信道类型亚音码
          default: AnalogDigitalCodeDataOptions,
        }

        // TD818SVT模拟信道亚音编解码采用C6000版本
        // https://git.kicad99.com/bfdx/bf8100-web/-/issues/164
        if (this.isTD818SVT) {
          SubtoneCodeData[1] = SubtoneCodeData.default
        }

        return SubtoneCodeData[this.channelData.chType] ?? SubtoneCodeData.default
      },
      subsonicDecodeIsDigitalCode() {
        if (this.channelData.subChannelData.subsonicDecode === 0xFFFF) {
          return true
        }
        return subtoneIsDigitalCode(this.channelData.subChannelData.subsonicDecode)
      },
      subsonicEncodeIsDigitalCode() {
        if (this.onlyReceive || this.channelData.subChannelData.subsonicEncode === 0xFFFF) {
          return true
        }
        return subtoneIsDigitalCode(this.channelData.subChannelData.subsonicEncode)
      },
      tailToneList() {
        return [
          {
            label: this.$t('writeFreq.noSubtone'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.standardPhase'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.nonstandardPhase'),
            value: 2,
          },
        ]
      },
      defaultAddressList() {
        const cache = []
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFFFF,
          },
        ].concat(
          this.selectedAddressBook.concat(this.originAddressBook)
            .filter((address) => {
              if (cache.includes(address.id)) {
                return false
              }
              cache.push(address.id)
              return true
            })
            .map((address) => {
              return {
                label: address.name,
                value: address.id,
              }
            }),
        )
      },
      receiveGroupList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFFFF,
          },
        ]
        const list = this.rxGroupList.map((item) => {
          return {
            label: item.groupName,
            value: item.groupId,
          }
        })
        return def.concat(list)
      },
      onlyReceive() {
        return this.channelData.powerAndFlag.onlyReceiveSign
      },
      emergencySysIdList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
        ]
        const digitAlarmList = this.digitAlarmList.map((item) => {
          return {
            label: item.alarmName,
            value: item.alarmID,
          }
        })
        return def.concat(digitAlarmList)
      },
      txPowerTypes() {
        // 0-低
        // 2-高
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.high'),
            value: 2,
          },
        ]
      },
      permissionConditionsList() {
        // 0 可用彩色码
        // 1 始终
        // 2 信道空闲
        return [
          {
            label: this.$t('dialog.availableColorCode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.always'),
            value: 1,
          },
          {
            label: this.$t('dialog.channelIdle'),
            value: 2,
          },
        ]
      },
      busyChannelLockList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.carrier'),
            value: 1,
          },
          {
            label: 'CTCSS/CDCSS',
            value: 2,
          },
        ]
      },

    },
    watch: {
      'channelData.subChannelData.funcSettings.TDMAThroughMode'(val) {
        if (!val && this.channelData.subChannelData.slotConfig) {
          this.channelData.subChannelData.slotConfig.channelSlotCalibrator = 0
        }
      },
      'channelData.subChannelData.slotConfig.slot'(val) {
        if (val !== 2 && this.channelData.subChannelData.slotConfig) {
          this.channelData.subChannelData.slotConfig.virtualTimeSlot = 0
        }
      },
      'channelData.subChannelData.funcSettings.networking'(val) {
        if (!val && this.channelData.subChannelData.funcSettings) {
          this.channelData.subChannelData.funcSettings.localCall = false
        }
      },
      sameFreq(value) {
        // 如果是同频点，则重置联网模式
        if (value) {
          this.networkingMode = 0
          this.channelData.subChannelData.svtSiteInfo = 0xff
        }
      },
    },
    components: {
      frequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
      freqMapOffset: defineAsyncComponent(() => import('../freqMapOffset')),
    },
  }
</script>

<style scoped></style>
