import { cloneDeep } from 'lodash'

const DefaultFrequency = {
  value: 400000000,
  label: '400',
}
const AnalogChannelV9 = {
  signalType: 0xFF,
  propertyId: 0,
  signalSysId: 0xFF,
  rxSqType: 0,
  autoResetTime: 1,
  autoResetMode: 0,
  emergencyID: 0,
  flagConfig: {
    bandwidthFlag: 1,
    beatFrequency: false,
    busyChannelLock: 0,
    preEmphasis: 0,
    pressureDiffusion: 0,
    scrambleSwitch: 0,
    allowOfflineSign: false,
  },
  soundConfig: {
    tailCancellation: true,
    plosive: 0,
    receivingTailTone: 0,
  },
  subsonicDecode: 0x0670,
  subsonicEncode: 0x0670,
  scramblingValue: 0,
}
const DigitChannelV9 = {
  colorCode: 1,
  alarmSettings: {
    allowOfflineSign: false,
    offlineSign: 0,
    emergencyAlertTip: false,
    emergencyAlertConfirm: false,
    emergencyCallTip: false,
    priorityInterrupt: false,
    permitConditions: 1,
  },
  // 紧急系统ID值，default = 0xFF 表示无
  emergencySysId: 0xFF,
  receiveGroup: 0xFFFF,
  defaultAddress: 0xFFFF,
  funcSettings: {
    TDMAThroughMode: false,
    singleCallRes: false,
    networking: true,
    localCall: false,
    directMode: false,
    svtEnable: false,
  },
  slotConfig: {
    slot: 1,
    virtualTimeSlot: 0,
    voicePriority: 1,
    channelSlotCalibrator: 0,
  },
  encryptConfig: {
    enable: false,
    type: 0,
    encryptAlgorithm: 0,
    randomKeyEncrypt: false,
    multiKeyDecrypt: false,
    smsEncrypt: false,
  },
  encryptListId: 0xFF,
}
const DigitAnalogChannelV9 = {
  ...cloneDeep(DigitChannelV9),
  ...cloneDeep(AnalogChannelV9),
}
const OneChannelV9 = {
  chId: 0,
  chType: 0,
  receivingFrequency: DefaultFrequency.value,
  transmittingFrequency: DefaultFrequency.value,
  scanList: 0xFF,
  subChannelData: {},
  powerAndFlag: {
    autoScan: false,
    powerSign: 2,
    onlyReceiveSign: false,
    squelchLevel: 3,
  },
  roamConfig: {
    scanType: 0,
    ipSiteConnect: false,
    autoRoam: false,
  },
  PBAID: 0,
  transmissionLimit: 300,
  totKeyUpdateDelay: 0,
  chName: 'CH 1',
}

/**
 * 获取版本9的信道的信道类型参数
 * @param chType {number} 信道类型
 * @returns {{[key:string]: any}} 各类型的信道参数，数字信道、模拟信道、数字模拟信道等
 */
export function getDefaultChannelParamV9(chType = 0) {
  switch (chType) {
    case 0:
      return DigitChannelV9
    case 1:
      return AnalogChannelV9
    default:
      return DigitAnalogChannelV9
  }
}

/**
 * 获取版本9的信道参数
 * @param chType {number} 信道类型
 * @returns {{[key:string]: any}} 信道参数
 */
export function getDefaultChannelV9(chType = 0) {
  const subChannelData = getDefaultChannelParamV9(chType)
  const Channel = cloneDeep(OneChannelV9)
  Channel.chType = chType
  Channel.subChannelData = cloneDeep(subChannelData)

  return Channel
}

const AnalogChannelFuncSettingsV5 = {
  bandwidthFlag: 1,
  beatFrequency: false,
  busyChannelLock: 0,
  preEmphasis: 0,
  pressureDiffusion: 0,
  scrambleSwitch: 0,
  allowOfflineSign: false,
  tailCancellation: true,
  plosive: 0,
  receivingTailTone: 0,
}
const AnalogChannelV5 = {
  funcSettings: { ...AnalogChannelFuncSettingsV5 },
  subsonicDecode: 0x0670,
  subsonicEncode: 0x0670,
  scramblingValue: 0,
}
const DigitChannelAlarmSettingsV5 = {
  allowOfflineSign: false,
  offlineSign: 0,
  emergencyAlertTip: false,
  emergencyAlertConfirm: false,
  emergencyCallTip: false,
  priorityInterrupt: false,
  permitConditions: 1,
}
const DigitChannelFuncSettingsV5 = {
  TDMAThroughMode: false,
  singleCallRes: false,
  networking: true,
  localCall: false,
  slot: 1,
  virtualTimeSlot: 0,
  voicePriority: 1,
  channelSlotCalibrator: 0,
}
const DigitChannelV5 = {
  colorCode: 1,
  alarmSettings: { ...DigitChannelAlarmSettingsV5 },
  // 紧急系统ID值，default = 0xFF 表示无
  emergencySysId: 0xFF,
  receiveGroup: 0xFF,
  defaultAddress: 0xFFFF,
  funcSettings: { ...DigitChannelFuncSettingsV5 },
}
const DigitAnalogChannelV5 = {
  colorCode: 1,
  alarmSettings: { ...DigitChannelAlarmSettingsV5 },
  emergencySysId: 0xFF,
  receiveGroup: 0xFF,
  defaultAddress: 0xFFFF,
  funcSettings: {
    ...DigitChannelFuncSettingsV5,
    ...AnalogChannelFuncSettingsV5,
  },
  subsonicDecode: 0x0670,
  subsonicEncode: 0x0670,
  scramblingValue: 0,
}
const OneChannelV5 = {
  chId: 0,
  chType: 0,
  receivingFrequency: DefaultFrequency.value,
  transmittingFrequency: DefaultFrequency.value,
  scanList: 0xFF,
  subChannelData: {},
  powerAndFlag: {
    autoScan: false,
    powerSign: 1,
    onlyReceiveSign: false,
    squelchLevel: 3,
  },
  roamConfig: {
    scanType: 0,
    ipSiteConnect: false,
    autoRoam: false,
  },
  transmissionLimit: 300,
  totKeyUpdateDelay: 0,
  chName: 'CH 1',
}

/**
 * 获取版本5的信道的信道类型参数
 * @param chType {number} 信道类型
 * @returns {{[key:string]: any}} 各类型的信道参数，数字信道、模拟信道、数字模拟信道等
 */
export function getDefaultChannelParamV5(chType = 0) {
  switch (chType) {
    case 0:
      return DigitChannelV5
    case 1:
      return AnalogChannelV5
    default:
      return DigitAnalogChannelV5
  }
}

/**
 * 获取版本5的信道参数
 * @param chType {number} 信道类型
 * @returns {{[key:string]: any}} 信道参数
 */
export function getDefaultChannelV5(chType = 0) {
  const subChannelData = getDefaultChannelParamV5(chType)
  const Channel = cloneDeep(OneChannelV5)
  Channel.chType = chType
  Channel.subChannelData = cloneDeep(subChannelData)

  return Channel
}

const AnalogChannelV10 = {
  signalType: 0xff,
  propertyId: 0,
  signalSysId: 0xff,
  rxSqType: 0,
  autoResetTime: 1,
  autoResetMode: 0,
  emergencyID: 0,
  flagConfig: {
    bandwidthFlag: 1,
    beatFrequency: false,
    busyChannelLock: 0,
    preEmphasis: 0,
    pressureDiffusion: 0,
    scrambleSwitch: 0,
    allowOfflineSign: false
  },
  soundConfig: {
    tailCancellation: true,
    plosive: 0,
    receivingTailTone: 0
  },
  subsonicDecode: 0x0670,
  subsonicEncode: 0x0670,
  scramblingValue: 0
}
const DigitChannelV10 = {
  colorCode: 1,
  alarmSettings: {
    allowOfflineSign: false,
    offlineSign: 0,
    emergencyAlertTip: false,
    emergencyAlertConfirm: false,
    emergencyCallTip: false,
    priorityInterrupt: false,
    permitConditions: 1
  },
  // 紧急系统ID值，default = 0xFF 表示无
  emergencySysId: 0xff,
  // 虚拟集群站点信息ID
  svtSiteInfo: 0xFF,
  receiveGroup: 0xffff,
  defaultAddress: 0xffff,
  funcSettings: {
    TDMAThroughMode: false,
    singleCallRes: false,
    networking: true,
    localCall: false,
    directMode: false,
    svtEnable: false
  },
  slotConfig: {
    slot: 1,
    virtualTimeSlot: 0,
    voicePriority: 1,
    channelSlotCalibrator: 0
  },
  encryptConfig: {
    enable: false,
    type: 0,
    encryptAlgorithm: 0,
    randomKeyEncrypt: false,
    multiKeyDecrypt: false,
    smsEncrypt: false
  },
  encryptListId: 0xff
}
const DigitAnalogChannelV10 = {
  ...cloneDeep(DigitChannelV10),
  ...cloneDeep(AnalogChannelV10)
}
const OneChannelV10 = {
  chId: 0,
  chType: 0,
  receivingFrequency: DefaultFrequency.value,
  transmittingFrequency: DefaultFrequency.value,
  scanList: 0xff,
  subChannelData: {},
  powerAndFlag: {
    autoScan: false,
    powerSign: 2,
    onlyReceiveSign: false,
    squelchLevel: 3
  },
  roamConfig: {
    scanType: 0,
    ipSiteConnect: false,
    autoRoam: false
  },
  PBAID: 0,
  transmissionLimit: 300,
  totKeyUpdateDelay: 0,
  chName: 'CH 1'
}

/**
 * 获取版本10的信道的信道类型参数
 * @param chType {number} 信道类型
 * @returns {{[key:string]: any}} 各类型的信道参数，数字信道、模拟信道、数字模拟信道等
 */
export function getDefaultChannelParamV10(chType = 0) {
  switch (chType) {
    case 0:
      return DigitChannelV10
    case 1:
      return AnalogChannelV10
    default:
      return DigitAnalogChannelV10
  }
}

/**
 * 获取版本10的信道参数
 * @param chType {number} 信道类型
 * @returns {{[key:string]: any}} 信道参数
 */
export function getDefaultChannelV10(chType = 0) {
  const subChannelData = getDefaultChannelParamV10(chType)
  const Channel = cloneDeep(OneChannelV10)
  Channel.chType = chType
  Channel.subChannelData = cloneDeep(subChannelData)

  return Channel
}

/**
 * 根据指定的版本号和信道类型，获取默认的信道参数
 * @param version {number} 版本号
 * @param chType {number} 信道类型
 * @returns {{[key:string]: any}} 信道参数
 */
export function getDefaultChannel(version, chType = 0) {
  switch (version) {
    case 9:
      return getDefaultChannelV9(chType)
    case 10:
      return getDefaultChannelV10(chType)
    default:
      return getDefaultChannelV5(chType)
  }
}

/**
 * 获取指定版本的各类型信道参数
 * @param version {number} 版本号
 * @param chType {number} 信道类型
 * @return {{[p: string]: *}} 各类型信道参数
 */
export function getDefaultChannelParam(version, chType = 0) {
  switch (version) {
    case 9:
      return getDefaultChannelParamV9(chType)
    case 10:
      return getDefaultChannelParamV10(chType)
    default:
      return getDefaultChannelParamV5(chType)
  }
}

/**
 * 输入一个频点，根据给定的频点范围和偏移量，计算另一个频点值
 * 频单位为MHz
 * @param srcFreq {number} 输入的频点
 * @param offset {number} 偏移量
 * @param freqMin {number} 最小频点
 * @param freqMax {number} 最大频点
 * @returns {{offset:number,dstFreq:number}} 计算出的频点值和被修改后的偏移量
 */
export function calcFreqFromOffset(srcFreq, offset, freqMin, freqMax) {
  const oneMhz = 1 // 1MHz
  let diff = -1
  let dstFreq = srcFreq + offset

  // 偏移值不合法，接收+偏移 < 最小值
  if (dstFreq < freqMin) {
    diff = freqMin - srcFreq
    // 接收频率等于最小值时，将偏移值设置为1MHz
    if (diff === 0) {
      diff = oneMhz
    }
    // 发射频率 = 接收频率 + 偏移值
    // 偏移频率 = 最小值 - 接收频率
    dstFreq = srcFreq + diff
    offset = diff
  }

  // 偏移值不合法，接收+偏移 > 最大值
  if (dstFreq > freqMax) {
    diff = freqMax - srcFreq
    // 接收频率等于最大值时，将偏移值设置为-1MHz
    if (diff === 0) {
      diff = oneMhz * -1
    }
    // 发射频率 = 接收频率 + 偏移值
    // 偏移频率 = 最大值 - 接收频率
    dstFreq = srcFreq + diff
    offset = diff
  }

  return {
    offset,
    dstFreq,
  }
}
