<template>
  <el-form
    class="roam-form"
    :model="oneRoamGroup"
    :rules="oneRoamGroupRules"
    label-position="top"
  >
    <el-row
      :gutter="20"
      class="no-margin-x flex justify-center"
      type="flex"
      align="middle"
    >
      <el-col
        :xs="24"
        :class="['transfer-wrapper', locale]"
      >
        <el-transfer
          v-model="oneRoamGroup.roamChList"
          :titles="titles"
          :data="availableChannelList"
          :props="{
            key: 'value'
          }"
          @change="membersListChange"
        >
          <template #default="{ option }">
            <span>{{ option.label }}</span>
          </template>
        </el-transfer>
      </el-col>
      <div class="roma-form-item">
        <el-col v-if="hasMainSiteRoaming">
          <el-form-item>
            <el-checkbox v-model="oneRoamGroup.config.mainSiteRoaming">
              <span v-text="$t('writeFreq.activeSiteRoamingEnable')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item
            :label="$t('dialog.name')"
            prop="name"
          >
            <el-input
              v-model="oneRoamGroup.name"
              :maxlength="16"
              @change="nameChange"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="hasSiteSearchTimer">
          <el-form-item :label="$t('writeFreq.siteSearchTimer')">
            <el-input-number
              v-model="oneRoamGroup.siteSearchTimer"
              step-strictly
              :min="0"
              :max="255"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="!hasRssi">
          <el-form-item :label="$t('dialog.rssiThreshold')">
            <el-input-number
              v-model="oneRoamGroup.rssi"
              step-strictly
              :min="-120"
              :max="-80"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="hasRssi">
          <el-form-item :label="$t('dialog.rssiThreshold')">
            <bf-input-number
              v-model="oneRoamGroup.rssi"
              step-strictly
              :min="0"
              :max="40"
              :formatter="rssiFormat"
              @cus-change="rssiChange"
            />
          </el-form-item>
        </el-col>
      </div>
    </el-row>
  </el-form>
</template>

<script>
  import { messageBox, Types } from '@/utils/notify'
  import validateRules from '@/utils/validateRules'
  import { TD930SDCR7F, TD930SVTR7F, TM8250SDCR7F, TM825FRModel, TM8250SVTR7F } from '@/writingFrequency/interphone/models'

  // const BaseConfig = {
  //   roamId: 0,
  //   name: '',
  //   rssi: -120,
  //   roamChList: [0xFFFE],
  //   roamChCount: 1,
  // }
  // const OneRoamGroup = {
  //   ...BaseConfig,
  //   config: {
  //     mainSiteRoaming: false,
  //     followMainSite: false,
  //     channelAsScanList: false,
  //   },
  //   siteSearchTimer: 60,
  // }
  // const TD811RoamGroup = {
  //   ...BaseConfig,
  // }
  const ModelList = ['TD081100', '825SDC00', TM8250SDCR7F, TM825FRModel, '930SDC00', '930SVT00', TD930SVTR7F, TD930SDCR7F, 'R935SDC0', 'R935SVT0', 'D860SD00', 'D860SV00', TM8250SVTR7F]

  export default {
    name: 'RoamGroupList',
    emits: ['update:modelValue', 'nameChange'],
    props: {
      modelValue: {
        type: Object,
        required: true,
        validator(v) {
          return Object.prototype.toString.call(v).slice(8, -1) === 'Object'
        },
      },
      channels: {
        type: Array,
        required: true,
      },
      limit: {
        type: Number,
        default: 32,
      },
      dataId: {
        type: Number,
        default: -1,
      },
      directMode: {
        type: Boolean,
        default: false,
      },
      currChannel: {
        type: Object,
        default() {
          return {
            scanConfig: {
              ipSiteConnect: false,
            },
          }
        },
      },
      model: {
        type: String,
        default: 'TD081000',
      },
      maxCount: {
        type: Number,
        default: 16,
      },

      hasMainSiteRoaming: {
        type: Boolean,
        default: false,
      },
      hasSiteSearchTimer: {
        type: Boolean,
        default: false,
      },
      hasRssi: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        oneRoamGroup: {},
      }
    },
    methods: {
      // 3个参数，当前选中列表，穿梭方向，穿梭数据的key的集合
      membersListChange(list) {
        // 设置选中的信道时，最大为16个
        if (list.length > this.maxCount) {
          this.oneRoamGroup.roamChList = this.oneRoamGroup.roamChList.slice(
            0,
            this.maxCount,
          )
          messageBox(this.$t('writeFreq.fullList'), Types.warning)
        }
      },
      // TD081100 RSSI阀值(dBm) 0~40 信号强度阈值是用户设置接收信号强度的门限。对应显示范围 -120~-80
      rssiFormat(val) {
        return val - 120
      },
      rssiChange(val) {
        this.oneRoamGroup.rssi = parseInt(val) + 120
      },
      nameChange(val) {
        if (!val) {
          this.oneRoamGroup.name = `${this.roamGroupName} ${this.oneRoamGroup
            .roamId + 1}`
        }
        this.$emit('nameChange', this.oneRoamGroup)
      },
    },
    watch: {
      modelValue: {
        deep: true,
        immediate: true,
        handler(val) {
          this.oneRoamGroup = val
        },
      },
      oneRoamGroup: {
        deep: true,
        handler(val) {
          this.$emit('update:modelValue', val)
        },
      },
      'oneRoamGroup.roamChList': {
        deep: true,
        handler(val) {
          this.oneRoamGroup.roamChCount = val.length
        },
      },
    },
    computed: {
      locale() { return this.$i18n.locale },
      roamGroupName() {
        return ModelList.includes(this.model)
          ? this.$t('writeFreq.roamList')
          : this.$t('writeFreq.roamingGroup')
      },
      oneRoamGroupRules() {
        return {
          name: [validateRules.required(['blur'])],
        }
      },
      titles() {
        return [
          this.$t('writeFreq.availableChannel'),
          this.$t('writeFreq.containedChannel'),
        ]
      },
      nothingList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
      },
      theSelectedList() {
        return [
          {
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
          },
        ]
      },
      lastActiveChannelList() {
        return [
          {
            label: this.$t('writeFreq.lastActiveChannel'),
            value: 0xfffd,
          },
        ]
      },
      ipSiteConnect() {
        return ModelList.includes(this.model)
          ? this.currChannel.callingSettings.ipSiteConnect
          : this.currChannel.scanConfig.ipSiteConnect
      },
      channelList() {
        const filter = ModelList.includes(this.model)
          ? channel => channel.callingSettings.ipSiteConnect
          : channel => channel.scanConfig.ipSiteConnect
        return this.channels.filter(filter).map(channel => {
          return {
            label: channel.chName,
            value: channel.chId,
          }
        })
      },
      channelsIndex() {
        if (!Array.isArray(this.channels)) {
          return {}
        }

        return this.channels
          .map(data => {
            const prop = data.chId
            return { [prop]: data }
          })
          .reduce((p, c) => {
            return Object.assign(p, c)
          }, {})
      },
      availableChannelList() {
        return [
          ...this.theSelectedList.map(item => {
            item.disabled = true
            return item
          }),
          ...this.channelList,
        ]
      },
      isTD811() {
        return ModelList.includes(this.model)
      },
    },
    components: {
      bfInputNumber: () =>
        import(
          '@/components/common/bfInputNumber'
        ),
    },
  }
</script>

<style lang='scss'>
  @use "@/css/interphoneWf/scan-roam-group.scss" as *;
</style>
