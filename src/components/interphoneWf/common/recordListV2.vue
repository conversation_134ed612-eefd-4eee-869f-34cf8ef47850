<template>
  <div class="h-full record-list-v2">
    <el-form
      ref="filterForm"
      :model="filterOptions"
      class="h-auto mb-3 grid grid-cols-2 gap-x-4 gap-y-2 record-list-form"
      :label-width="labelWidth"
    >
      <div class="col-span-2 flex gap-4 record-head">
        <el-form-item
          :label="$t('dialog.programmingPwd')"
          :label-width="modelLabelWidth"
          class="w-fit mb-0"
        >
          <el-input
            v-model="ioPassword"
            type="password"
            autocomplete="new-password"
            :maxlength="8"
          />
        </el-form-item>

        <div class="btn-group">
          <el-button
            :disabled="disReadBtn"
            @click="getRecordList"
          >
            {{
              $t('writeFreq.getRecordList')
            }}
          </el-button>
          <el-button
            :disabled="downloadRecordList.length === 0 || isReading || isWriting"
            @click="startDownload"
          >
            {{ $t('writeFreq.batchDownload') }}
          </el-button>
        </div>
      </div>

      <div class="flex gap-2">
        <el-checkbox v-model="filterOptions.queryById">
          <span>{{ $t('writeFreq.queryById') }}</span>
        </el-checkbox>
        <el-select
          v-model="filterOptions.id"
          :disabled="!filterOptions.queryById"
        >
          <el-option
            v-for="opt in idOptions"
            :key="opt.label + opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </div>

      <div class="flex gap-2">
        <el-checkbox v-model="filterOptions.queryByCallDir">
          <span>{{ $t('writeFreq.queryByCallDir') }}</span>
        </el-checkbox>
        <el-select
          v-model="filterOptions.callDir"
          :disabled="!filterOptions.queryByCallDir"
        >
          <el-option
            v-for="item in callDirOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </div>

      <div class="flex gap-2">
        <el-checkbox v-model="filterOptions.queryByTime">
          <span>{{ $t('writeFreq.queryByTime') }}</span>
        </el-checkbox>
        <el-date-picker
          v-model="filterOptions.queryTime"
          type="daterange"
          range-separator="-"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          :start-placeholder="$t('dialog.startTime')"
          :end-placeholder="$t('dialog.endTime')"
          :disabled="!filterOptions.queryByTime"
          class="grow"
          @change="chooseTime"
        />
      </div>
    </el-form>

    <dataTablesVue3
      v-if="!!scrollY"
      ref="dataTable"
      :head="dthead"
      :data="recordData"
      :layout="dtLayout"
      :columnDefs="columnDefs"
      :scrollY="scrollY"
      name="recordTable"
    />
  </div>
</template>

<script>
  import dataTablesVue3 from '@/components/common/dataTablesVue3.vue'
  import { SupportedLang } from '@/modules/i18n/index.js'
  import { formatDmrIdLabel } from '@/utils/bfutil'
  import { bcdCodeTimeToDayjs, formatTime } from '@/utils/time'

  export default {
    name: 'RecordListV2',
    emits: ['downloadRecord', 'getRecordList'],
    components: {
      dataTablesVue3,
    },
    props: {
      isReading: {
        type: Boolean,
        required: true
      },
      isWriting: {
        type: Boolean,
        required: true
      },
      disReadBtn: {
        type: Boolean,
        required: true
      },
      recordList: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        filterOptions: {
          queryByTime: false,
          queryTime: [],
          queryById: false,
          id: '',
          queryByCallDir: false,
          callDir: 1,
        },
        downloadRecordList: [],
        ioPassword: '',
        scrollY: '',
      }
    },
    computed: {
      dtLayout() {
        return {
          topStart: null,
          topEnd: null,
          bottomStart: 'info',
          bottomEnd: null,
        }
      },
      recordData() {
        if (this.recordList.length === 0) return []
        return this.recordList.filter(item => {
          const byCallDir = this.filterOptions.queryByCallDir ? item.recordConfig.callInOut === this.filterOptions.callDir : true
          const byId = (this.filterOptions.queryById && this.filterOptions.id !== '') ? item.desId === this.filterOptions.id : true
          const startTime = bcdCodeTimeToDayjs(item.startTime).toDate().getTime()
          const byTime = this.filterOptions.queryByTime && this.filterOptions.queryTime.length === 2 ?
            (startTime >= new Date(this.filterOptions.queryTime[0]).getTime()
              && startTime <= new Date(this.filterOptions.queryTime[1]).getTime()) : true

          return byCallDir && byId && byTime
        })
      },
      dthead() {
        return [
          {
            title: '<input type="checkbox" class="dt-checkBox-all"></input>',
            data: '',
            width: '40px',
            render: (data, type, row, meta) => {
              return `<input type="checkbox" class="dt-checkBox" data-record-id="${row.fileNum}"></input>`
            },
            orderable: false,
          },
          {
            title: this.$t('writeFreq.recordId'),
            data: 'fileNum',
            width: '80px',
          },
          {
            title: this.$t('writeFreq.targetId'),
            data: 'desId',
            width: '120px',
            render: (data, type, row, meta) => {
              if (row.desId === 0) {
                return this.$t('dialog.simulation') + '/' + this.$t('dialog.fullCall')
              } else {
                const dmrId = (row.desId >>> 0).toString(16)
                return formatDmrIdLabel(dmrId)
              }
            },
          },
          {
            title: this.$t('writeFreq.callDir'),
            data: 'recordConfig',
            width: '120px',
            render: (data) => {
              return data.callInOut === 0 ? this.$t('writeFreq.inbound') : this.$t('writeFreq.Exhale')
            }
          },
          {
            title: this.$t('writeFreq.fileName'),
            data: 'startTime',
            width: '120px',
          },
          {
            title: this.$t('writeFreq.recordTime'),
            data: '',
            width: '100px',
            render: (data, type, row, meta) => {
              const s = ((row.frameNum * 60) / 1000)
              return Math.ceil(s) + ' s'
            },
          },
        ]
      },
      columnDefs() {
        return [
          {
            'className': 'text-center',
            'targets': [1, 3, 4, 5],
          },
          {
            'className': 'text-right',
            'targets': [6],
          },
        ]
      },
      locale() { return this.$i18n.locale },
      isFR() { return this.locale === SupportedLang.fr },
      isEN() { return this.locale === SupportedLang.enUS },
      labelWidth() {
        return this.isFR || this.isEN ? '100px' : '80px'
      },
      modelLabelWidth() {
        return this.isFR || this.isEN ? '165px' : '80px'
      },
      idOptions() {
        const desIdSet = new Set()
        const options = []
        for (let i = 0; i < this.recordList.length; i++) {
          const desId = this.recordList[i].desId
          if (desIdSet.has(desId)) continue

          let idLabel = ''
          if (desId === 0) {
            idLabel = this.$t('dialog.simulation') + '/' + this.$t('dialog.fullCall')
          } else {
            const dmrId = (desId >>> 0).toString(16)
            idLabel = formatDmrIdLabel(dmrId)
          }

          desIdSet.add(desId)
          options.push({
            label: idLabel,
            value: desId,
          })
        }

        return options
      },
      callDirOptions() {
        // 0:语音呼入 1:语音呼出
        return [
          {
            label: this.$t('writeFreq.inbound'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.Exhale'),
            value: 1,
          }
        ]
      },
    },
    methods: {
      calcScrollY() {
        // 计算表格的高度
        // 16px: 表格上方的表单的下边距
        // 38px: 表格的固定表头高度
        // 21px: 表格的底部信息栏高度
        return `calc(${this.$el.clientHeight}px - ${this.$refs.filterForm.$el.clientHeight}px - 16px - 38px - 21px)`
      },
      startDownload() {
        this.$emit('downloadRecord', this.downloadRecordList)
      },
      chooseTime(val) {
        this.filterOptions.queryTime = val === null ? [] : val.map(item => formatTime(item))
      },
      getRecordList() {
        this.$emit('getRecordList')
      },
      queryDtCheckBoxAllSelector() {
        return this.$el.querySelector('.dt-container .dt-scroll-head .dt-checkBox-all')
      },
      queryDtScrollBodyProxySelector() {
        return this.$el.querySelector('.dt-container .dt-scroll-body .dataTable > tbody')
      },
      queryDtScrollBodyCheckBoxSelector() {
        return this.$el.querySelectorAll('.dt-container .dt-scroll-body .dataTable > tbody .dt-checkBox')
      },
      chooseRecord(isChoose, recordId) {
        const index = this.downloadRecordList.findIndex(item => item.fileNum === recordId)
        if (isChoose && index === -1) { // 勾选
          const item = this.recordData.find(item => item.fileNum === recordId)
          if (item) {
            this.downloadRecordList.push(item)
          }
        } else if (!isChoose && index !== -1) { // 取消勾选
          this.downloadRecordList.splice(index, 1)
        }

        const checkBoxAll = this.queryDtCheckBoxAllSelector()
        this.downloadRecordList.length === this.recordData.length ? checkBoxAll.checked = true : checkBoxAll.checked = false
      },
      syncAllDtCheckBoxChecked(isChoose, checkBoxList) {
        this.downloadRecordList = []
        if (isChoose) {
          for (let i = 0; i < checkBoxList.length; i++) {
            checkBoxList[i].checked = true
            this.chooseRecord(true, +checkBoxList[i].dataset.recordId)
          }
        } else {
          for (let i = 0; i < checkBoxList.length; i++) {
            checkBoxList[i].checked = false
          }
        }
      },
    },
    watch: {
      recordData(/* dataList */) {
        this.$refs.dataTable?.instance?.clear()
        this.$refs.dataTable?.instance?.draw(true)
        this.$nextTick(() => {
          const chooseAllBtn = this.queryDtCheckBoxAllSelector()
          if (!chooseAllBtn) return
          const checkBoxList = this.queryDtScrollBodyCheckBoxSelector()
          this.syncAllDtCheckBoxChecked(chooseAllBtn.checked, checkBoxList)
        })
      },
    },
    mounted() {
      this.scrollY = this.calcScrollY()
      this.$nextTick(() => {
        const chooseAllBtn = this.queryDtCheckBoxAllSelector()
        chooseAllBtn?.addEventListener('change', (e) => {
          const checkBoxList = this.queryDtScrollBodyCheckBoxSelector()
          this.syncAllDtCheckBoxChecked(e.target.checked, checkBoxList)
        })

        // 代理选中下载的复选框
        const tableBody = this.queryDtScrollBodyProxySelector()
        tableBody?.addEventListener('change', (e) => {
          if (e.target.type !== 'checkbox' && !e.target.classList.contains('dt-checkBox') && e.target.dataset.recordId === undefined) {
            return
          }
          this.chooseRecord(e.target.checked, +e.target.dataset.recordId)
        })
      })
    },
    activated() {
      this.$refs.dataTable.columnsAdjust()
    }
  }
</script>

<style lang="scss">
  .record-list-v2 {
    .dt-container .dt-scroll table.dataTable {

      thead tr>th.dt-orderable-none,
      tbody tr>td.dt-orderable-none {
        padding-right: 4px;
      }
    }
  }
</style>
