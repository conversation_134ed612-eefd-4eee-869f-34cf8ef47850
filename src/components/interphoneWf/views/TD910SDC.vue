<template>
  <div class="writer-frequency-wrap">
    <selectDevice
      v-model="selectedDeviceDmrId"
      :disabled="isReading || isWriting"
    />
    <section class="TD910-layout">
      <TableTree
        :ref="menuTreeId"
        class="TD910-menu-tree"
        :treeId="menuTreeId"
        :filter="false"
        :contextmenuOption="menuTreeContextmenuOption"
        :option="menuTreeOpts"
        @loaded="treeLoaded"
      />
      <main class="TD910-content">
        <el-card
          v-if="showTD910DeviceInfo"
          shadow="never"
          class="write-freq-component deviceInfo-container"
        >
          <deviceInfo
            ref="deviceWriteInfo"
            v-model="deviceWriteInfo"
            :model="deviceModel"
            :multipleFreqRange="2"
          />
        </el-card>
        <el-card
          v-if="showGeneralSettings"
          shadow="never"
          class="write-freq-component general-settings-container"
        >
          <el-form
            ref="generalSettings"
            class="general-settings-form"
            :model="generalSettings"
            label-width="100px"
            label-position="top"
            :rules="generalSettingsRules"
          >
            <el-row
              :gutter="20"
              class="no-margin-x"
              type="flex"
              align="middle"
            >
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.terminalName')"
                  prop="deviceName"
                >
                  <el-input
                    v-model="generalSettings.deviceName"
                    :maxlength="16"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item label="DMRID">
                  <el-input
                    :value="dmrIdLabel"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('dialog.repeaterId')">
                  <el-input-number
                    v-model="generalSettings.repeaterId"
                    step-strictly
                    :min="1"
                    :max="16777215"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('dialog.programmingPwd')">
                  <el-input
                    v-model="passwordInfo.md5Key"
                    type="password"
                    :maxlength="8"
                    @input="fixMd5KeyValue"
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-if="!DeviceNoLocale"
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('dialog.languageType')">
                  <el-select
                    v-model="generalSettings.baseSettings.locale"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in localeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.voiceLevel')"
                  prop="soundCtrlLevel"
                >
                  <bf-input-number
                    v-model="generalSettings.soundCtrlLevel"
                    step-strictly
                    :min="0"
                    :max="9"
                    :step="1"
                    :formatter="(v) => { return v === 0 ? $t('writeFreq.off') : v }"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.voiceDelay')"
                  prop="soundCtrlDelay"
                >
                  <el-input-number
                    v-model="generalSettings.soundCtrlDelay"
                    step-strictly
                    :min="500"
                    :max="10000"
                    :step="500"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.sendPreambleDuration')"
                  prop="sendLeadCodeTime"
                >
                  <el-input-number
                    v-model="generalSettings.sendLeadCodeTime"
                    step-strictly
                    :min="0"
                    :max="8640"
                    :step="240"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.offNetworkGroupCallHangTime')"
                  prop="offlineGroupCallHungTime"
                >
                  <el-input-number
                    v-model="generalSettings.offlineGroupCallHungTime"
                    step-strictly
                    :min="0"
                    :max="7000"
                    :step="500"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.offNetworkSingleCallHangTime')"
                  prop="offlineSingleCallHungTime"
                >
                  <el-input-number
                    v-model="generalSettings.offlineSingleCallHungTime"
                    step-strictly
                    :min="0"
                    :max="7000"
                    :step="500"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.powerSavingMode')"
                  prop="savePowerMode"
                >
                  <el-select
                    v-model="generalSettings.savePowerMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in savePowerModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.disabledAllLED">
                    <span v-text="$t('dialog.disabledAllLed')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.rejectUnfamiliarCall">
                    <span v-text="$t('dialog.rejectingStrangeCalls')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <!--              <el-col :xs="24" :sm="12">-->
              <!--                <el-form-item>-->
              <!--                  <el-checkbox v-model="generalSettings.baseSettings.directMode">-->
              <!--                    <span v-text="$t('dialog.passThroughMode')"></span>-->
              <!--                  </el-checkbox>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.recordSettings.allowErasing">
                    <span v-text="$t('writeFreq.allowErasingDevice')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.pttAlone">
                    <span v-text="$t('writeFreq.pttAlone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.allowedSelfDestruct">
                    <span v-text="$t('writeFreq.allowedSelfDestruct')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.stealthModeHeadsetMute">
                    <span v-text="$t('writeFreq.stealthModeHeadsetMute')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row
              :gutter="20"
              class="no-margin-x"
              type="flex"
              align="middle"
            >
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.voicePrompt')" />
              </el-divider>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.soundSettings.muteAll">
                    <span v-text="$t('dialog.muteAll')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox
                    v-model="generalSettings.soundSettings.voiceNotice"
                    :disabled="muteAll"
                  >
                    <span v-text="$t('dialog.voiceIndication')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox
                    v-model="generalSettings.soundSettings.channelFreeNotice"
                    :disabled="muteAll"
                  >
                    <span v-text="$t('dialog.channelIdleIndication')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('dialog.callPermissionIndication')">
                  <el-select
                    v-model="generalSettings.soundSettings.allowCallInstruction"
                    :disabled="muteAll"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in allowCallInstructionList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item
                  :label="$t('dialog.receiveLowPowerPromptInterval')"
                  prop="powerInfoAlert"
                >
                  <el-input-number
                    v-model="generalSettings.powerInfoAlert"
                    step-strictly
                    :min="0"
                    :max="635"
                    :step="5"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 密码 -->
            <el-row
              :gutter="20"
              class="no-margin-x"
              type="flex"
              align="middle"
            >
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('loginDlg.password')" />
              </el-divider>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('dialog.powerOnPwd')">
                  <el-input
                    v-model="menuSettings.powerOnPwd"
                    type="password"
                    :maxlength="6"
                    :minlength="6"
                    @input="fixPowerOnValue"
                    @blur="fixPowerValueOnBlur"
                  >
                    <template #prefix>
                      <el-popover
                        placement="bottom"
                        trigger="hover"
                      >
                        <div class="power-on-pwd-helper center">
                          <div>{{ $t('writeFreq.powerOnPwdRange') }}</div>
                          <img :src="powerOnPwdTips">
                        </div>
                        <template #reference>
                          <el-icon class="el-input__icon">
                            <QuestionFilled />
                          </el-icon>
                        </template>
                      </el-popover>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row
              :gutter="20"
              class="no-margin-x"
              type="flex"
              align="middle"
            >
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.powerAutoConversion')" />
              </el-divider>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item>
                  <el-checkbox v-model="generalSettings.powerTransfer.enable">
                    <span v-text="$t('dialog.enable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('writeFreq.switchHighPowerThreshold')">
                  <el-input-number
                    v-model="generalSettings.highPowerLimit"
                    step-strictly
                    :min="-115"
                    :max="-75"
                    :step="1"
                    :disabled="powerTransferEnable"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('writeFreq.switchMediumPowerThreshold')">
                  <el-input-number
                    v-model="generalSettings.middlePowerLimit"
                    step-strictly
                    :min="-105"
                    :max="-70"
                    :step="1"
                    :disabled="powerTransferEnable"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
              >
                <el-form-item :label="$t('writeFreq.switchLowPowerThreshold')">
                  <el-input-number
                    v-model="generalSettings.lowPowerLimit"
                    step-strictly
                    :min="-95"
                    :max="-65"
                    :step="1"
                    :disabled="powerTransferEnable"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!--            <el-row :gutter='20' class='no-margin-x'-->
            <!--                    type='flex' align='middle'>-->
            <!--              <el-divider>-->
            <!--                <el-icon><CaretBottom /></el-icon>-->
            <!--                <span v-text='$t("dialog.recording")'></span>-->
            <!--              </el-divider>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item prop="recordFunc.recordEnable">-->
            <!--                  <el-checkbox v-model="generalSettings.recordFunc.recordEnable">-->
            <!--                    <span v-text="$t('dialog.recordEnable')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm='12'>-->
            <!--                <el-form-item :label="$t('dialog.recordCompRatio')" prop="recordFunc.recordCompressionRatio">-->
            <!--                  <el-select v-model="generalSettings.recordFunc.recordCompressionRatio"-->
            <!--                             :disabled='!!!generalSettings.recordFunc.recordEnable'-->
            <!--                             -->
            <!--                             :placeholder="$t('dialog.select')"-->
            <!--                             filterable-->
            <!--                             :no-match-text="$t('dialog.noMatchText')">-->
            <!--                    <el-option v-for="(item,i) in recordCompressionRatioList" :key="i"-->
            <!--                               :label="item.label"-->
            <!--                               :value="item.value"></el-option>-->
            <!--                  </el-select>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->
            <!--            <el-row :gutter='20' class='no-margin-x'-->
            <!--                    type='flex' align='middle'>-->
            <!--              <el-divider>-->
            <!--                <el-icon><CaretBottom /></el-icon>-->
            <!--                <span v-text='$t("dialog.timeSetting")'></span>-->
            <!--              </el-divider>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="generalSettings.syncTime">-->
            <!--                    <span v-text="$t('writeFreq.synchronisedTime')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item :label="$t('dialog.timeZoneHours')">-->
            <!--                  <el-input-number v-model="generalSettings.timeZoneHour"-->
            <!--                                   :min="-12" :max="12" :step="1"-->
            <!--                                   :disabled='syncTime'></el-input-number>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item :label="$t('dialog.timeZoneMinutes')">-->
            <!--                  <el-input-number v-model="generalSettings.timeZoneMinute"-->
            <!--                                   :min="0" :max="59" :step="1"-->
            <!--                                   :disabled='syncTime'></el-input-number>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item :label="$t('writeFreq.dateTime.year')">-->
            <!--                  <el-input-number v-model="generalSettings.year"-->
            <!--                                   :min="1970" :max="0xFFFF" :step="1"-->
            <!--                                   :disabled='syncTime'></el-input-number>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item :label="$t('writeFreq.dateTime.month')">-->
            <!--                  <el-input-number v-model="month"-->
            <!--                                   :min="1" :max="12" :step="1"-->
            <!--                                   :disabled='syncTime'></el-input-number>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item :label="$t('writeFreq.dateTime.date')">-->
            <!--                  <el-input-number v-model="generalSettings.day"-->
            <!--                                   :min="1" :max="maxDate" :step="1"-->
            <!--                                   :disabled='syncTime'></el-input-number>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item :label="$t('writeFreq.dateTime.hours')">-->
            <!--                  <el-input-number v-model="hours"-->
            <!--                                   :min="0" :max="23" :step="1"-->
            <!--                                   :disabled='syncTime'></el-input-number>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item :label="$t('writeFreq.dateTime.minutes')">-->
            <!--                  <el-input-number v-model="generalSettings.minute"-->
            <!--                                   :min="0" :max="59" :step="1"-->
            <!--                                   :disabled='syncTime'></el-input-number>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->
          </el-form>
        </el-card>
        <el-card
          v-if="showButtonSettings"
          shadow="never"
          class="write-freq-component buttonDefine-container"
        >
          <el-form
            ref="buttonDefine"
            class="buttonDefine-form"
            :model="buttonDefined"
            label-position="top"
          >
            <div class="grid grid-cols-1">
              <div>
                <el-col
                  :xs="24"
                  :sm="12"
                >
                  <el-form-item
                    :label="$t('dialog.longPressDuration')"
                    :label-width="buttonDefineLabelWidth"
                  >
                    <el-input-number
                      v-model="buttonDefined.longPressTime"
                      step-strictly
                      :min="250"
                      :max="3750"
                      :step="250"
                    />
                  </el-form-item>
                </el-col>
              </div>
              <div>
                <el-table
                  :data="buttonDefined.sideKey"
                  :empty-text="$t('msgbox.emptyText')"
                >
                  <el-table-column
                    label=""
                    min-width="65"
                  >
                    <template #default="scope">
                      <span v-text="getKeyName(scope.$index)" />
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('dialog.shortPress')"
                    min-width="100"
                  >
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.short"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="() => { syncLongPressDefine(scope.row) }"
                        >
                          <el-option
                            v-for="(shortKey, i) in getSoftKeyFuncDefine(0)"
                            :key="i"
                            :label="shortKey.label"
                            :value="shortKey.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('dialog.longPress')"
                    min-width="85"
                  >
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.long"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="() => { syncLongPressDefine(scope.row) }"
                        >
                          <el-option
                            v-for="(shortKey, i) in getSoftKeyFuncDefine(1)"
                            :key="i"
                            :label="shortKey.label"
                            :value="shortKey.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div>
                <el-divider>
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span v-text="$t('writeFreq.singleKeyFuncCall')" />
                </el-divider>
                <el-table
                  :data="buttonDefined.singleKeyCall"
                  :empty-text="$t('msgbox.emptyText')"
                >
                  <el-table-column
                    label=""
                    type="index"
                  />
                  <el-table-column
                    :label="$t('dialog.callTarget')"
                    min-width="100"
                  >
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.callId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                        >
                          <el-option
                            v-for="(item, i) in buttonDefineAddressList"
                            :key="i"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('dialog.callType')"
                    min-width="85"
                  >
                    <template #default="scope">
                      <el-form-item
                        v-if="scope.row.callId !== 0xFFFF"
                        label-width="0"
                      >
                        <el-select
                          v-model="scope.row.callType"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                        >
                          <el-option
                            v-for="(callType, i) in getSoftKeyCallTypeList(scope.row)"
                            :key="i"
                            :label="callType.label"
                            :value="callType.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('writeFreq.sms')"
                    min-width="85"
                  >
                    <template #default="scope">
                      <el-form-item
                        v-if="!(scope.row.callId === 0xFFFF || scope.row.callType !==
                          SoftKeyCallType.MSG)"
                        label-width="0"
                      >
                        <el-select
                          v-model="scope.row.smsId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          popper-class="sms-selection-container"
                        >
                          <el-option
                            v-for="sms in smsContent"
                            :key="sms.msgId"
                            :label="sms.msgContent"
                            :value="sms.msgId"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!--              <el-col :xs="24">-->
              <!--                <el-divider>-->
              <!--                  <el-icon><CaretBottom /></el-icon>-->
              <!--                  <span v-text='$t("writeFreq.presetChannel")'></span>-->
              <!--                </el-divider>-->
              <!--                <el-table :data="buttonDefined.defaultChannel"-->
              <!--                          :empty-text="$t('msgbox.emptyText')"-->
              <!--                 >-->
              <!--                  <el-table-column label=""-->
              <!--                                   min-width="100">-->
              <!--                    <template slot-scope="scope">-->
              <!--                      <span v-text='$t(`writeFreq.defaultChannel.ch${scope.$index+1}`)'></span>-->
              <!--                    </template>-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column min-width="100"-->
              <!--                                   :label="$t('writeFreq.zones.root')">-->
              <!--                    <template slot-scope="scope">-->
              <!--                      <el-form-item label-width="0">-->
              <!--                        <el-select v-model="scope.row.rootId"-->
              <!--                                   -->
              <!--                                   :placeholder="$t('dialog.select')"-->
              <!--                                   filterable-->
              <!--                                   :no-match-text="$t('dialog.noMatchText')">-->
              <!--                          <el-option v-for="(item,i) in buttonDefineZoneRootList"-->
              <!--                                     :key="i"-->
              <!--                                     :label="item.label"-->
              <!--                                     :value="item.value">-->
              <!--                          </el-option>-->
              <!--                        </el-select>-->
              <!--                      </el-form-item>-->
              <!--                    </template>-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column min-width="100"-->
              <!--                                   :label="$t('writeFreq.zones.parent')">-->
              <!--                    <template slot-scope="scope">-->
              <!--                      <el-form-item label-width="0">-->
              <!--                        <el-select v-model="scope.row.parentId"-->
              <!--                                   -->
              <!--                                   :placeholder="$t('dialog.select')"-->
              <!--                                   filterable-->
              <!--                                   :no-match-text="$t('dialog.noMatchText')">-->
              <!--                          <el-option v-for="(item,i) in getButtonDefineZoneParentList(scope.row)"-->
              <!--                                     :key="i"-->
              <!--                                     :label="item.label"-->
              <!--                                     :value="item.value"></el-option>-->
              <!--                        </el-select>-->
              <!--                      </el-form-item>-->
              <!--                    </template>-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column min-width="100"-->
              <!--                                   :label="$t('writeFreq.zones.leaf')">-->
              <!--                    <template slot-scope="scope">-->
              <!--                      <el-form-item-->
              <!--                        label-width="0">-->
              <!--                        <el-select v-model="scope.row.zoneId"-->
              <!--                                   -->
              <!--                                   :placeholder="$t('dialog.select')"-->
              <!--                                   filterable-->
              <!--                                   :no-match-text="$t('dialog.noMatchText')">-->
              <!--                          <el-option v-for="(item,i)  in getButtonDefineZoneLeafList(scope.row)"-->
              <!--                                     :key="i"-->
              <!--                                     :label="item.label"-->
              <!--                                     :value="item.value"></el-option>-->
              <!--                        </el-select>-->
              <!--                      </el-form-item>-->
              <!--                    </template>-->
              <!--                  </el-table-column>-->
              <!--                  <el-table-column min-width="100"-->
              <!--                                   :label="$t('dialog.channel')">-->
              <!--                    <template slot-scope="scope">-->
              <!--                      <el-form-item-->
              <!--                        label-width="0">-->
              <!--                        <el-select v-model="scope.row.channelId"-->
              <!--                                   -->
              <!--                                   :placeholder="$t('dialog.select')"-->
              <!--                                   filterable-->
              <!--                                   :no-match-text="$t('dialog.noMatchText')">-->
              <!--                          <el-option v-for="(item,i)  in getButtonDefineChannelList(scope.row)"-->
              <!--                                     :key="i"-->
              <!--                                     :label="item.label"-->
              <!--                                     :value="item.value"></el-option>-->
              <!--                        </el-select>-->
              <!--                      </el-form-item>-->
              <!--                    </template>-->
              <!--                  </el-table-column>-->
              <!--                </el-table>-->
              <!--              </el-col>-->
            </div>
          </el-form>
        </el-card>
        <el-card
          v-if="showShortMessage"
          shadow="never"
          class="write-freq-component short-message-container"
        >
          <shortMessage
            :ref="refSms"
            v-model="smsContent"
            :maxSize="smsMaxSize"
          />
        </el-card>
        <el-card
          v-if="showEncryptSettings"
          shadow="never"
          class="write-freq-component encrypt-settings-container"
        >
          <section class="encrypt-settings-form">
            <el-row
              :gutter="20"
              class="no-margin-x"
              type="flex"
              align="middle"
            >
              <el-col
                :xs="12"
                :sm="12"
              >
                <el-checkbox v-model="encryptConfig.config.encryptEnable">
                  <span v-text="$t('dialog.enable')" />
                </el-checkbox>
              </el-col>
              <el-col
                :xs="12"
                :sm="12"
              >
                <el-button
                  type="primary"
                  :disabled="!encryptEnable || encryptList.length >= encryptListLimit"
                  @click="addEncryptItem"
                  v-text="$t('dialog.add')"
                />
              </el-col>
            </el-row>
            <el-table
              :data="encryptList"
              :empty-text="$t('msgbox.emptyText')"
              :highlight-current-row="encryptEnable"
              :row-class-name="encryptEnable ? 'encrypt-enable-row' : ' '"
            >
              <el-table-column
                label="#"
                type="index"
              />
              <el-table-column
                :label="$t('writeFreq.secretKeyName')"
                min-width="100"
                sortable
                :sort-method="secretKeyNameSortMethod"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.keyName"
                    :maxlength="16"
                    :disabled="!encryptEnable"
                    @change="() => { encryptKeyNameChanged(scope.row) }"
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('writeFreq.secretKeyValue')"
                min-width="100"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.keyValue"
                    type="password"
                    :maxlength="10"
                    :disabled="!encryptEnable"
                    @input="() => { encryptKeyValueInput(scope.row) }"
                    @change="() => { encryptKeyValueChanged(scope.row) }"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="#"
                :width="encryptListActiveWidth"
              >
                <template #default="scope">
                  <el-button
                    type="danger"
                    :disabled="!encryptEnable || encryptList.length <= 1"
                    @click="deleteEncryptItem(scope.row, scope.$index)"
                    v-text="$t('dialog.delete')"
                  />
                </template>
              </el-table-column>
            </el-table>
          </section>
        </el-card>
        <el-card
          v-if="showMenuSettings"
          shadow="never"
          class="write-freq-component menu-settings-container"
        >
          <el-form
            ref="menuSettings"
            class="menu-settings-form"
            :model="menuSettings"
            label-position="top"
          >
            <div class="grid grid-cols-2 mdx:grid-cols-1">
              <div class="md:mr-4">
                <el-form-item
                  :label="$t('dialog.menuHangTime')"
                  :label-width="menuHangTimeLabelWidth"
                >
                  <el-input-number
                    v-model="menuSettings.hangTime"
                    step-strictly
                    :min="0"
                    :max="30"
                    :step="1"
                  />
                </el-form-item>
              </div>
              <div class="md:mr-4">
                <el-form-item
                  :label="$t('dialog.chDisplayMode')"
                  :label-width="menuHangTimeLabelWidth"
                >
                  <el-select
                    v-model="menuSettings.baseSetting.chDisplayMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in freqDisplayList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div>
                <el-form-item>
                  <el-checkbox v-model="menuSettings.baseSetting.menuOff">
                    <span v-text="$t('dialog.closeMenuButton')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div>
                <el-form-item>
                  <el-checkbox v-model="menuSettings.baseSetting.showAlias">
                    <span v-text="$t('dialog.displaysCallIdAndAlias')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div>
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsConfig.enable">
                    <span v-text="$t('dialog.sms')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div>
                <el-form-item>
                  <el-checkbox v-model="menuSettings.zoneConfig.enable">
                    <span v-text="$t('writeFreq.zone')" />
                  </el-checkbox>
                </el-form-item>
              </div>
            </div>
            <div>
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.addressBook')" />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div>
                  <el-form-item>
                    <el-checkbox v-model="menuSettings.contactConfig.contacts">
                      <span v-text="$t('dialog.addressBook')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.contactConfig.contactList"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.contactList')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.contactConfig.editEnable"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('writeFreq.edit')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.contactConfig.manualDialing"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.manualDialing')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.contactConfig.privateCallDialing"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.singleCallDialing')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.contactConfig.groupCallDialing"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.groupCallDialing')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.contactConfig.newContact"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.newContact')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.contactConfig.callPrompting"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.callReminder')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceControl.deviceDetect"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.deviceDetect')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceControl.remoteMonitor"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.remoteMonitor')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceControl.deviceActive"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.deviceActive')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceControl.deviceRemoteDeath"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('dialog.deviceRemoteDeath')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceControl.deviceRemoteDestroy"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('writeFreq.deviceRemoteDestroy')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceControl.deviceStun"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('writeFreq.deviceStun')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceControl.deviceWokeUp"
                      :disabled="contactsDisabled"
                    >
                      <span v-text="$t('writeFreq.deviceWokeUp')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>
            </div>

            <div>
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.phoneBook')" />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div>
                  <el-form-item>
                    <el-checkbox v-model="menuSettings.phoneConfig.enable">
                      <span v-text="$t('dialog.phoneBook')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.phoneConfig.list"
                      :disabled="phoneConfigDisabled"
                    >
                      <span v-text="$t('dialog.contactList')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.phoneConfig.manualDialing"
                      :disabled="phoneConfigDisabled"
                    >
                      <span v-text="$t('dialog.manualDialing')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>
            </div>

            <div>
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.scan')" />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div>
                  <el-form-item>
                    <el-checkbox v-model="menuSettings.scanConfig.enable">
                      <span v-text="$t('dialog.scanEnable')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.scanConfig.editList"
                      :disabled="scanConfigDisabled"
                    >
                      <span v-text="$t('dialog.editScanList')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>
            </div>

            <div>
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.roaming')" />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div>
                  <el-form-item>
                    <el-checkbox v-model="menuSettings.scanConfig.roamEnable">
                      <span v-text="$t('writeFreq.roamEnable')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.scanConfig.roamLockSite"
                      :disabled="roamConfigDisabled"
                    >
                      <span v-text="$t('writeFreq.roamLockSite')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.scanConfig.roamManual"
                      :disabled="roamConfigDisabled"
                    >
                      <span v-text="$t('writeFreq.roamManual')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>
            </div>

            <div>
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.callRecord')" />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div>
                  <el-form-item>
                    <el-checkbox v-model="menuSettings.callConfig.callHistory">
                      <span v-text="$t('dialog.callRecord')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.callConfig.missedCall"
                      :disabled="callConfigDisabled"
                    >
                      <span v-text="$t('dialog.missedCall')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.callConfig.receivedCall"
                      :disabled="callConfigDisabled"
                    >
                      <span v-text="$t('dialog.answeredCall')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.callConfig.outgoingRecord"
                      :disabled="callConfigDisabled"
                    >
                      <span v-text="$t('dialog.outgoingCall')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>
            </div>

            <div>
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.interphoneConfig')" />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div>
                  <el-form-item>
                    <el-checkbox v-model="menuSettings.deviceConfig.setting">
                      <span v-text="$t('dialog.configure')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceInfo.info"
                      :disabled="deviceConfigUnable"
                    >
                      <span v-text="$t('writeFreq.interphoneInfo')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig.deviceSetting"
                      :disabled="deviceConfigUnable"
                    >
                      <span v-text="$t('writeFreq.interphoneConfig')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig.offline"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.offNetwork')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig.toneTip"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.toneOrTip')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig.transmitPower"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.txPower')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <!-- <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="menuSettings.deviceConfig.backLight"
                                 :disabled='deviceConfigSettingUnable'>
                      <span v-text="$t('dialog.backlight')"></span>
                    </el-checkbox>
                  </el-form-item>
                </el-col> -->
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig.bootInterface"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.bootInterface')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig.keyboardLock"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.keyboardLock')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig2.ledIndicator"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.ledIndicator')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig2.quieting"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.Quieting')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <!--              <el-col :xs="24" :sm="12">-->
                <!--                <el-form-item>-->
                <!--                  <el-checkbox v-model="menuSettings.deviceConfig.powerOnPassword"-->
                <!--                               disabled>-->
                <!--                    <span v-text="$t('dialog.powerOnPwd')"></span>-->
                <!--                  </el-checkbox>-->
                <!--                </el-form-item>-->
                <!--              </el-col>-->
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig2.locale"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('writeFreq.langEnv')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.deviceConfig2.soundCtrl"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('dialog.voiceControl')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.recordConfig.maydayRescue"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('writeFreq.maydayRescue')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item>
                    <el-checkbox
                      v-model="menuSettings.recordConfig.stealthMode"
                      :disabled="deviceConfigSettingUnable"
                    >
                      <span v-text="$t('writeFreq.stealthMode')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>

              <!--              <el-col :xs="24" :sm="12">-->
              <!--                <el-form-item>-->
              <!--                  <el-checkbox v-model="menuSettings.deviceConfig2.timeSetting">-->
              <!--                    <span v-text="$t('dialog.timeSetting')"></span>-->
              <!--                  </el-checkbox>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
            </div>
            <!--            <el-row :gutter='20' class='no-margin-x'-->
            <!--                    type='flex' align='middle'>-->
            <!--              <el-divider>-->
            <!--                <el-icon><CaretBottom /></el-icon>-->
            <!--                <span v-text='$t("writeFreq.channelConfig")'></span>-->
            <!--              </el-divider>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.chConfigEnable">-->
            <!--                    <span v-text="$t('dialog.chConfigSwitch')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.receivingFrequency">-->
            <!--                    <span v-text="$t('dialog.receiveFrequency')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.transmittingFrequency">-->
            <!--                    <span v-text="$t('dialog.transFrequency')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.channelName">-->
            <!--                    <span v-text="$t('dialog.chName')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.roamInterface">-->
            <!--                    <span v-text="$t('dialog.transTimeLimit')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.subAudioSetting">-->
            <!--                    <span v-text="$t('dialog.subaudioSetting')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.launchContact">-->
            <!--                    <span v-text="$t('dialog.launchContact')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.channelSetting.colorCode">-->
            <!--                    <span v-text="$t('dialog.colorCodes')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.timeSlotSetting.timeSlot">-->
            <!--                    <span v-text="$t('dialog.timeSlots')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.timeSlotSetting.virtualClusterTimeSlot">-->
            <!--                    <span v-text="$t('dialog.virtualTimeSlot')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--              <el-col :xs="24" :sm="12">-->
            <!--                <el-form-item>-->
            <!--                  <el-checkbox v-model="menuSettings.timeSlotSetting.receivingList">-->
            <!--                    <span v-text="$t('dialog.receivingList')"></span>-->
            <!--                  </el-checkbox>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
            <!--            </el-row>-->
          </el-form>
        </el-card>
        <el-card
          v-if="showSignalingSystem"
          shadow="never"
          class="write-freq-component signaling-system-container"
        >
          <el-form
            ref="signalingSystem"
            class="signaling-system-form"
            :model="signalingSystem"
            label-position="top"
            :rules="signalingSystemRules"
          >
            <div class="grid grid-cols-2 mdx:grid-cols-1">
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteConfig.remoteDeathDecode">
                  <span v-text="$t('writeFreq.deviceRemoteDeadDecode')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteConfig.remoteMonitorDecode">
                  <span v-text="$t('writeFreq.remoteMonitorDecode')" />
                </el-checkbox>
              </el-form-item>
              <!--              <el-col :xs="24" :sm='12'>-->
              <!--                <el-form-item>-->
              <!--                  <el-checkbox v-model="signalingSystem.remoteConfig.urgentRemoteMonitorDecode">-->
              <!--                    <span v-text="$t('writeFreq.urgentRemoteMonitorDecode')"></span>-->
              <!--                  </el-checkbox>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteConfig.remoteNoticeDecode">
                  <span v-text="$t('writeFreq.remotePromptDecode')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteConfig.remoteDetectDecode">
                  <span v-text="$t('writeFreq.remoteDetectionDecode')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteConfig.remoteEraseDecode">
                  <span v-text="$t('writeFreq.remoteDestructionDecode')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteConfig.remoteStunWakeupDecode">
                  <span v-text="$t('writeFreq.remoteStunWakeupDecode')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item
                class="col-span-2 mr-4"
                :label="$t('writeFreq.remoteMonitorDuration')"
              >
                <el-input-number
                  v-model="signalingSystem.remoteMonitorDuration"
                  step-strictly
                  :min="10"
                  :max="120"
                  :step="10"
                />
              </el-form-item>
              <!--              <el-col :xs="24" :sm='12'>-->
              <!--                <el-form-item :label="$t('writeFreq.signalingPassword')"-->
              <!--                              prop='signalingPwd'>-->
              <!--                  <el-input v-model="signalingSystem.signalingPwd"-->
              <!--                            type="password" :maxlength='6'>-->
              <!--                  </el-input>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
            </div>
          </el-form>
        </el-card>
        <el-card
          v-if="showAlertConfig"
          shadow="never"
          class="write-freq-component signaling-system-container"
        >
          <el-form
            ref="alertConfig"
            class="signaling-system-form"
            :model="alertConfig"
            label-position="top"
          >
            <div>
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.workAlone')" />
              </el-divider>
              <div class="grid grid-cols-1">
                <el-form-item>
                  <el-checkbox v-model="alertConfig.aloneWorkEnable">
                    <span v-text="$t('writeFreq.workAlone')" />
                  </el-checkbox>
                </el-form-item>
                <el-form-item :label="$t('writeFreq.workResTimeAlone')">
                  <el-input-number
                    v-model="alertConfig.aloneWorkTime"
                    :disabled="workAloneUnEnable"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
                <el-form-item :label="$t('writeFreq.workAloneReminderTime')">
                  <el-input-number
                    v-model="alertConfig.aloneWorkRemindTime"
                    :disabled="workAloneUnEnable"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
                <el-form-item :label="$t('writeFreq.workResOptAlone')">
                  <el-select
                    v-model="alertConfig.aloneWorkResOpt"
                    :placeholder="$t('dialog.select')"
                    :disabled="workAloneUnEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in aloneWorkOptList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card
          v-show="showDigitalAlert"
          shadow="never"
          class="write-freq-component digital-alert-container"
        >
          <digitalAlert
            ref="digitalAlert"
            v-model="digitalAlertList"
            v-model:alertId="currDigitalAlertId"
            :channelList="channelDataList"
            :limit="digitalAlertListLimit"
            @init-data="loadDigitalAlertNode"
            @name-change="updateDigitalAlertNode"
          />
        </el-card>
        <el-card
          v-show="showDigitalAddress"
          shadow="never"
          class="write-freq-component address-book-container"
        >
          <addressBook
            :ref="addrBookTreeId"
            class="address-book"
            :treeId="addrBookTreeId"
            :maxSize="1600"
            :redrawTree="showDigitalAddress"
            :callTypes="addressBookCallTypes"
            noSysSelect
            @select="selectAddressBooks"
          />
        </el-card>
        <el-card
          v-show="showAddressGroup"
          shadow="never"
          class="write-freq-component address-group-container"
        >
          <addressBookGroup
            ref="addressBookGroup"
            v-model="addressBookGroup"
            v-model:addressBook="selectedAddressBook"
            v-model:dataId="currAddressBookGroupId"
            :limit="addressBookGroupLimit"
            @name-change="updateAddressBookGroupNode"
            @init-data="loadAddressBookGroupNode"
          />
        </el-card>
        <el-card
          v-show="showPhoneBook"
          shadow="never"
          class="write-freq-component phone-book-container"
        >
          <phoneBook
            :ref="phoneBookTreeId"
            class="phone-book"
            :treeId="phoneBookTreeId"
            :redrawTree="showPhoneBook"
            @select="selectPhoneBooks"
          />
        </el-card>
        <el-card
          v-show="showRxGroup"
          shadow="never"
          class="write-freq-component receive-group-container"
        >
          <receiveGroup
            :ref="refReceiveGroup"
            v-model="rxGroupList"
            :channels="selectedChannels"
            :addressTreeId="addrBookTreeId"
            :getDefaultAddress="getDefaultAddress"
            :getAddressName="getAddressNameByDmrId"
            :getOriginAddress="getOriginAddressBook"
          />
        </el-card>
        <el-card
          v-show="showZoneRootData"
          shadow="never"
          class="write-freq-component channel-container"
        >
          <multistageZone
            ref="rootZone"
            v-model="zoneRootDataList"
            v-model:dataId="currZoneRootId"
            :limit="zoneRootLimit"
            :level="1"
            @row-dblclick="zoneRootDataDblclick"
          />
        </el-card>
        <el-card
          v-show="showZoneParentData"
          shadow="never"
          class="write-freq-component channel-container"
        >
          <multistageZone
            ref="parentZone"
            v-model="zoneParentDataList"
            v-model:dataId="currZoneParentId"
            :limit="zoneParentLimit"
            :parentZone="zoneRootDataIndex[currZoneRootId]"
            :level="2"
            @row-dblclick="zoneParentDataDblclick"
          />
        </el-card>
        <el-card
          v-show="showZoneLeafData"
          shadow="never"
          class="write-freq-component channel-container"
        >
          <multistageZone
            ref="leafZone"
            v-model="zoneLeafDataList"
            v-model:dataId="currZoneLeafId"
            :limit="zoneLeafLimit"
            :parentZone="zoneParentDataIndex[currZoneParentId]"
            :level="3"
            @row-dblclick="zoneLeafDataDblclick"
          />
        </el-card>
        <el-card
          v-show="showZoneLeafChannels"
          shadow="never"
          class="write-freq-component channel-container"
        >
          <zoneLeafTable
            ref="zoneLeafChannel"
            v-model="channelDataList"
            :limit="oneZoneLeafChannelLimit"
            :parentZone="zoneLeafDataIndex[currZoneLeafId]"
            :getDefaultChannel="getDefaultChannel"
            @row-dblclick="zoneLeafDataRowDblclick"
          />
        </el-card>
        <el-card
          v-show="showChannelItem"
          shadow="never"
          class="write-freq-component channel-container"
        >
          <el-form
            ref="channelSetting"
            class="channel-setting-form"
            :model="oneChannel"
            :rules="channelRules"
            label-position="top"
          >
            <div class="grid grid-cols-2 mdx:grid-cols-1">
              <div class="mr-4">
                <el-form-item
                  :label="$t('dialog.chName')"
                  prop="chName"
                >
                  <el-input
                    v-model="oneChannel.chName"
                    :maxlength="16"
                    @change="chNameChanged"
                  />
                </el-form-item>
              </div>
              <div class="mr-4">
                <el-form-item :label="$t('dialog.chType')">
                  <el-select
                    v-model="oneChannel.chType"
                    @change="chTypeChanged"
                  >
                    <el-option
                      v-for="(item, i) in chTypeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div
                v-if="isAChannel"
                class="mr-4"
              >
                <el-form-item :label="$t('writeFreq.broadBand')">
                  <el-select v-model="oneChannel.subChannelData.funcConfig.bandwidthFlag">
                    <el-option
                      v-for="(item, i) in bandwidthFlagList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div class="mr-4">
                <el-form-item :label="$t('writeFreq.scanList')">
                  <el-select
                    v-model="oneChannel.scanList"
                    :disabled="isConnectNetworking"
                  >
                    <el-option
                      v-for="(item, i) in chScanRoamGroupList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div class="mt-8">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.scanConfig.autoScan"
                    :disabled="disAutoScan"
                  >
                    <span v-text="$t('writeFreq.autoScanning')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div
                v-if="isDChannel"
                class="mt-8"
              >
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.scanConfig.autoRoam"
                    :disabled="disAutoRoam"
                  >
                    <span v-text="$t('writeFreq.autoRoaming')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div
                v-if="!isDChannel"
                class="mr-4"
              >
                <el-form-item :label="$t('dialog.squelchLevel')">
                  <el-input-number
                    v-model="oneChannel.powerConfig.squelchLevel"
                    step-strictly
                    :min="0"
                    :max="9"
                    :step="1"
                  />
                </el-form-item>
              </div>
              <!--              <el-col :xs="24" :sm="12" v-if='isAChannel'>-->
              <!--                <el-form-item :label="$t('writeFreq.emphasis')">-->
              <!--                  <el-select v-model="oneChannel.subChannelData.funcConfig.preEmphasis">-->
              <!--                    <el-option v-for="(item,i) in preEmphasisList"-->
              <!--                               :key="i"-->
              <!--                               :label="item.label"-->
              <!--                               :value="item.value">-->
              <!--                    </el-option>-->
              <!--                  </el-select>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <!--              <el-col :xs="24" :sm="12" v-if='isAChannel'>-->
              <!--                <el-form-item :label="$t('writeFreq.scramble')" class='scramble-form-item'>-->
              <!--                  <el-checkbox v-model="oneChannel.subChannelData.funcConfig.scrambleEnable">-->
              <!--                  </el-checkbox>-->
              <!--                  <el-input-number-->
              <!--                    v-model="oneChannel.subChannelData.scramble"-->
              <!--                    :disabled='!oneChannel.subChannelData.funcConfig.scrambleEnable'-->
              <!--                    step-strictly-->
              <!--                    :min="0" :max="6000" :step="1">-->
              <!--                  </el-input-number>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <!--              <el-col :xs="24" :sm="12" v-if='isAChannel'>-->
              <!--                <el-form-item>-->
              <!--                  <el-checkbox v-model="oneChannel.subChannelData.funcConfig.companding">-->
              <!--                    <span v-text="$t('writeFreq.companding')"></span>-->
              <!--                  </el-checkbox>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <div
                v-if="!isAChannel"
                class="mr-4"
              >
                <el-form-item :label="$t('dialog.colorCodes')">
                  <el-input-number
                    v-model="oneChannel.subChannelData.colorCode"
                    step-strictly
                    :min="0"
                    :max="15"
                    :step="1"
                  />
                </el-form-item>
              </div>
              <div>
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.scanConfig.allowOfflineSign"
                    :disabled="sameFreq"
                  >
                    <span v-text="$t('dialog.allowOffNetwork')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div>
                <el-form-item>
                  <el-checkbox v-model="oneChannel.scanConfig.onlyReceive">
                    <span v-text="$t('dialog.receiveOnly')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div v-if="isDChannel">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.scanConfig.ipSiteConnect"
                    :disabled="sameFreq || oneChannel.scanConfig.onlyReceive"
                  >
                    <span v-text="$t('writeFreq.ipSiteConnection')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <!--              <el-col :xs="24" :sm="12" v-if='isAChannel'>-->
              <!--                <el-form-item>-->
              <!--                  <el-checkbox v-model="oneChannel.subChannelData.funcConfig.beatFreq">-->
              <!--                    <span v-text="$t('writeFreq.beatFreq')"></span>-->
              <!--                  </el-checkbox>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <div v-if="!isAChannel">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.subChannelData.timeSlotConfig.priorityInterrupt">
                    <span v-text="$t('writeFreq.priorityInterrupt')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div v-if="isDChannel">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.subChannelData.networkConfig.networking">
                    <span v-text="$t('writeFreq.networking')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div v-if="isDChannel">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.subChannelData.networkConfig.localCall"
                    :disabled="!oneChannel.subChannelData.networkConfig.networking"
                  >
                    <span v-text="$t('writeFreq.localCall')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div v-if="!isAChannel">
                <el-form-item>
                  <el-checkbox
                    v-model="oneChannel.subChannelData.timeSlotConfig.throughEnable"
                    :disabled="!sameFreq"
                  >
                    <span v-text="$t('writeFreq.TDMAThroughMode')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div v-if="!isAChannel">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.scanConfig.directMode">
                    <span v-text="$t('dialog.passThroughMode')" />
                  </el-checkbox>
                </el-form-item>
              </div>
              <div
                v-if="!isAChannel"
                class="mr-4"
              >
                <el-form-item :label="$t('writeFreq.chTimeSlotCalibrator')">
                  <el-select
                    v-model="oneChannel.subChannelData.timeSlotConfig.chSlotAdjust"
                    :disabled="!throughEnable"
                  >
                    <el-option
                      v-for="(item, i) in chTimeSlotCalList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div v-if="!isAChannel">
                <el-form-item :label="$t('dialog.slotMode')">
                  <el-select
                    v-model="oneChannel.subChannelData.timeSlotConfig.timeSlot"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in slotModeList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div
                v-if="!isAChannel"
                class="mr-4"
              >
                <el-form-item :label="$t('dialog.virtualTimeSlot')">
                  <el-select
                    v-model="oneChannel.subChannelData.timeSlotConfig.virtualTimeSlot"
                    :placeholder="$t('dialog.select')"
                    :disabled="oneChannel.subChannelData.timeSlotConfig.timeSlot !== 2"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option
                      v-for="(item, i) in virtualTimeSlotList"
                      :key="i"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div
                v-if="!isAChannel"
                class="mr-4"
              >
                <el-form-item :label="$t('writeFreq.voicePriority')">
                  <el-input-number
                    v-model="oneChannel.subChannelData.alertConfig.voicePriority"
                    step-strictly
                    :min="0"
                    :max="3"
                    :step="1"
                  />
                </el-form-item>
              </div>
            </div>
            <div v-if="isDChannel">
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span
                  class="divider-label"
                  v-text="$t('writeFreq.encryption')"
                />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div class="mt-8">
                  <el-form-item>
                    <el-checkbox
                      v-model="oneChannel.subChannelData.encryptConfig.enable"
                      :disabled="disEncryptConfigEnable"
                    >
                      <span v-text="$t('dialog.enable')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div class="mr-4">
                  <el-form-item :label="$t('writeFreq.encryptionAlgorithm')">
                    <el-select
                      v-model="oneChannel.subChannelData.encryptConfig.algorithm"
                      :placeholder="$t('dialog.select')"
                      :disabled="disEncryption"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option
                        v-for="(item, i) in algorithmList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div class="mr-4">
                  <el-form-item :label="$t('writeFreq.keyList')">
                    <el-select
                      v-model="oneChannel.subChannelData.encryptKeyList"
                      :placeholder="$t('dialog.select')"
                      :disabled="disEncryption"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option
                        v-for="(item, i) in encryptKeyList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
            </div>
            <div>
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span
                  class="divider-label"
                  v-text="$t('dialog.receive')"
                />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div class="mr-4">
                  <el-form-item
                    :label="$t('dialog.rxFrequency')"
                    prop="rxFreq"
                  >
                    <frequencyMhz
                      v-model="oneChannel.rxFreq"
                      :maxlength="9"
                    />
                  </el-form-item>
                </div>
                <div>
                  <freqMapOffset
                    v-model="freqOffset"
                    v-model:dstFreq="oneChannel.txFreq"
                    :srcFreq="oneChannel.rxFreq"
                    :freqRange="deviceWriteInfo.frequencyRange"
                  />
                </div>
                <div
                  v-if="!isDChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('writeFreq.decoding')">
                    <el-select
                      v-model="oneChannel.subChannelData.subsonicDecode"
                      filterable
                      :allow-create="isAChannel"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                      @change="subsonicDecodeChange"
                    >
                      <el-option
                        v-for="(item, i) in subtoneCodeDataList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isDChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('dialog.tailSelection')">
                    <el-select
                      v-model="oneChannel.subChannelData.funcConfig.ctcssRxDps"
                      :disabled="oneChannel.subChannelData.subsonicDecode === 0xFFFF"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option
                        v-for="(item, i) in tailToneList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('dialog.receiveGroupList')">
                    <el-select
                      v-model="oneChannel.subChannelData.receiveGroup"
                      disabled
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option
                        v-for="(item, i) in receiveGroupList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mt-8"
                >
                  <el-form-item>
                    <el-checkbox
                      v-model="oneChannel.subChannelData.alertConfig.emergencyAlertTip"
                      :disabled="isConnectNetworking"
                    >
                      <span v-text="$t('writeFreq.emergencyAlarmIndication')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mt-8"
                >
                  <el-form-item>
                    <el-checkbox
                      v-model="oneChannel.subChannelData.alertConfig.emergencyAlertConfirm"
                      :disabled="!oneChannel.subChannelData.alertConfig.emergencyAlertTip"
                    >
                      <span v-text="$t('writeFreq.emergencyAlarmConfirm')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mt-8"
                >
                  <el-form-item>
                    <el-checkbox
                      v-model="oneChannel.subChannelData.alertConfig.emergencyCallTip"
                      :disabled="isConnectNetworking"
                    >
                      <span v-text="$t('writeFreq.emergencyCallAlert')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>
            </div>
            <div>
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span
                  class="divider-label"
                  v-text="$t('dialog.emission')"
                />
              </el-divider>
              <div class="grid grid-cols-2 mdx:grid-cols-1">
                <div class="mr-4">
                  <el-form-item
                    :label="$t('dialog.txFrequency')"
                    prop="txFreq"
                  >
                    <frequencyMhz
                      v-model="oneChannel.txFreq"
                      :maxlength="9"
                      :disabled="onlyReceive"
                    />
                  </el-form-item>
                </div>
                <div
                  v-if="!isDChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('writeFreq.encoding')">
                    <el-select
                      v-model="oneChannel.subChannelData.subsonicEncode"
                      filterable
                      :allow-create="isAChannel"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                      :disabled="onlyReceive"
                      @change="subsonicEncodeChange"
                    >
                      <el-option
                        v-for="(item, i) in subtoneCodeDataList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isDChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('dialog.plosive')">
                    <el-select
                      v-model="oneChannel.subChannelData.funcConfig.ctcssTxDps"
                      :disabled="disPlosive"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option
                        v-for="(item, i) in tailToneList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('dialog.sendGroup')">
                    <el-select
                      v-model="oneChannel.subChannelData.defaultAddress"
                      disabled
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option
                        v-for="(item, i) in defaultAddressList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('writeFreq.emergencySystem')">
                    <el-select
                      v-model="oneChannel.subChannelData.emergencySysId"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                      :disabled="isConnectNetworking || onlyReceive"
                    >
                      <el-option
                        v-for="(item, i) in emergencySysIdList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div class="mr-4">
                  <el-form-item :label="$t('dialog.txPower')">
                    <el-select
                      v-model="oneChannel.powerConfig.powerType"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                      :disabled="onlyReceive"
                    >
                      <el-option
                        v-for="(item, i) in txPowerTypes"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div class="mr-4">
                  <el-form-item :label="$t('dialog.sendTimeLimiter')">
                    <el-input-number
                      v-model="oneChannel.transmissionLimit"
                      step-strictly
                      :min="15"
                      :max="495"
                      :step="15"
                      :disabled="onlyReceive"
                    />
                  </el-form-item>
                </div>
                <div class="mr-4">
                  <el-form-item :label="$t('dialog.totPwdUpdateDelay')">
                    <el-input-number
                      v-model="oneChannel.totKeyUpdateDelay"
                      step-strictly
                      :min="0"
                      :max="255"
                      :step="5"
                      :disabled="onlyReceive"
                    />
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('dialog.permissionConditions')">
                    <el-select
                      v-model="oneChannel.subChannelData.alertConfig.permitConditions"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                      :disabled="onlyReceive"
                    >
                      <el-option
                        v-for="(item, i) in permissionConditionsList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isAChannel"
                  class="mt-8"
                >
                  <el-form-item>
                    <el-checkbox
                      v-model="oneChannel.subChannelData.alertConfig.singleCallRes"
                      :disabled="onlyReceive"
                    >
                      <span v-text="$t('dialog.singleCallConfirm')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div
                  v-if="!isDChannel"
                  class="mr-4"
                >
                  <el-form-item :label="$t('writeFreq.busyChannelLock')">
                    <el-select
                      v-model="oneChannel.subChannelData.funcConfig.bclFlag"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                      :disabled="onlyReceive"
                    >
                      <el-option
                        v-for="(item, i) in busyChannelLockList"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div
                  v-if="!isDChannel"
                  class="mt-8"
                >
                  <el-form-item>
                    <el-checkbox
                      v-model="oneChannel.subChannelData.funcConfig.tailCancellation"
                      :disabled="onlyReceive"
                    >
                      <span v-text="$t('dialog.tailCancellation')" />
                    </el-checkbox>
                  </el-form-item>
                </div>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card
          v-show="showScan"
          shadow="never"
          class="write-freq-component scan-container"
        >
          <scanGroup
            ref="scanGroup"
            v-model="scanGroup"
            v-model:dataId="currScanGroupId"
            :channels="channelDataList"
            :limit="scanGroupLimit"
            :is-fullscreen="isFullscreen"
            @name-change="updateScanGroupNode"
            @init-data="loadScanGroupNode"
          />
        </el-card>
        <el-card
          v-show="showRoam"
          shadow="never"
          class="write-freq-component roam-container"
        >
          <roamGroup
            ref="roamGroup"
            v-model="roamGroup"
            v-model:dataId="currRoamGroupId"
            :channels="channelDataList"
            :limit="roamGroupLimit"
            :directMode="directMode"
            :currChannel="oneChannel"
            :is-fullscreen="isFullscreen"
            @name-change="updateRoamGroupNode"
            @init-data="loadRoamGroupNode"
          />
        </el-card>
        <el-tabs
          v-if="showPatrolSystem"
          v-model="patrolSystemTabsValue"
          type="border-card"
          class="write-freq-component patrol-system-container"
        >
          <el-tab-pane
            :label="$t('dialog.configure')"
            name="configure"
            class="h-full"
          >
            <patrolConfig
              ref="patrolConfig"
              v-model="patrolConfig"
            />
          </el-tab-pane>
          <el-tab-pane
            :label="$t('dialog.emergency')"
            name="emergency"
            class="h-full"
          >
            <emergencyAlarmConfig
              ref="emergencyAlarm"
              v-model="emergencyAlarm"
              :addressBooks="selectedAddressBook"
              :showAutoTrackTime="false"
            />
          </el-tab-pane>
        </el-tabs>
        <el-card
          v-if="showSosSettings"
          shadow="never"
          class="write-freq-component sos-settings-container"
        >
          <el-form
            ref="sosSettings"
            class="sos-settings-form"
            :model="sosCfg"
            label-width="120px"
            label-position="top"
          >
            <el-form-item label-width="0">
              <el-checkbox v-model="sosCfg.config.enable">
                <span v-text="$t('writeFreq.sosRescue')" />
              </el-checkbox>
            </el-form-item>
            <el-form-item
              :label="$t('writeFreq.intervalHonkingTime')"
              class="tone-volume-form-item"
            >
              <el-checkbox
                v-model="sosCfg.config.intervalHonking"
                :disabled="!sosEnable"
              />
              <el-input-number
                v-model="intervalHonkingTime"
                step-strictly
                :min="5"
                :max="60"
                :step="5"
                :disabled="!sosEnable || !sosCfg.config.intervalHonking"
                class="grow"
              />
            </el-form-item>
            <el-form-item
              :label="$t('writeFreq.flashlightTime')"
              class="tone-volume-form-item"
            >
              <el-checkbox
                v-model="sosCfg.config.flashlight"
                :disabled="!sosEnable"
              />
              <el-input-number
                v-model="flashlightTime"
                step-strictly
                :min="500"
                :max="2000"
                :step="100"
                :disabled="!sosEnable || !sosCfg.config.flashlight"
                class="grow"
              />
            </el-form-item>
            <el-form-item
              :label="$t('writeFreq.rescueScanHandUpTime')"
              class="tone-volume-form-item"
            >
              <el-input-number
                v-model="sosCfg.rescueScanHandUpTime"
                step-strictly
                :min="0"
                :max="10000"
                :step="500"
              />
            </el-form-item>
            <el-form-item
              :label="$t('writeFreq.highPowerSosTime')"
              class="tone-volume-form-item"
            >
              <el-input-number
                v-model="highPowerSosTime"
                step-strictly
                :min="3"
                :max="10"
                :step="1"
                :disabled="!sosEnable"
              />
            </el-form-item>
            <el-form-item
              :label="$t('writeFreq.middlePowerSosTime')"
              class="tone-volume-form-item"
            >
              <el-input-number
                v-model="middlePowerSosTime"
                step-strictly
                :min="5"
                :max="15"
                :step="1"
                :disabled="!sosEnable"
              />
            </el-form-item>
            <el-form-item
              :label="$t('writeFreq.lowPowerSosTime')"
              class="tone-volume-form-item"
            >
              <el-input-number
                v-model="lowPowerSosTime"
                step-strictly
                :min="10"
                :max="30"
                :step="1"
                :disabled="!sosEnable"
              />
            </el-form-item>
            <el-form-item
              :label="$t('writeFreq.sosInfo')"
              class="tone-volume-form-item"
            >
              <el-input
                v-model="sosCfg.sosInfo"
                type="textarea"
                :rows="3"
                resize="none"
                :maxlength="24"
                show-word-limit
                :disabled="!sosEnable"
              />
            </el-form-item>
          </el-form>
        </el-card>
        <!--        <el-card v-show='showSosChannel'-->
        <!--                 shadow="never"-->
        <!--                 class='write-freq-component sos-channel-container'>-->
        <!--          <el-form class='sos-channel-setting-form'-->
        <!--                   :model="sosChannel"-->
        <!--                   :rules="channelRules"-->
        <!--                   :disabled="notSettable"-->
        <!--                   ref='sosChannelSetting'-->
        <!--                   -->
        <!--                   label-position="top">-->
        <!--            <el-row :gutter='20' class='no-margin-x'-->
        <!--                    type='flex' align='middle'>-->
        <!--              <el-col :xs="24" :sm="12">-->
        <!--                <el-form-item :label="$t('dialog.chName')"-->
        <!--                              prop='chName'>-->
        <!--                  <el-input v-model="sosChannel.chName"-->
        <!--                            @change='sosChannelNameChanged'-->
        <!--                            :maxlength='16'></el-input>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm="12">-->
        <!--                <el-form-item :label="$t('dialog.colorCodes')">-->
        <!--                  <el-input-number-->
        <!--                    v-model="sosChannel.subChannelData.colorCode"-->
        <!--                    step-strictly-->
        <!--                    :min="0" :max="15" :step="1">-->
        <!--                  </el-input-number>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm="12">-->
        <!--                <el-form-item>-->
        <!--                  <el-checkbox v-model="sosChannel.subChannelData.timeSlotConfig.priorityInterrupt">-->
        <!--                    <span v-text="$t('writeFreq.priorityInterrupt')"></span>-->
        <!--                  </el-checkbox>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm="12">-->
        <!--                <el-form-item>-->
        <!--                  <el-checkbox v-model="sosChannel.scanConfig.directMode">-->
        <!--                    <span v-text="$t('dialog.passThroughMode')"></span>-->
        <!--                  </el-checkbox>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm="12">-->
        <!--                <el-form-item :label="$t('dialog.slotMode')">-->
        <!--                  <el-select v-model="sosChannel.subChannelData.timeSlotConfig.timeSlot"-->
        <!--                             -->
        <!--                             :placeholder="$t('dialog.select')"-->
        <!--                             :no-match-text="$t('dialog.noMatchText')">-->
        <!--                    <el-option v-for="(item,i) in slotModeList" :key="i"-->
        <!--                               :label="item.label"-->
        <!--                               :value="item.value"></el-option>-->
        <!--                  </el-select>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm="12" v-if='!isAChannel'>-->
        <!--                <el-form-item :label="$t('dialog.virtualTimeSlot')">-->
        <!--                  <el-select v-model="sosChannel.subChannelData.timeSlotConfig.virtualTimeSlot"-->
        <!--                             -->
        <!--                             :placeholder="$t('dialog.select')"-->
        <!--                             :disabled='sosChannel.subChannelData.timeSlotConfig.timeSlot!==2'-->
        <!--                             :no-match-text="$t('dialog.noMatchText')">-->
        <!--                    <el-option v-for="(item,i) in virtualTimeSlotList" :key="i"-->
        <!--                               :label="item.label"-->
        <!--                               :value="item.value"></el-option>-->
        <!--                  </el-select>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm="12">-->
        <!--                <el-form-item :label="$t('writeFreq.voicePriority')">-->
        <!--                  <el-input-number-->
        <!--                    v-model="sosChannel.subChannelData.alertConfig.voicePriority"-->
        <!--                    step-strictly-->
        <!--                    :min="0" :max="3" :step="1">-->
        <!--                  </el-input-number>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--            </el-row>-->
        <!--            <el-row :gutter='20' class='no-margin-x'-->
        <!--                    type='flex' align='middle'>-->
        <!--              <el-divider>-->
        <!--                <el-icon class="divider-icon“><CaretBottom /></el-icon>-->
        <!--                <span class='divider-label' v-text='$t("dialog.receive")'></span>-->
        <!--              </el-divider>-->
        <!--              <el-col :xs="24" :sm='12'>-->
        <!--                <el-form-item :label="$t('dialog.rxFrequency')"-->
        <!--                              prop='rxFreq'>-->
        <!--                  <frequencyMhz v-model="sosChannel.rxFreq"-->
        <!--                                :maxlength='9'></frequencyMhz>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm='12'>-->
        <!--                <el-form-item :label="$t('writeFreq.freqOffset')">-->
        <!--                  <el-input v-model="freqOffset" >-->
        <!--                    <el-button slot="append" icon="search"-->
        <!--                               @click='mappingFreqOffset'-->
        <!--                               v-text='$t("writeFreq.mapping")'>-->
        <!--                    </el-button>-->
        <!--                  </el-input>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--            </el-row>-->
        <!--            <el-row :gutter='20' class='no-margin-x'-->
        <!--                    type='flex' align='middle'>-->
        <!--              <el-divider>-->
        <!--                <el-icon class="divider-icon“><CaretBottom /></el-icon>-->
        <!--                <span class='divider-label' v-text='$t("dialog.emission")'></span>-->
        <!--              </el-divider>-->
        <!--              <el-col :xs="24" :sm='12'>-->
        <!--                <el-form-item :label="$t('dialog.txFrequency')"-->
        <!--                              prop='txFreq'>-->
        <!--                  <frequencyMhz v-model="sosChannel.txFreq"-->
        <!--                                :maxlength='9'></frequencyMhz>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm='12'>-->
        <!--                <el-form-item :label="$t('dialog.txPower')">-->
        <!--                  <el-select v-model="sosChannel.powerConfig.powerType"-->
        <!--                             -->
        <!--                             :placeholder="$t('dialog.select')"-->
        <!--                             :no-match-text="$t('dialog.noMatchText')">-->
        <!--                    <el-option v-for="(item,i) in txPowerTypes" :key="i"-->
        <!--                               :label="item.label"-->
        <!--                               :value="item.value"></el-option>-->
        <!--                  </el-select>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm='12'>-->
        <!--                <el-form-item :label="$t('dialog.sendTimeLimiter')">-->
        <!--                  <el-input-number v-model="sosChannel.transmissionLimit"-->
        <!--                                   step-strictly-->
        <!--                                   :min="15" :max="495" :step="15"></el-input-number>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm='12'>-->
        <!--                <el-form-item :label="$t('dialog.totPwdUpdateDelay')">-->
        <!--                  <el-input-number v-model="sosChannel.totKeyUpdateDelay"-->
        <!--                                   step-strictly-->
        <!--                                   :min="0" :max="255" :step="5"></el-input-number>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--              <el-col :xs="24" :sm='12' v-if='!isAChannel'>-->
        <!--                <el-form-item :label="$t('dialog.permissionConditions')">-->
        <!--                  <el-select v-model="sosChannel.subChannelData.alertConfig.permitConditions"-->
        <!--                             -->
        <!--                             :placeholder="$t('dialog.select')"-->
        <!--                             :no-match-text="$t('dialog.noMatchText')">-->
        <!--                    <el-option v-for="(item,i) in permissionConditionsList" :key="i"-->
        <!--                               :label="item.label"-->
        <!--                               :value="item.value"></el-option>-->
        <!--                  </el-select>-->
        <!--                </el-form-item>-->
        <!--              </el-col>-->
        <!--            </el-row>-->
        <!--          </el-form>-->
        <!--        </el-card>-->
      </main>
    </section>
    <write-freq-footer
      :is-reading="isReading"
      :is-writing="isWriting"
      :disable-read="disReadBtn"
      :disable-write="disWriteBtn"
      @new-config="newConfig"
      @read-config="readDataConfig"
      @write-config="writeInFrequency"
      @export-config="exportConfig"
      @import-config="importConfig"
    />
  </div>
</template>

<script>
  import TableTree from '@/components/common/tableTree'
  import { cloneDeep, merge } from 'lodash'
  import Long from 'long'
  import bftree from '@/utils/bftree'
  import bfutil from '@/utils/bfutil'
  import bfNotify from '@/utils/notify'
  import validateRules from '@/utils/validateRules'
  import { AnalogCodeData, AnalogDigitalCodeData } from '@/writingFrequency/interphone/511SDC00'
  import {
    filterChannelIdWhenDeviceChanged,
    fixPowerOnPassword,
    fixProgramPassword,
    generateEnumObject,
    resetDigitalAlertReplyChannel,
  } from '@/writingFrequency/interphone/common'
  import commonToolMixin from '@/writingFrequency/interphone/commonToolMixin'
  import {
    ButtonKeys,
    CallType,
    getClassInstance,
    Model,
    SoftKeyCallType,
    TableIndex,
  } from '@/writingFrequency/interphone/DP109SDC'
  import wfTool from '@/writingFrequency/interphone/tool'
  import deviceInfo from '@/components/interphoneWf/common/deviceInfo'
  import selectDevice from '@/components/interphoneWf/common/selectDevice'
  import WriteFreqFooter from '@/components/interphoneWf/common/writeFreqFooter.vue'
  import powerOnPwdTipsPng from '@/images/TD910(P)_powerOnPwd_tips.png'
  import { defineAsyncComponent } from 'vue'

  function getTimeZoneOffsetObject(TimeZoneOffset = new Date().getTimezoneOffset()) {
    return {
      timeZoneHour: ((TimeZoneOffset * -1) / 60) | 0,
      timeZoneMinute: Math.abs(TimeZoneOffset) % 60,
    }
  }

  const Password = {
    md5Key: '',
  }
  const MenuTreeContextmenu = generateEnumObject({
    ADD: 1,
    DELETE: 2,
    ORDER: 3,
  })
  const GeneralSettings = {
    deviceName: '',
    intDmrId: 0,
    repeaterId: 16777215,
    soundCtrlLevel: 0,
    soundCtrlDelay: 500,
    sendLeadCodeTime: 960,
    offlineGroupCallHungTime: 2000,
    offlineSingleCallHungTime: 2000,
    powerInfoAlert: 120,
    baseSettings: {
      locale: 0,
      disabledAllLED: false,
      rejectUnfamiliarCall: false,
      directMode: false,
      pttAlone: false,
      allowedSelfDestruct: true,
      stealthModeHeadsetMute: false,
    },
    savePowerMode: 2,
    soundSettings: {
      muteAll: false,
      voiceNotice: true,
      channelFreeNotice: false,
      allowCallInstruction: 2,
    },
    second: 0,
    minute: 0,
    hour: 0,
    day: 0,
    month: 0,
    year: 0,
    ...getTimeZoneOffsetObject(),
    syncTime: false,
    recordSettings: {
      enable: false,
      compressionRatio: 0,
      allowErasing: false,
    },
    powerTransfer: {
      enable: false,
    },
    highPowerLimit: -115,
    middlePowerLimit: -70,
    lowPowerLimit: -65,
  }
  const ButtonDefined = {
    longPressTime: 1000,
    sideKey: [
      {
        short: 2,
        long: 3,
      },
      {
        short: 0,
        long: 0,
      },
    ],
    singleKeyCall: [
      {
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
    ],
    defaultChannel: [
      {
        zoneId: 0xFFFF,
        channelId: 0xFFFF,
        rootId: 0xFFFF,
        parentId: 0xFFFF,
      },
      {
        zoneId: 0xFFFF,
        channelId: 0xFFFF,
        rootId: 0xFFFF,
        parentId: 0xFFFF,
      },
      {
        zoneId: 0xFFFF,
        channelId: 0xFFFF,
        rootId: 0xFFFF,
        parentId: 0xFFFF,
      },
      {
        zoneId: 0xFFFF,
        channelId: 0xFFFF,
        rootId: 0xFFFF,
        parentId: 0xFFFF,
      },
    ],
  }
  const EncryptConfig = {
    config: {
      // 加密使能
      encryptEnable: false,
    },
  }
  const OneEncryptOption = {
    keyId: 0,
    keyValue: '',
    keyName: '',
  }
  const MenuSettings = {
    hangTime: 30,
    baseSetting: {
      menuOff: false,
      chDisplayMode: 2,
      showAlias: true,
    },
    powerOnPwd: '',
    chConfigPwd: '',
    contactConfig: {
      contacts: true,
      contactList: true,
      editEnable: true,
      manualDialing: true,
      privateCallDialing: true,
      groupCallDialing: true,
      newContact: true,
      callPrompting: true,
    },
    deviceControl: {
      deviceDetect: false,
      remoteMonitor: false,
      deviceActive: false,
      deviceRemoteDeath: false,
      deviceRemoteDestroy: false,
      deviceStun: false,
      deviceWokeUp: false,
    },
    phoneConfig: {
      enable: true,
      list: true,
      manualDialing: true,
    },
    zoneConfig: {
      enable: true,
    },
    scanConfig: {
      enable: true,
      editList: true,
      // 以下为漫游配置
      roamEnable: true,
      roamLockSite: true,
      roamManual: true,
    },
    smsConfig: {
      enable: true,
    },
    callConfig: {
      callHistory: true,
      missedCall: true,
      receivedCall: true,
      outgoingRecord: true,
    },
    deviceConfig: {
      setting: true,
      deviceSetting: true,
      offline: true,
      toneTip: true,
      transmitPower: true,
      backLight: true,
      bootInterface: true,
      keyboardLock: true,
    },
    deviceConfig2: {
      ledIndicator: true,
      quieting: true,
      powerOnPassword: false,
      locale: true,
      soundCtrl: true,
      timeSetting: true,
      locate: false,
      workAlone: false,
    },
    recordConfig: {
      upend: false,
      recording: false,
      uDiskMode: false,
      maydayRescue: true,
      stealthMode: true,
    },
    deviceInfo: {
      info: true,
    },
    channelSetting: {
      chConfigEnable: true,
      rxFreq: true,
      txFreq: true,
      channelName: true,
      roamInterface: true,
      subAudioSetting: true,
      launchContact: true,
      colorCode: true,
    },
    timeSlotSetting: {
      timeSlot: true,
      virtualClusterTimeSlot: true,
      receivingList: true,
    },
  }
  const SignalingSystem = {
    remoteConfig: {
      remoteDeathDecode: true,
      remoteMonitorDecode: false,
      urgentRemoteMonitorDecode: true,
      remoteNoticeDecode: false,
      remoteDetectDecode: false,
      remoteEraseDecode: false,
      remoteStunWakeupDecode: true,
    },
    remoteMonitorDuration: 10,
    signalingPwd: '',
  }
  const AlertConfig = {
    aloneWorkEnable: false,
    aloneWorkTime: 1,
    aloneWorkRemindTime: 60,
    aloneWorkResOpt: 0,
    upendEnable: false,
    entryDelay: 10,
    quitDelay: 10,
    upendConfig: {
      rewindTime: 5,
      triggerTilt: 0,
      triggerMode: 2,
    },
  }
  // 数字信道
  const DigitalChannelBits = {
    timeSlotConfig: {
      timeSlot: 0,
      virtualTimeSlot: 0,
      throughEnable: false,
      chSlotAdjust: 0,
      priorityInterrupt: false,
    },
    alertConfig: {
      voicePriority: 1,
      emergencyAlertTip: false,
      emergencyAlertConfirm: false,
      emergencyCallTip: false,
      permitConditions: 1,
      singleCallRes: false,
    },
    networkConfig: {
      networking: true,
      localCall: false,
    },
    encryptConfig: {
      enable: false,
      type: 0,
      algorithm: 0,
      randomKey: false,
      randomAlg: false,
      smsEncrypt: false,
    },
  }
  const DigitalChannel = {
    colorCode: 0,
    timeSlotConfig: DigitalChannelBits.timeSlotConfig,
    alertConfig: DigitalChannelBits.alertConfig,
    networkConfig: DigitalChannelBits.networkConfig,
    encryptConfig: DigitalChannelBits.encryptConfig,
    encryptKeyList: 0,
    receiveGroup: 0xFF,
    defaultAddress: 0xFFFF,
    emergencySysId: 0xFF,
  }
  // 模拟信道参数
  const AnalogChannelBits = {
    funcConfig: {
      bandwidthFlag: 1,
      beatFrequency: 0,
      preEmphasis: 0,
      pressureDiffusion: 0,
      scrambleEnable: 0,
      signalingSystem: 0,
      tailCancellation: true,
      ctcssTxDps: 0,
      ctcssRxDps: 0,
      bclFlag: 0,
    },
  }
  const AnalogChannel = {
    funcConfig: AnalogChannelBits.funcConfig,
    scramble: 3300,
    subsonicDecode: 0xFFFF,
    subsonicEncode: 0xFFFF,
    rxSignallingSystem: 0,
    txSignallingSystem: 0,
  }
  // 数模兼容
  const CompatibleChannel = {
    ...DigitalChannel,
    ...AnalogChannel,
  }
  // 信道数据
  const Channel = {
    // 信道 ID
    chId: 0,
    chType: 0,
    scanList: 0xFF,
    scanConfig: {
      listType: 0,
      autoScan: false,
      onlyReceive: false,
      ipSiteConnect: false,
      autoRoam: false,
      allowOfflineSign: false,
      directMode: false,
    },
    powerConfig: {
      powerType: 1,
      squelchLevel: 3,
    },
    rxFreq: 136000000,
    txFreq: 174000000,
    subChannelData: {},
    transmissionLimit: 300,
    totKeyUpdateDelay: 0,
    chName: '',
  }
  // 救援/求救
  const SosCfg = {
    config: {
      enable: true,
      intervalHonking: false,
      flashlight: false,
      sendGps: false,
    },
    intervalHonkingTime: 0,
    flashlightTime: 1,
    rescueScanHandUpTime: 5000,
    highPowerSosTime: 0,
    middlePowerSosTime: 0,
    lowPowerSosTime: 0,
    sosInfo: '',
  }
  let lastClickNodeKey = null

  export default {
    name: 'TD910SDC',
    mixins: [commonToolMixin, wfTool],
    data() {
      return {
        selectedDeviceDmrId: '',
        isReading: false,
        isWriting: false,
        menuTreeId: 'TD910MenuTree',
        selectMenu: 'TD910DeviceInfo',

        // 设备信息
        deviceModel: Model,
        deviceWriteInfo: {},
        // 身份信息
        identityInfo: {},
        // 编程密码
        passwordInfo: cloneDeep(Password),
        // 常规设置
        generalSettings: cloneDeep(GeneralSettings),
        // 按键设置
        buttonDefined: cloneDeep(ButtonDefined),
        // 短信
        refSms: 'shortMessage',
        smsMaxSize: 100,
        smsContent: [],
        // 加密配置
        encryptConfig: cloneDeep(EncryptConfig),
        // 加密列表 最大数量: 32
        encryptListLimit: 32,
        encryptList: [this.createEncryptItem()],
        // 菜单设置
        menuSettings: cloneDeep(MenuSettings),
        // 信令系统
        signalingSystem: cloneDeep(SignalingSystem),
        // 警报配置
        alertConfig: cloneDeep(AlertConfig),
        // 数字报警
        digitalAlertList: [],
        currDigitalAlertId: -1,
        digitalAlertListLimit: 32,
        // 数字通讯录
        selectedAddressBook: [],
        addressBookCache: [],
        originAddressBook: [],
        addrBookTreeId: 'TD910AddressBookTree',
        // 数字通讯录群组
        addressBookGroup: [],
        currAddressBookGroupId: 0,
        addressBookGroupLimit: 10,
        // 电话本
        phoneBookTreeId: 'TD910PhoneBook',
        phoneBook: [],
        // 接收组
        rxGroupList: [],
        refReceiveGroup: 'receiveGroup',
        // 信道
        channelDataList: [],
        channelDataListCache: [],
        oneZoneLeafChannelLimit: 3773,
        oneZoneChannelLimit: 64,
        currChannelId: 0,
        oneChannel: this.getDefaultChannel(),
        // 频率偏移值
        freqOffset: 10,

        // 一级区域
        zoneRootDataList: [],
        zoneRootLimit: 4,
        currZoneRootId: -1,
        // 二级区域
        zoneParentDataList: [],
        zoneParentLimit: 1000,
        currZoneParentId: -1,
        // 三级区域
        zoneLeafDataList: [],
        zoneLeafLimit: 2000,
        currZoneLeafId: -1,
        // 扫描组
        scanGroup: [],
        currScanGroupId: 0,
        scanGroupLimit: 32,
        // 漫游
        roamGroup: [],
        currRoamGroupId: 0,
        roamGroupLimit: 32,
        // 系统功能巡查配置
        patrolConfig: {},
        // 系统功能紧急报警,
        emergencyAlarm: {},
        patrolSystemTabsValue: 'configure',

        // 救援/求救配置
        sosCfg: cloneDeep(SosCfg),
        defaultChannelNames: ['145.5MHz', '435.5MHz'],
        sosChannelList: [],
        sosChannelId: 0,
        sosChannel: {},
      }
    },
    methods: {
      fixPowerOnValue(value) {
        const reg = /[^0-6]/g
        this.menuSettings.powerOnPwd = fixPowerOnPassword(value, reg)
      },
      fixPowerValueOnBlur() {
        this.fixPowerOnValue(this.menuSettings.powerOnPwd)
        const powerOnPwd = this.menuSettings.powerOnPwd
        this.menuSettings.powerOnPwd = powerOnPwd.length === 6 ? powerOnPwd : ''
      },
      frequencyHz2Mhz: bfutil.frequencyHz2Mhz,
      frequencyMhz2Hz: bfutil.frequencyMhz2Hz,
      fixMd5KeyValue(value) {
        this.passwordInfo.md5Key = fixProgramPassword(value)
      },
      initSosChannelList() {
        const channel = this.getDefaultChannel()
        this.sosChannelList = this.defaultChannelNames.map((name, index) => {
          const sosChannel = cloneDeep(channel)
          sosChannel.chName = name
          sosChannel.chId = index
          sosChannel.rxFreq = sosChannel.txFreq = bfutil.frequencyMhz2Hz(name.slice(0, -3))
          return sosChannel
        })
        this.sosChannel = this.sosChannelList[this.sosChannelId]
      },
      sosChannelNameChanged(val) {
        if (!val) {
          this.sosChannel.chName = `${this.defaultChannelNames[this.sosChannel.chId]}`
        }
        this.updateNodeTitle({
          key: `${this.menuTreeBaseNodeKeys[39]}:${this.sosChannel.chId}`,
          title: this.sosChannel.chName,
          data: this.sosChannel,
        })
      },
      // 切换选中的设备时，同步界面中的配置数据
      mergeGeneralSettings(device) {
        this.generalSettings = Object.assign(this.generalSettings, {
          deviceName: device.selfId,
          intDmrId: parseInt(device.dmrId, 16),
        })
      },
      getOldChannelFromCache(chId) {
        for (let i = 0; i < this.channelDataListCache.length; i++) {
          const data = this.channelDataListCache[i]
          if (data.chId === chId) {
            return cloneDeep(data)
          }
        }
        return {}
      },
      initChannels() {
        // 如果没有合法有效的信道，则向用户提示
        if (!this.selectedChannels.length) {
          this.$nextTick(() => {
            bfNotify.messageBox(this.$t('writeFreq.notHaveValidChannel'), 'error')
          })
          return
        }

        // 生成信道与对应的三级区域数据
        const zoneLeafDataMap = {}
        const zoneLeafNodes = {}
        const channelsMap = {}
        const zoneConfigCache = {}
        // 默认最小频率
        const minFreq = this.frequencyMhz2Hz(this.deviceWriteInfo.minFrequency)

        for (let i = 0; i < this.selectedChannels.length; i++) {
          const channel = this.selectedChannels[i]
          // 缓存信道区域配置，同一个区域配置只查找一次
          const zoneLeaf = zoneConfigCache[channel.zoneRid] || bfglob.gchannelZone.get(channel.zoneRid)
          if (!zoneLeaf || zoneLeaf.zoneLevel !== 4) {
            continue
          }
          zoneConfigCache[channel.zoneRid] = zoneLeaf

          // 生成三级区域数据
          let zoneLeafData = zoneLeafDataMap[channel.zoneRid]
          if (!zoneLeafData) {
            zoneLeafData = this.leafZoneComp?.addOneZoneData()
          }
          // 每个三级区域最多有64个信道
          if (!zoneLeafData || zoneLeafData.usedList.length >= this.oneZoneChannelLimit) {
            continue
          }

          // 生成信道的节点数据
          if (!this.zoneLeafChannelComp) {
            continue
          }
          let channelData = this.zoneLeafChannelComp.addOneChannel()
          if (!channelData) {
            continue
          }
          const chId = channel.no - 1
          // 判断信道ID是否超出机型的信道范围
          if (chId >= this.oneZoneLeafChannelLimit) {
            continue
          }
          // 合并缓存参数
          channelData = merge(channelData, this.getOldChannelFromCache(chId) || cloneDeep(this.oneChannel))
          // 重置信道ID和名称
          channelData.chId = chId
          channelData.chName = `${this.$t('dialog.channel')} ${channel.no}`
          // 设置接收组和发射组ID
          channelData.subChannelData.receiveGroup = this.getRxGroupId(channelData.chId)
          channelData.subChannelData.defaultAddress = this.getDefaultAddress(channel.sendGroup)
          // 重置信道默认频率
          channelData.rxFreq = minFreq || channelData.rxFreq
          channelData.txFreq = minFreq || channelData.txFreq

          // 设置三级区域管辖信道数据参数
          zoneLeafData.usedList.push(channelData.chId)
          zoneLeafData.usedFlag = this.setZoneChFlag(zoneLeafData.usedList)
          this.setZoneDataName(zoneLeafData, zoneLeaf)

          // 缓存三级区域的ID到信道中
          channelData.zoneId = zoneLeafData.zoneId
          channelData.zoneRid = channel.zoneRid

          // 生成fancytree节点
          if (!zoneLeafNodes[channel.zoneRid]) {
            zoneLeafNodes[channel.zoneRid] = this.createZoneLeafNodeData(zoneLeafData)
          }
          const zoneLeafNode = zoneLeafNodes[channel.zoneRid]
          zoneLeafNode.children.push(this.createChannelNodeData(channelData))

          // 缓存三级区域和信道数据
          zoneLeafDataMap[channel.zoneRid] = zoneLeafData
          channelsMap[channelData.chId] = channelData
        }

        const showData = this.channelDataList.find(data => data.chId === this.currChannelId)
        if (showData) { this.oneChannel = showData }

        // 生成三级区域对应的二级区域数据
        const zoneParentDataMap = {}
        const zoneParentNodes = {}
        const zoneLeafDataMapKeys = Object.keys(zoneLeafDataMap)
        for (let i = 0; i < zoneLeafDataMapKeys.length; i++) {
          const key = zoneLeafDataMapKeys[i]
          const zoneLeafData = zoneLeafDataMap[key]

          // 未生成二级区域节点，则先创建
          const zoneParent = zoneLeafData.origin.zoneParent
          let zoneParentData = zoneParentDataMap[zoneParent]
          if (!zoneParentData) {
            const zoneParentOriginData = bfglob.gchannelZone.get(zoneParent)
            // 未查找到本地二级区域数据，跳过创建
            if (!zoneParentOriginData || zoneParentOriginData.zoneLevel !== 3) {
              continue
            }

            zoneParentData = this.parentZoneComp.addOneZoneData()
            if (!zoneParentData) {
              continue
            }
            this.setZoneDataName(zoneParentData, zoneParentOriginData)
            zoneParentDataMap[zoneParent] = zoneParentData

            // 生成fancytree节点
            if (!zoneParentNodes[zoneParent]) {
              zoneParentNodes[zoneParent] = this.createZoneParentNodeData(zoneParentData)
            }
          }

          // 同步子级区域的上下级关系ID
          zoneLeafData.parentId = zoneParentData.zoneId
          // 关联子级区域fancytree节点
          const zoneParentNode = zoneParentNodes[zoneParent]
          zoneParentNode.children.push(zoneLeafNodes[zoneLeafData.origin.rid])
        }

        // 生成二级区域对应的一级区域数据
        const zoneRootDataMap = {}
        const zoneRootNodes = {}
        const zoneParentDataMapKeys = Object.keys(zoneParentDataMap)
        for (let i = 0; i < zoneParentDataMapKeys.length; i++) {
          const key = zoneParentDataMapKeys[i]
          const zoneParentData = zoneParentDataMap[key]

          // 未生成一级区域节点，则先创建
          const zoneParent = zoneParentData.origin.zoneParent
          let zoneRootData = zoneRootDataMap[zoneParent]
          if (!zoneRootData) {
            const zoneRootOriginData = bfglob.gchannelZone.get(zoneParent)
            // 未查找到本地一级区域数据，跳过创建
            if (!zoneRootOriginData || zoneRootOriginData.zoneLevel !== 2) {
              continue
            }

            zoneRootData = this.rootZoneComp.addOneZoneData()
            if (!zoneRootData) {
              continue
            }
            this.setZoneDataName(zoneRootData, zoneRootOriginData)
            zoneRootDataMap[zoneRootOriginData.rid] = zoneRootData

            // 生成fancytree节点
            if (!zoneRootNodes[zoneRootOriginData.rid]) {
              zoneRootNodes[zoneRootOriginData.rid] = this.createZoneRootNodeData(zoneRootData)
            }
          }

          // 同步子级区域的上下级关系ID
          zoneParentData.rootId = zoneRootData.zoneId
          // 关联子级区域fancytree节点
          const zoneRootNode = zoneRootNodes[zoneParent]
          zoneRootNode.children.push(zoneParentNodes[zoneParentData.origin.rid])
        }
        // 同步三级区域的一级区域ID
        for (let i = 0; i < zoneLeafDataMapKeys.length; i++) {
          const key = zoneLeafDataMapKeys[i]
          const zoneLeafData = zoneLeafDataMap[key]
          const zoneParentData = zoneParentDataMap[zoneLeafData.origin.zoneParent]
          if (!zoneParentData) {
            continue
          }
          zoneLeafData.rootId = zoneParentData.rootId
        }

        // 加载fancytree节点数据到DOM元素上
        // 信道设置最顶级根节点，如果不存在，则无法加载所有区域、信道节点
        const channelRootNode = this.getNodeByKey(this.menuTreeBaseNodeKeys[26])
        if (!channelRootNode) {
          return
        }
        this.addNodeChildren(channelRootNode, Object.keys(zoneRootNodes).map(key => {
          return zoneRootNodes[key]
        }))
      },
      filterScanListChannelId() {
        const channelIdList = this.currentChannelIdList
        this.scanGroup = this.scanGroup.map(item => {
          item.membersList = filterChannelIdWhenDeviceChanged(channelIdList, item.membersList)
          item.channelCount = item.membersList.length
          return item
        })
      },
      filterRoamListChannelId() {
        const channelIdList = this.currentChannelIdList
        this.roamGroup = this.roamGroup.map(item => {
          item.roamChList = filterChannelIdWhenDeviceChanged(channelIdList, item.roamChList)
          item.roamChCount = item.roamChList.length
          return item
        })
      },
      resetDigitalAlarmReplyChannel() {
        const channelIdList = this.currentChannelIdList
        this.digitalAlertList = this.digitalAlertList.map(data => {
          data.replyChannel = resetDigitalAlertReplyChannel(channelIdList, data.replyChannel)
          return data
        })
      },
      async syncDeviceDataIntoConfig(device) {
        this.mergeGeneralSettings(device)

        await this.initRxGroupList()
        // 区域、信道
        this.initChannels()
        // // 扫描组过滤不存在的信道ID
        this.filterScanListChannelId()
        // 漫游组过滤不存在的信道ID
        this.filterRoamListChannelId()
        // 数字报警重置回复信道参数
        this.resetDigitalAlarmReplyChannel()

        this.$nextTick(() => {
          if (!lastClickNodeKey) { return }
          this.manuallyTriggerNodeClick(this.getNodeByKey(lastClickNodeKey))
        })
      },
      // 清除通讯录被禁用的状态
      clearTreeNodeUnselectable() {
        // 清除通讯录被禁用和非系统通讯数据的节点
        this.addressBookComp && this.addressBookComp.removeNotInSystemNodes()
        this.selectedAddressBook.forEach(item => {
          bftree.nodeUnselectable(this.addrBookTreeId, item.nodeKey)
        })
      },
      clearPrivateConfig() {
        // 清除常规设置的设备名称
        this.generalSettings.deviceName = ''
        this.generalSettings.intDmrId = 0
        // 接收组
        this.receiveGroupComp && this.receiveGroupComp.resetRxGroupList()
        // 区域
        this.initZoneDataList()
        // 信道
        this.initChannelDataList()
        this.clearTreeNodeUnselectable()
        if (this.phoneBookComp) {
          this.phoneBookComp.removeNotInSystemNodes()
        }
      },
      // cleanAll 标记是否清除全部配置
      async clearDeviceDataConfig(cleanAll = false) {
        // 必须清除的数据，接收组、区域、信道等私有数据，包括一些标记参数
        this.clearPrivateConfig()

        this.initEncryptConfig()
        this.initScanGroupList()
        this.initRoamGroupList()
        this.initDigitAlertList()

        // 可选的清除数据，常规设置、菜单、按键定义、警报、通讯录、电话本、短信等通用数据
        if (!cleanAll) { return }
        this.originAddressBook = []
        this.selectedAddressBook = []
        this.addressBookCache = []
        this.addressBookComp && this.addressBookComp.treeReload(true)
        this.initAddressBookGroup()
        this.phoneBook = []
        this.phoneBookComp && this.phoneBookComp.treeReload(true)
      },
      initSmsData() {
        this.smsContent = []
      },
      initEncryptConfig() {
        this.encryptConfig = cloneDeep(EncryptConfig)
        this.encryptList = []
        this.addEncryptItem()
      },
      initRxGroupList() {
        if (!this.receiveGroupComp) {
          return []
        }
        return this.receiveGroupComp.initRxGroupList(this.selectedChannels)
      },
      initScanGroupList() {
        // 删除节点，通过父节点，直接删除所有子节点数据
        const parentNodeKey = this.menuTreeBaseNodeKeys[27]
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.scanGroupComp) {
          this.scanGroupComp.initDataList()
        }
      },
      initRoamGroupList() {
        // 删除节点
        const parentNodeKey = this.menuTreeBaseNodeKeys[28]
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.roamGroupComp) {
          this.roamGroupComp.initDataList()
        }
      },
      initDigitAlertList() {
        // 删除节点
        const parentNodeKey = this.menuTreeBaseNodeKeys[18]
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.digitalAlertComp) {
          this.digitalAlertComp.initDigitalAlert()
        }
      },
      initZoneDataList() {
        if (this.rootZoneComp) {
          this.rootZoneComp.initZoneData()
        }
        if (this.parentZoneComp) {
          this.parentZoneComp.initZoneData()
        }
        if (this.leafZoneComp) {
          this.leafZoneComp.initZoneData()
        }
      },
      initChannelDataList() {
        if (this.channelDataList.length) {
          this.channelDataListCache = cloneDeep(this.channelDataList)
        }
        // 删除信道菜单节点下所有子节点
        const parentNodeKey = this.menuTreeBaseNodeKeys[26]
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.zoneLeafChannelComp) {
          this.zoneLeafChannelComp.initDataList()
        }
      },
      initAddressBookGroup() {
        // 删除节点
        const parentNodeKey = this.menuTreeBaseNodeKeys[19]
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.addressBookGroupComp) {
          this.addressBookGroupComp.initDataList()
        }
      },
      initPatrolConfig(flag = false) {
        if (!flag && this.patrolConfigComp) {
          this.patrolConfigComp.initData()
        }
      },
      initEmergencyAlarm(flag = false) {
        if (!flag && this.emergencyAlarmComp) {
          this.emergencyAlarmComp.initData()
        }
      },
      removeMenuTreeNodeChildren(key) {
        const parentNode = this.getNodeByKey(key)
        if (parentNode) {
          parentNode.expanded = true
          parentNode.removeChildren()
          this.updateViewport()
        }
      },

      // 区域数据
      loadZoneNodes({
        dataList = [],
        parentKeyPrefix = '',
        parentKeyProp,
        createNode,
      }) {
        if (typeof createNode !== 'function' || !dataList.length) {
          return
        }

        const zonesMap = {}
        for (let i = 0; i < dataList.length; i++) {
          const data = dataList[i]
          let parentKey = parentKeyPrefix
          if (typeof parentKeyProp !== 'undefined') {
            parentKey += `:${data[parentKeyProp]}`
          }
          if (!zonesMap[parentKey]) {
            zonesMap[parentKey] = []
          }
          zonesMap[parentKey].push(createNode(data))
        }

        const parentNodeCache = {}
        const zonesMapKeys = Object.keys(zonesMap)
        for (let i = 0; i < zonesMapKeys.length; i++) {
          const key = zonesMapKeys[i]
          const parentNode = parentNodeCache[key] || (parentNodeCache[key] = this.getNodeByKey(key))
          if (!parentNode) {
            continue
          }
          this.addNodeChildren(parentNode, zonesMap[key])
        }
      },
      setZoneDataName(target, data) {
        target.zoneName = data.zoneTitle
        target.origin = cloneDeep(data)
      },
      zoneDataDblclick(key) {
        bftree.gotoNodeLocation(this.menuTreeId, key, { isActive: true })
      },
      zoneRootDataDblclick(row) {
        this.zoneDataDblclick(`${this.menuTreeBaseNodeKeys[23]}:${row.zoneId}`)
      },
      createZoneRootNodeData(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[23]
        const key = `${parentNodeKey}:${data.zoneId}`
        return {
          title: data.zoneName,
          folder: true,
          expanded: true,
          key: key,
          icon: false,
          selected: false,
          origin: data,
          children: [],
        }
      },
      loadZoneRootNodes(dataList = this.zoneRootDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.menuTreeBaseNodeKeys[26],
          createNode: this.createZoneRootNodeData,
        })
      },
      zoneParentDataDblclick(row) {
        this.zoneDataDblclick(`${this.menuTreeBaseNodeKeys[24]}:${row.zoneId}`)
      },
      createZoneParentNodeData(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[24]
        const key = `${parentNodeKey}:${data.zoneId}`
        return {
          title: data.zoneName,
          folder: true,
          expanded: true,
          key: key,
          icon: false,
          selected: false,
          origin: data,
          children: [],
        }
      },
      loadZoneParentNodes(dataList = this.zoneParentDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.menuTreeBaseNodeKeys[23],
          parentKeyProp: 'rootId',
          createNode: this.createZoneParentNodeData,
        })
      },
      zoneLeafDataDblclick(row) {
        this.zoneDataDblclick(`${this.menuTreeBaseNodeKeys[25]}:${row.zoneId}`)
      },
      createZoneLeafNodeData(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[25]
        const key = `${parentNodeKey}:${data.zoneId}`
        return {
          title: data.zoneName,
          folder: true,
          expanded: true,
          key: key,
          icon: false,
          selected: false,
          origin: data,
          children: [],
        }
      },
      loadZoneLeafNodes(dataList = this.zoneLeafDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.menuTreeBaseNodeKeys[24],
          parentKeyProp: 'parentId',
          createNode: this.createZoneLeafNodeData,
        })
      },

      // 信道设置
      getChannelSubProto(type = 0) {
        switch (type) {
          case 0:
            return cloneDeep(DigitalChannel)
          case 1:
            return cloneDeep(AnalogChannel)
          default:
            return cloneDeep(CompatibleChannel)
        }
      },
      getDefaultChannel(type = 0) {
        const channel = Channel
        channel.chName = `${this.$t('dialog.channel')} 1`
        channel.subChannelData = this.getChannelSubProto(type)

        return cloneDeep(channel)
      },
      zoneLeafDataRowDblclick(row) {
        const showChannel = () => {
          this.setChannelData(row)
          this.zoneDataDblclick(`${this.menuTreeBaseNodeKeys[260]}:${row.chId}`)
        }
        if (row.chId === this.oneChannel.chId) {
          showChannel()
          return
        }
        // 如果信道设置的数据在信道列表中，则需要验证表单通过后再切换信道
        this.validateOneChannel()
          .then(() => {
            showChannel()
          })
          .catch(() => {
            // 提示对应的三级区域有信道未命名
            const zoneLeafData = this.zoneLeafDataIndex[this.oneChannel.zoneId]
            if (zoneLeafData) {
              bfNotify.messageBox(this.$t('writeFreq.correctChDataTip', { name: zoneLeafData.zoneName }), 'warning')
            }
          })
      },
      createChannelNodeData(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[260]
        const key = `${parentNodeKey}:${data.chId}`
        return {
          title: data.chName,
          folder: false,
          expanded: false,
          key: key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadChannelNodes(dataList = this.channelDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.menuTreeBaseNodeKeys[25],
          parentKeyProp: 'zoneId',
          createNode: this.createChannelNodeData,
        })
      },
      updateOneChannelNode(data) {
        this.updateNodeTitle({
          key: `${this.menuTreeBaseNodeKeys[260]}:${data.chId}`,
          title: data.chName,
          data,
        })
      },
      chNameChanged(val) {
        if (!val) {
          this.oneChannel.chName = `${this.$t('dialog.channel')} ${this.oneChannel.chId + 1}`
        }
        this.updateOneChannelNode(this.oneChannel)
      },
      chTypeChanged(val) {
        this.oneChannel['subChannelData'] = merge(this.getChannelSubProto(val,
          this.oneChannel.subChannelData,
        ))
        // 非数字信道，有亚音频参数，需要重置为默认值
        // 为确保纯模拟信道自定义参数正确显示，先将亚音频置为0,再重置参数
        if (val === 1) {
          this.oneChannel.subChannelData.subsonicDecode = 0
          this.oneChannel.subChannelData.subsonicEncode = 0
          this.$nextTick(() => {
            this.oneChannel.subChannelData.subsonicDecode = 0xFFFF
            this.oneChannel.subChannelData.subsonicEncode = 0xFFFF
          })
        } else if (val > 1) {
          //   this.oneChannel.subChannelData.subsonicDecode = 0x670
          //   this.oneChannel.subChannelData.subsonicEncode = 0x670
        }
      },
      getFreqRangeMaxValue(freq, unsigned = true) {
        for (let i = 0; i < this.deviceWriteInfo.frequencyRange.length; i++) {
          const range = this.deviceWriteInfo.frequencyRange[i]
          const min = bfutil.frequencyMhz2Hz(range.min)
          const max = bfutil.frequencyMhz2Hz(range.max)
          if (freq >= min && freq <= max) {
            return unsigned ? max : min
          }
        }
        return freq
      },
      subsonicDecodeChange(val) {
        this.oneChannel.subChannelData.subsonicDecode = this.encodeSubsonic2String(val)
      },
      subsonicEncodeChange(val) {
        this.oneChannel.subChannelData.subsonicEncode = this.encodeSubsonic2String(val)
      },
      setChannelData(data) {
        this.oneChannel = data
        // 纯模拟信道可自定义参数，为确保参数正确显示，先将参数设置为0,再重置
        if (this.oneChannel.chType === 1) {
          // 纯模拟信道可自定义参数，需要将值转换成字符串显示
          let subsonicDecode = this.oneChannel.subChannelData.subsonicDecode
          let subsonicEncode = this.oneChannel.subChannelData.subsonicEncode
          const codeList = this.subtoneCodeDataList
          if (codeList.filter(item => item.value === subsonicDecode).length === 0 && typeof subsonicDecode ===
            'number') {
            subsonicDecode = this.decodeSubsonic(subsonicDecode)
          }
          if (codeList.filter(item => item.value === subsonicEncode).length === 0 && typeof subsonicEncode ===
            'number') {
            subsonicEncode = this.decodeSubsonic(subsonicEncode)
          }

          this.oneChannel.subChannelData.subsonicDecode = 0
          this.oneChannel.subChannelData.subsonicEncode = 0
          this.$nextTick(() => {
            this.oneChannel.subChannelData.subsonicDecode = subsonicDecode
            this.oneChannel.subChannelData.subsonicEncode = subsonicEncode
          })
        }
      },
      validateOneChannel() {
        if (!this.channelSettingComp) {
          return Promise.reject()
        }
        return this.channelSettingComp.validate()
      },

      // 加密配置
      secretKeyNameSortMethod(a, b) {
        return a.keyName > b.keyName ? 1 : a.keyName < b.keyName ? -1 : 0
      },
      encryptKeyNameChanged(encryptItem) {
        if (!encryptItem) {
          return
        }
        if (!encryptItem.keyName) {
          encryptItem.keyName = this.$t('writeFreq.encryptedList') + ' ' + (encryptItem.keyId + 1)
        }
      },
      replaceInvalidByte(value, reg) {
        // 不是正则或字符串，则返回原数据
        if (reg instanceof RegExp || typeof reg === 'string') {
          return value.replace(reg, '')
        }

        return value
      },
      // 检测加密密钥输入的字符是否符合规则
      encryptKeyValueInput(encryptItem) {
        if (!encryptItem) {
          return
        }

        encryptItem.keyValue = this.replaceInvalidByte(encryptItem.keyValue, /[^0-9A-Za-z]/)
      },
      encryptKeyValueChanged(encryptItem) {
        if (!encryptItem) {
          return
        }
        // 密钥不存在，自动生成新密钥
        if (!encryptItem.keyValue) {
          encryptItem.keyValue = this.createEncryptValue().toUpperCase()
          return
        }

        // 判断密钥是否完整，密钥前自动填充0
        if (encryptItem.keyValue.length < 10) {
          encryptItem.keyValue = encryptItem.keyValue.padStart(10, '0')
        }
      },
      nextId(usedId, limit = 32) {
        let id = 0
        while (id < limit) {
          if (!usedId.includes(id)) {
            return id
          }
          id++
        }
        return id
      },
      deleteEncryptItem(row, index) {
        if (this.encryptList.includes(row)) {
          this.encryptList.splice(index, 1)
        }
      },
      addEncryptItem() {
        if (this.encryptList.length >= this.encryptListLimit) {
          return
        }
        const oneEncryptOption = this.createEncryptItem()
        this.encryptList.push(oneEncryptOption)
      },
      createEncryptValue(radix = 16) {
        return Math.random().toString(radix).slice(2, 12)
      },
      createEncryptItem() {
        const oneEncryptOption = cloneDeep(OneEncryptOption)
        const usedId = (this.encryptList || []).map(item => item.keyId)
        const keyId = this.nextId(usedId)
        oneEncryptOption.keyId = keyId
        oneEncryptOption.keyName = this.$t('writeFreq.encryptedList') + ' ' + (keyId + 1)
        oneEncryptOption.keyValue = this.createEncryptValue().toUpperCase()
        return oneEncryptOption
      },

      // 按键设置
      getKeyName(index) {
        const names = {
          0: this.$t('writeFreq.customKey', { name: 'P1' }),
          1: this.$t('writeFreq.customKey', { name: 'P2' }),
        }
        return names[index] || ''
      },
      syncLongPressDefine(row) {
        // 如果短按是紧急模式开启,则长按自动设置为紧急模式关闭
        if (row.short === ButtonKeys.WARNING_ON) {
          row.long = ButtonKeys.WARNING_OFF
        } else if (row.long === ButtonKeys.WARNING_OFF) {
          row.long = ButtonKeys.NONE
        }
      },
      getSoftKeyFuncDefine(type = 0) {
        let unusedList = []
        switch (type) {
          case 0:
            unusedList = [ButtonKeys.WARNING_OFF, ButtonKeys.LONGMONI]
            break
          case 1:
            unusedList = [ButtonKeys.WARNING_ON]
            break
        }
        return this.softKeyFuncDefine.filter(item => {
          return !unusedList.includes(item.value)
        })
      },

      // 通讯录
      getSelectedAddress(id) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.id === id) {
            return item
          }
        }

        return undefined
      },
      getSoftKeyCallTypeList(data) {
        const list = []
        const address = this.getSelectedAddress(data.callId)
        if (!address) {
          return []
        }
        const hasSms = this.smsContent.length > 0
        if (hasSms) {
          list.push(SoftKeyCallType[SoftKeyCallType.MSG])
        }
        if (address.callType === CallType.SINGLE) {
          list.push(SoftKeyCallType[SoftKeyCallType.SINGLE])
          list.push(SoftKeyCallType[SoftKeyCallType.TIP])
          if (data.callType === SoftKeyCallType.GROUP) {
            data.callType = SoftKeyCallType.SINGLE
          }
        } else {
          list.push(SoftKeyCallType[SoftKeyCallType.GROUP])
        }

        return list.map((key) => {
          return {
            label: this.$t(`writeFreq.softKeyCallType.${key}`),
            value: SoftKeyCallType[key],
          }
        })
      },
      detectButtonDefined() {
        for (let i = 0; i < this.buttonDefined.singleKeyCall.length; i++) {
          const item = this.buttonDefined.singleKeyCall[i]
          // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
          // 从缓存通讯录中查找的通讯录记录不在新的通讯录列表中，则重置按键功能
          const contactCache = this.getAddressBookFromCache(item.callId)
          if (!contactCache) {
            this.resetButtonDefined(item)
            continue
          }
          const contact = this.getAddressByDmrId(contactCache.dmrId)
          if (!contact) {
            this.resetButtonDefined(item)
            continue
          }
          // 如果存在但ID已经变更，则更新按键功能通讯录ID
          if (contactCache.id !== contact.id) {
            item.callId = contact.id
          }
        }
      },
      selectAddressBooks(books) {
        this.addressBookCache = cloneDeep(this.selectedAddressBook)
        this.selectedAddressBook = books
        // 通讯录变化时，检测按键定义中单键呼叫功能的设置
        this.detectButtonDefined()
      },

      // 电话簿
      selectPhoneBooks(books) {
        this.phoneBook = books
      },
      getOriginAddressBook(id) {
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.id === id) {
            return item
          }
        }
        return undefined
      },
      getAddressNameByDmrId(dmrId) {
        // 从读取回来的通讯录中查找对应的dmrId的通讯录名称
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.dmrId === dmrId) {
            return item.name
          }
        }

        // 在通讯录中无法找到数据，则从本地的数据中查找
        const org = bfglob.gorgData.getDataByIndex(dmrId)
        return org ? org.orgShortName : ''
      },
      getSelectedAddressByDmrId(dmrId) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item
          }
        }

        return undefined
      },
      getDefaultAddress(dmrId) {
        const address = this.getSelectedAddressByDmrId(dmrId)
        return address ? address.id : 0xFFFF
      },
      resetButtonDefined(target) {
        target.callId = 0xFFFF
        target.callType = SoftKeyCallType.GROUP
        target.smsId = 0xFF

        return target
      },
      getAddressBookFromCache(addrId) {
        for (let i = 0; i < this.addressBookCache.length; i++) {
          const book = this.addressBookCache[i]
          if (book.id === addrId) {
            return book
          }
        }
        return undefined
      },
      getAddressByDmrId(dmrId) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item
          }
        }
        return undefined
      },
      getSmsById(msgId) {
        for (let i = 0; i < this.smsContent.length; i++) {
          const sms = this.smsContent[i]
          if (sms.msgId === msgId) {
            return sms
          }
        }
        return undefined
      },
      detectButtonDefinedFromSmsChange() {
        for (let i = 0; i < this.buttonDefined.singleKeyCall.length; i++) {
          const item = this.buttonDefined.singleKeyCall[i]
          if (item.callType !== SoftKeyCallType.MSG) {
            continue
          }
          // 如果按键定义中的短信ID对应的短信内容已经不存在，则按键功能短信ID重置为默认值
          const sms = this.getSmsById(item.smsId)
          if (!sms) {
            // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
            const contact = this.getSelectedAddress(item.callId)
            if (!contact) {
              this.resetButtonDefined(item)
              continue
            }
            item.smsId = 0xFF

            // 重置按键功能类型参数
            if (contact.callType === CallType.SINGLE) {
              item.callType = SoftKeyCallType.SINGLE
            } else {
              item.callType = SoftKeyCallType.GROUP
            }
          }
        }
      },

      /* 菜单树节点操作功能 */
      updateNodeTitleFromLocaleChange() {
        const updateNodeTitle = (nodeData) => {
          const node = this.menuTreeRef.getNodeByKey(nodeData.key)
          if (!node) { return }
          node.setTitle(nodeData.title)
          if (nodeData.children) {
            for (let i = 0; i < nodeData.children.length; i++) {
              updateNodeTitle(nodeData.children[i])
            }
          }
        }
        for (let i = 0; i < this.menuTreeSource.length; i++) {
          updateNodeTitle(this.menuTreeSource[i])
        }
      },
      treeLoaded() {
        bftree.gotoNodeLocation(this.menuTreeId, this.menuTreeBaseNodeKeys[1], { isActive: true })
      },
      menuTreeNodeClick(event, data) {
        const node = data.node
        if (!node) {
          return
        }

        lastClickNodeKey = node.key
        const keys = this.menuTreeBaseNodeKeys
        const cNodeKeys = this.hasChildrenNodeKeys
        let [parentNodeKey, dataId] = node.key.split(':')
        this.selectMenu = parentNodeKey
        // 没有数据的ID，为基础的菜单节点
        if (typeof dataId === 'undefined') {
          // 再判断是否为动态数据节点的父节点，如果是则该节点不显示组件
          if (cNodeKeys.includes(parentNodeKey)) {
            this.selectMenu = ''
          }
          // 如果是信道基础节点，则需要加载显示一级区域
          if (parentNodeKey === keys[26]) {
            this.selectMenu = parentNodeKey
          }
        } else {
          dataId = parseInt(dataId)
          switch (parentNodeKey) {
            // 数字紧急报警
            case keys[18]:
              this.currDigitalAlertId = dataId
              break
            // 通讯录群组
            case keys[19]:
              this.currAddressBookGroupId = dataId
              break
            // 一级区域数据
            case keys[23]:
              this.currZoneRootId = dataId
              break
            // 二级区域数据
            case keys[24]:
              this.currZoneParentId = dataId
              break
            // 三级区域数据
            case keys[25]:
              this.currZoneLeafId = dataId
              break
            // 扫描
            case keys[260]:
              if (dataId === this.oneChannel.chId) {
                this.setChannelData(this.channelsIndex[dataId])
              } else {
                this.validateOneChannel()
                  .then(() => {
                    this.currChannelId = dataId
                    this.setChannelData(this.channelsIndex[dataId])
                  })
                  .catch(() => {
                    this.zoneDataDblclick(`${this.menuTreeBaseNodeKeys[260]}:${this.oneChannel.chId}`)
                  })
              }
              break
            // 扫描
            case keys[27]:
              this.currScanGroupId = dataId
              break
            // 漫游
            case keys[28]:
              this.currRoamGroupId = dataId
              break
            // 救援信道
            case keys[39]:
              this.sosChannelId = dataId
              break
            default:
          }
        }
      },
      contextmenuBeforeOpen(event, ui) {
        const menuTreeRef = this.menuTreeRef
        const node = $.ui.fancytree.getNode(ui.target)
        if (!node) {
          return false
        }

        // 判断哪个节点有右键菜单功能，没有的返回false以阻止打开右键菜单
        const [nodeKey, dataId] = node.key.split(':')
        const cNodeKeys = this.hasChildrenNodeKeys
        if (!cNodeKeys.includes(nodeKey)) {
          return false
        }

        node.setActive()
        const keys = this.menuTreeBaseNodeKeys
        const dataNotExist = typeof dataId === 'undefined'
        const status = {
          add: true,
          delete: true,
        }
        let contextmenu = this.menuTreeContextmenu
        switch (nodeKey) {
          // 数字紧急报警
          case keys[18]:
            if (dataNotExist) {
              status.add = this.digitalAlertList.length < this.digitalAlertListLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.digitalAlertList.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          // 通讯录群组
          case keys[19]:
            if (dataNotExist) {
              status.add = this.addressBookGroup.length < this.addressBookGroupLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.addressBookGroup.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          // 信道和区域数据以系统内数据为准，不支持右键菜单
          case keys[23]:
          case keys[24]:
          case keys[25]:
          case keys[26]:
            return false
          // 扫描
          case keys[27]:
            if (dataNotExist) {
              status.add = this.scanGroup.length < this.scanGroupLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.scanGroup.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          // 漫游
          case keys[28]:
            if (dataNotExist) {
              status.add = this.roamGroup.length < this.roamGroupLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.roamGroup.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          default:
        }
      },
      contextmenuSelect(event, ui) {
        const node = $.ui.fancytree.getNode(ui.target)
        if (!node) {
          return
        }

        const cmd = parseInt(ui.cmd)
        const keys = this.menuTreeBaseNodeKeys
        const [nodeKey, dataId] = node.key.split(':')
        switch (nodeKey) {
          // 数字紧急报警
          case keys[18]:
            this.digitalAlertContextmenuEvent(cmd, nodeKey, dataId)
            break
          // 通讯录群组
          case keys[19]:
            this.addressBookGroupContextmenuEvent(cmd, nodeKey, dataId)
            break
          // 扫描
          case keys[27]:
            this.scanGroupContextmenuEvent(cmd, nodeKey, dataId)
            break
          // 漫游
          case keys[28]:
            this.roamGroupContextmenuEvent(cmd, nodeKey, dataId)
            break
          default:
        }
      },
      manuallyTriggerNodeClick(node) {
        if (!node || !node.tr) {
          return
        }

        node.tr.click()
      },
      updateViewport() {
        this.menuTreeRef.updateViewport()
      },
      getNodeByKey(key) {
        return this.menuTreeRef.getNodeByKey(key)
      },
      removeNode(key) {
        return this.menuTreeRef.removeNode(key)
      },
      updateNodeTitle({
        key,
        title,
        data,
      }) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }

        node.title = title
        node.data.origin = data
        node.renderTitle()
      },
      addNodeChildren(parentNode, children) {
        return this.menuTreeRef.addNodeChildren(parentNode, children)
      },
      gotoMenuNode(key) {
        bftree.gotoNodeLocation(this.menuTreeId, key, { isActive: true })
      },

      // 数字紧急报警节点操作
      deleteDigitalAlertNode(data) {
        const key = `${this.menuTreeBaseNodeKeys[18]}:${data.id}`
        this.removeNode(key)
      },
      addDigitalAlertNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[18]
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createDigitalAlertNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      createDigitalAlertNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[18]
        const key = `${parentNodeKey}:${data.id}`

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      updateDigitalAlertNode(data) {
        this.updateNodeTitle({
          key: `${this.menuTreeBaseNodeKeys[18]}:${data.id}`,
          title: data.name,
          data,
        })
      },
      loadDigitalAlertNode() {
        const parentNodeKey = this.menuTreeBaseNodeKeys[18]
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          return
        }
        const children = []
        for (let i = 0; i < this.digitalAlertList.length; i++) {
          const data = this.digitalAlertList[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.id}`)
          if (node) {
            this.updateDigitalAlertNode(data)
            continue
          }
          children.push(this.createDigitalAlertNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addDigitalAlertData() {
        if (!this.digitalAlertComp) {
          return false
        }
        const data = this.digitalAlertComp.addDigitalAlert()
        this.addDigitalAlertNode(data)
      },
      deleteDigitalAlertData(nodeKey, dataId) {
        const dataLen = this.digitalAlertList.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.digitalAlertList[i]
          if (data.id !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteDigitalAlertNode(data)
          this.digitalAlertList.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = (i === dataLen - 1) ? 0 : i
          let nextData = this.digitalAlertList[nextDataIndex]
          if (!nextData) {
            nextData = this.digitalAlertList[0]
          }
          this.currDigitalAlertId = nextData.id
          const parentNodeKey = this.menuTreeBaseNodeKeys[18]
          const nextNodeKey = `${parentNodeKey}:${nextData.id}`
          this.gotoMenuNode(nextNodeKey)
          break
        }
      },
      digitalAlertContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addDigitalAlertData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteDigitalAlertData(nodeKey, dataId)
            break
          default:
        }
      },

      // 通讯录群组节点操作
      deleteAddressBookGroupNode(data) {
        const key = `${this.menuTreeBaseNodeKeys[19]}:${data.id}`
        this.removeNode(key)
      },
      addAddressBookGroupNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[19]
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createAddressBookGroupNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      updateAddressBookGroupNode(data) {
        this.updateNodeTitle({
          key: `${this.menuTreeBaseNodeKeys[19]}:${data.id}`,
          title: data.name,
          data,
        })
      },
      createAddressBookGroupNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[19]
        const key = `${parentNodeKey}:${data.id}`

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadAddressBookGroupNode() {
        const parentNodeKey = this.menuTreeBaseNodeKeys[19]
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          return
        }
        const children = []
        for (let i = 0; i < this.addressBookGroup.length; i++) {
          const data = this.addressBookGroup[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.id}`)
          if (node) {
            this.updateAddressBookGroupNode(data)
            continue
          }
          children.push(this.createAddressBookGroupNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addAddressBookGroupData() {
        if (!this.addressBookGroupComp) {
          return false
        }
        const data = this.addressBookGroupComp.addOneBookGroup()
        this.addAddressBookGroupNode(data)
      },
      deleteAddressBookGroupData(nodeKey, dataId) {
        const dataLen = this.addressBookGroup.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.addressBookGroup[i]
          if (data.id !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteAddressBookGroupNode(data)
          this.addressBookGroup.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = (i === dataLen - 1) ? 0 : i
          let nextData = this.addressBookGroup[nextDataIndex]
          if (!nextData) {
            nextData = this.addressBookGroup[0]
          }
          this.currAddressBookGroupId = nextData.id
          const parentNodeKey = this.menuTreeBaseNodeKeys[19]
          const nextNodeKey = `${parentNodeKey}:${nextData.id}`
          this.gotoMenuNode(nextNodeKey)

          break
        }
      },
      addressBookGroupContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addAddressBookGroupData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteAddressBookGroupData(nodeKey, dataId)
            break
          default:
        }
      },

      // 扫描组节点操作
      deleteScanGroupNode(data) {
        const key = `${this.menuTreeBaseNodeKeys[27]}:${data.scanId}`
        this.removeNode(key)
      },
      addScanGroupNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[27]
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createScanGroupNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      updateScanGroupNode(data, node) {
        this.updateNodeTitle({
          key: `${this.menuTreeBaseNodeKeys[27]}:${data.scanId}`,
          title: data.name,
          data,
        })
      },
      createScanGroupNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[27]
        const key = `${parentNodeKey}:${data.scanId}`

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadScanGroupNode() {
        const parentNodeKey = this.menuTreeBaseNodeKeys[27]
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          bfglob.console.warn('扫描组节点不存在')
          return
        }
        const children = []
        for (let i = 0; i < this.scanGroup.length; i++) {
          const data = this.scanGroup[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.scanId}`)
          if (node) {
            this.updateScanGroupNode(data)
            continue
          }
          children.push(this.createScanGroupNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addScanGroupData() {
        if (!this.scanGroupComp) {
          return false
        }
        const data = this.scanGroupComp.addOneScanGroup()
        this.addScanGroupNode(data)
      },
      deleteScanGroupData(nodeKey, dataId) {
        const dataLen = this.scanGroup.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.scanGroup[i]
          if (data.scanId !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteScanGroupNode(data)
          this.scanGroup.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = (i === dataLen - 1) ? 0 : i
          let nextData = this.scanGroup[nextDataIndex]
          if (!nextData) {
            nextData = this.scanGroup[0]
          }
          this.currScanGroupId = nextData.scanId
          const parentNodeKey = this.menuTreeBaseNodeKeys[27]
          const nextNodeKey = `${parentNodeKey}:${nextData.scanId}`
          this.gotoMenuNode(nextNodeKey)
          break
        }
      },
      scanGroupContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addScanGroupData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteScanGroupData(nodeKey, dataId)
            break
          default:
        }
      },

      // 漫游组节点操作
      deleteRoamGroupNode(data) {
        const key = `${this.menuTreeBaseNodeKeys[28]}:${data.roamId}`
        this.removeNode(key)
      },
      addRoamGroupNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[28]
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createRoamGroupNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      updateRoamGroupNode(data, node) {
        this.updateNodeTitle({
          key: `${this.menuTreeBaseNodeKeys[28]}:${data.roamId}`,
          title: data.name,
          data,
        })
      },
      createRoamGroupNode(data) {
        const parentNodeKey = this.menuTreeBaseNodeKeys[28]
        const key = `${parentNodeKey}:${data.roamId}`

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadRoamGroupNode() {
        const parentNodeKey = this.menuTreeBaseNodeKeys[28]
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          bfglob.console.warn('漫游组节点不存在')
          return
        }
        const children = []
        for (let i = 0; i < this.roamGroup.length; i++) {
          const data = this.roamGroup[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.roamId}`)
          if (node) {
            this.updateRoamGroupNode(data)
            continue
          }
          children.push(this.createRoamGroupNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addRoamGroupData() {
        if (!this.roamGroupComp) {
          return false
        }
        const data = this.roamGroupComp.addOneRoamGroup()
        this.addRoamGroupNode(data)
      },
      deleteRoamGroupData(nodeKey, dataId) {
        const dataLen = this.roamGroup.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.roamGroup[i]
          if (data.roamId !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteRoamGroupNode(data)
          this.roamGroup.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = (i === dataLen - 1) ? 0 : i
          let nextData = this.roamGroup[nextDataIndex]
          if (!nextData) {
            nextData = this.roamGroup[0]
          }
          this.currRoamGroupId = nextData.roamId
          const parentNodeKey = this.menuTreeBaseNodeKeys[28]
          const nextNodeKey = `${parentNodeKey}:${nextData.roamId}`
          bftree.gotoNodeLocation(this.menuTreeId, nextNodeKey, { isActive: true })

          break
        }
      },
      roamGroupContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addRoamGroupData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteRoamGroupData(nodeKey, dataId)
            break
          default:
        }
      },

      /* 同步界面表单数据 */
      asyncDataList(originList, dataList, indexList, key = 'id', mapCallBack) {
        const needDelList = []
        const list = originList.map(item => {
          if (typeof mapCallBack === 'function') {
            item = mapCallBack(item)
          }
          const cache = indexList[item[key]]
          if (cache) {
            needDelList.push(cache)
          }
          return merge(cache, item)
        })
        while (needDelList.length) {
          dataList.splice(dataList.indexOf(needDelList.pop()), 1)
        }
        return dataList.concat(list)
      },
      asyncDeviceWriteInfo(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, res.result[0])
        // this.saveDefaultFrequency(this.deviceWriteInfo.minFrequency)
      },
      asyncIdentityInfo(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.identityInfo = merge(this.identityInfo, settings)

        // 显示序列号
        this.deviceWriteInfo['serizeNumber'] = this.identityInfo.serizeNumber
      },
      asyncGeneralSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.generalSettings = merge(this.generalSettings, settings)
      },
      asyncButtonsDefine(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        // 只有两个快捷键
        settings.sideKey.length = 2
        this.buttonDefined = merge(this.buttonDefined, settings)
      },
      asyncShortMessage(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        for (let i = 0; i < res.result.length; i++) {
          const sms = res.result[i]
          const index = this.smsContent.findIndex(item => item.msgId === sms.msgId)
          if (index === -1) {
            this.smsContent.push(sms)
          } else {
            this.smsContent[index] = sms
          }
        }
      },
      asyncEncryptConfig(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.encryptConfig = merge(this.encryptConfig, settings)
      },
      asyncEncryptList(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.encryptList = this.asyncDataList(res.result, this.encryptList, this.encryptListIndex, 'keyId')
      },
      asyncMenuSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.menuSettings = merge(this.menuSettings, settings)
      },
      asyncSignalingSystemSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.signalingSystem = merge(this.signalingSystem, settings)
      },
      asyncAlertConfigSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.alertConfig = merge(this.alertConfig, settings)
      },
      asyncDigitalAlertList(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        this.digitalAlertList = this.asyncDataList(res.result, this.digitalAlertList, this.digitalAlertListIndex)
        if (this.digitalAlertList.length && this.currDigitalAlertId < 0) {
          this.currDigitalAlertId = 0
        }
        this.loadDigitalAlertNode()
      },
      asyncAddressBookGroup(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const mapCb = item => {
          item.dmrIdList = item.dmrIdList || []
          return item
        }
        this.addressBookGroup = this.asyncDataList(res.result, this.addressBookGroup, this.addressBookGroupIndex, 'id',
          mapCb,
        )
        // 同步生成节点
        this.loadAddressBookGroupNode()
      },
      asyncAddressBook(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        // 同步本地通讯录树
        if (this.addressBookComp) {
          this.addressBookComp.asyncNodeSelectStatus(res.result)
        }
        this.originAddressBook = this.originAddressBook.concat(res.result)
        // 关联群组数据
        res.result.forEach(item => {
          const data = this.addressBookGroupIndex[item.groupId]
          if (data) {
            data.dmrIdList.push(item.dmrId)
          }
        })
      },
      asyncPhoneBook(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 保存本地数据
        for (let i = 0; i < res.result.length; i++) {
          const pb = res.result[i]
          const index = this.phoneBook.findIndex(item => item.phoneId === pb.phoneId)
          if (index === -1) {
            this.phoneBook.push(pb)
          } else {
            this.phoneBook[index] = pb
          }
        }
        if (this.phoneBookComp) {
          this.phoneBookComp.asyncNodeSelectStatus(res.result)
        }
      },
      asyncRxGroup(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 处理读取回来的接收组数据
        if (this.receiveGroupComp) {
          this.receiveGroupComp.asyncRxGroup(res.result)
        }
      },
      asyncZoneRootDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        this.zoneRootDataList = this.asyncDataList(res.result, this.zoneRootDataList, this.zoneRootDataIndex, 'zoneId')
        this.loadZoneRootNodes(res.result)
      },
      asyncZoneParentDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        this.zoneParentDataList = this.asyncDataList(res.result, this.zoneParentDataList, this.zoneParentDataIndex,
          'zoneId',
        )
        this.loadZoneParentNodes(res.result)
      },
      getZoneChIdList(zoneData) {
        if (!zoneData || !Array.isArray(zoneData.usedList)) {
          return []
        }
        // 每个区域下只有64个信道，信道使用标志右移后按位与0x01,结果为1则启用该信道
        let i = 0
        const chIdList = []
        const bits = zoneData.usedFlag.toString(2)
        while (i < bits.length) {
          if (parseInt(bits[i]) === 0x01) {
            chIdList.push(zoneData.usedList[i])
          }
          i++
        }
        return chIdList
      },
      setZoneChFlag(chIdList) {
        if (!Array.isArray(chIdList) || !chIdList.length) {
          return 0
        }
        // 生成二进制的字符串，
        let bitStr = ''
        let i = 1
        while (i <= chIdList.length) {
          bitStr = bitStr.padStart(i, 1)
          i++
        }
        // 从二进制中生成long类型
        return Long.fromString(bitStr, true, 2)
      },
      asyncZoneLeafDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        // 保留区域实际的信道id列表
        const syncCb = (item) => {
          item.usedList = this.getZoneChIdList(item)
          return item
        }
        this.zoneLeafDataList = this.asyncDataList(res.result, this.zoneLeafDataList, this.zoneLeafDataIndex, 'zoneId',
          syncCb,
        )
        this.loadZoneLeafNodes(res.result)
      },
      asyncChannelDataList(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.channelDataList = this.asyncDataList(res.result, this.channelDataList, this.channelsIndex, 'chId')
        this.loadChannelNodes(res.result)
      },
      asyncScanGroup(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const mapCb = item => {
          item.membersList = item.membersList.slice(0, item.memberCount)
          return item
        }
        this.scanGroup = this.asyncDataList(res.result, this.scanGroup, this.scanGroupIndex, 'scanId', mapCb)
        if (this.scanGroup.length && this.currScanGroupId < 0) {
          this.currScanGroupId = 0
        }
        this.loadScanGroupNode()
      },
      asyncRoamGroup(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const mapCb = item => {
          item.roamChList = item.roamChList.slice(0, item.roamChCount)
          return item
        }
        this.roamGroup = this.asyncDataList(res.result, this.roamGroup, this.roamGroupIndex, 'roamId', mapCb)
        if (this.roamGroup.length && this.currRoamGroupId < 0) {
          this.currRoamGroupId = 0
        }
        this.loadRoamGroupNode()
      },
      asyncPatrolConfig(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.patrolConfig = merge(this.patrolConfig, settings)
      },
      asyncEmergencyAlarm(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.emergencyAlarm = merge(this.emergencyAlarm, settings)
      },
      syncSosConfig(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.sosCfg = merge(this.sosCfg, settings)
      },
      syncSosChannels(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.sosChannelList = merge(this.sosChannelList, res.result)
        this.sosChannel = this.sosChannelList[this.sosChannelId]
      },
      asyncLocalConfig(data) {
        switch (data.type) {
          // 设备信息
          case 1:
            this.asyncDeviceWriteInfo(data)
            break
          // 身份信息
          case 2:
            this.asyncIdentityInfo(data)
            break
          // 总体设置
          case 4:
            this.asyncGeneralSettings(data)
            break
          // 5: 按键设置
          case 5:
            this.asyncButtonsDefine(data)
            break
          // 短信
          case 6:
            this.asyncShortMessage(data)
            break
          // 加密配置
          case 7:
            this.asyncEncryptConfig(data)
            break
          // 密钥列表
          case 8:
            this.asyncEncryptList(data)
            break
          // 菜单设置
          case 14:
            this.asyncMenuSettings(data)
            break
          //  信令系统
          case 15:
            this.asyncSignalingSystemSettings(data)
            break
          //  警报设置
          case 16:
            this.asyncAlertConfigSettings(data)
            break
          // 数字警报
          case 18:
            this.asyncDigitalAlertList(data)
            break
          // 数字通讯录群组列表
          case 19:
            this.asyncAddressBookGroup(data)
            break
          // 数字通信录
          case 20:
            this.asyncAddressBook(data)
            break
          // 电话簿
          case 21:
            this.asyncPhoneBook(data)
            break
          // 接收组列表
          case 22:
            this.asyncRxGroup(data)
            break
          // 一级区域
          case 23:
            this.asyncZoneRootDataList(data)
            break
          // 二级区域
          case 24:
            this.asyncZoneParentDataList(data)
            break
          // 三级区域
          case 25:
            this.asyncZoneLeafDataList(data)
            break
          // 信道数据
          case 26:
            this.asyncChannelDataList(data)
            break
          // 扫描组列表
          case 27:
            this.asyncScanGroup(data)
            break
          // 漫游组列表
          case 28:
            this.asyncRoamGroup(data)
            break
          // 巡查系统配置
          case 29:
            this.asyncPatrolConfig(data)
            break
          // 紧急报警
          case 31:
            this.asyncEmergencyAlarm(data)
            break
          // 救援信道配置
          case 38:
            this.syncSosConfig(data)
            break
          // 救援信道
          case 39:
            this.syncSosChannels(data)
            break
        }
      },
      readDataConfig() {
        if (!this.canRead() || this.isReading) {
          return
        }
        // 开始读取数据提示
        bfNotify.messageBox(this.$t('msgbox.startReading'))
        if (this.selectedDeviceDmrId) {
          this.selectedDeviceDmrId = ''
        } else {
          this.clearDeviceDataConfig()
        }
        this.sendAuthentication()
      },

      /* 写入数据操作 */
      checkDeviceFreqValid(deviceInfo) {
        if (!deviceInfo || !Array.isArray(deviceInfo.result)) {
          return false
        }
        const info = deviceInfo.result[0]
        if (!info || !Array.isArray(info.frequencyRange)) {
          return false
        }
        if (info.frequencyRange.filter(item => {
          return this.deviceWriteInfo.frequencyRange.some(v => {
            return item.min !== v.min || item.max !== v.max
          })
        }).length === 0) {
          this.showWriteInFailInfo(this.$t('writeFreq.frequencyRangeError'))
          this.setWriteEndStatus()
          return false
        }
        return true
      },
      getBeforeEncodeData(type) {
        switch (type) {
          // 设备信息
          case 1:
            return cloneDeep(this.deviceWriteInfo)
          // 身份信息
          case 2:
            return cloneDeep(this.identityInfo)
          // 通讯密码
          case 3:
            return cloneDeep(this.passwordInfo)
          // 总体设置
          case 4:
            return cloneDeep(this.generalSettings)
          // 按键设置
          case 5:
            return cloneDeep(this.buttonDefined)
          // 短信
          case 6:
            return cloneDeep(this.smsContent)
          // 加密配置
          case 7:
            return cloneDeep(this.encryptConfig)
          // 加密列表
          case 8:
            return cloneDeep(this.encryptList)
          // 菜单设置
          case 14:
            return cloneDeep(this.menuSettings)
          // 信令系统
          case 15:
            return cloneDeep(this.signalingSystem)
          // 警报配置
          case 16:
            return cloneDeep(this.alertConfig)
          // 数字紧急报警
          case 18:
            return cloneDeep(this.digitalAlertList)
          // 通讯录群组
          case 19:
            return cloneDeep(this.addressBookGroup)
          // 通讯录
          case 20:
            return cloneDeep(this.selectedAddressBook)
          // 电话簿
          case 21:
            return this.phoneBook.filter((item) => {
              return !!bfglob.gphoneBook.getDataByIndex(item.phoneNo)
            })
          // 接收组列表
          case 22:
            return this.receiveGroupComp ? this.receiveGroupComp.getWriteRxGroupList() : []
          // 一级区域
          case 23:
            return cloneDeep(this.zoneRootDataList)
          // 二级区域
          case 24:
            return cloneDeep(this.zoneParentDataList)
          // 三级区域
          case 25:
            return cloneDeep(this.zoneLeafDataList)
          // 信道
          case 26:
            return cloneDeep(this.channelDataList).map(channel => {
              if (this.receiveGroupComp) {
                channel.subChannelData.receiveGroup = this.receiveGroupComp.getGroupIdByChId(channel.chId)
              } else {
                channel.subChannelData.receiveGroup = 0xFF
              }
              if (channel.chType === 1) {
                channel.subChannelData.subsonicDecode = this.encodeSubsonic(channel.subChannelData.subsonicDecode)
                channel.subChannelData.subsonicEncode = this.encodeSubsonic(channel.subChannelData.subsonicEncode)
              }
              return channel
            })
          // 扫描列表
          case 27:
            return cloneDeep(this.scanGroup)
          // 漫游列表
          case 28:
            return cloneDeep(this.roamGroup)
          // 配置
          case 29:
            return cloneDeep(this.patrolConfig)
          // 紧急报警
          case 31:
            return cloneDeep(this.emergencyAlarm)
          // 救援配置
          case 38:
            return cloneDeep(this.sosCfg)
          // 救援信道
          case 39:
            return cloneDeep(this.sosChannelList)
        }
      },
      validateAllRules() {
        return new Promise((resolve, reject) => {
          const validateList = [
            {
              ref: 'generalSettings',
              msg: this.$t('writeFreq.generalSettingsFormValidate'),
            },
            // { ref: 'digitAlarm', msg: this.$t('writeFreq.digitalAlarmFormValidate') },
            // { ref: 'scanGroup', msg: this.$t('writeFreq.scanGroupFormValidate') }
          ]
          // 信道列表中有数据时才需要验证
          if (this.channelDataList.length) {
            // if (!this.channelDataList.includes(this.channelData)) {
            //   this.channelData = this.channelDataList[0]
            // }
            // validateList.push({ ref: 'channelData', msg: this.$t('writeFreq.channelDataFormValidate') })
          }
          const iterator = validateList[Symbol.iterator]()

          const validate = (item) => {
            if (item.done) {
              resolve()
            }
            const {
              ref,
              msg,
            } = item.value
            this.formValidate(ref)
              .then(() => {
                validate(iterator.next())
              })
              .catch(() => {
                reject(msg)
              })
          }

          validate(iterator.next())
        })
      },
      writeInFrequency() {
        this.writeDataConfig()
      },

      exportConfig() {
        // 只导出设备的写频配置，不导出设备的接收组、信道数据、设备的dmrId等
        const dataStructType = [
          1, 2, 4, 5, 6, 7, 8, 14, 15, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 31,
        ]
        const jsonData = dataStructType.map((type) => {
          let config = this.getBeforeEncodeData(type)
          // 删除一些不需要导出的参数
          switch (type) {
            case 4:
              // 将常规设置中的设备dmrId和设备名称去掉
              delete config.deviceName
              delete config.dmrIdLabel
              delete config.intDmrId
              break
          }
          if (!Array.isArray(config)) {
            config = [config]
          }
          return { [type]: config }
        })
          .reduce((p, c) => {
            return Object.assign(p, c)
          }, {})

        this.exportData(jsonData)
      },
      newConfig() {
        this.selectedDeviceDmrId = ''
        this.clearDeviceDataConfig(true)
        // 清除设备频率信息
        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, {
          frequencyRange: [
            {
              min: 0,
              max: 0,
            },
            {
              min: 0,
              max: 0,
            },
          ],
        })
        // 常规设置
        this.generalSettings = cloneDeep(GeneralSettings)
        // 按键设置
        this.buttonDefined = cloneDeep(ButtonDefined)
        // 菜单设置
        this.menuSettings = cloneDeep(MenuSettings)
        // 信令系统
        this.signalingSystem = cloneDeep(SignalingSystem)
        this.initPatrolConfig(false)
        this.initEmergencyAlarm(false)
        this.initSmsData()
      },
      redrawViewport(retry = 0) {
        if (retry > 10) {
          return
        }
        this.menuTreeRef.updateViewport()
        setTimeout(() => {
          const viewport = this.menuTreeRef.getTree().viewport
          const count = this.menuTreeRef.getViewportCount()
          if (isNaN(viewport.count) || isNaN(viewport.start) || count !== viewport.count) {
            this.redrawViewport(++retry)
          }
        }, 0)
      },
    },
    computed: {
      currentChannelIdList() {
        return this.channelDataList.map(item => item.chId)
      },
      powerOnPwdTips() {
        return powerOnPwdTipsPng
      },
      // 0=5秒，间隔5秒，范围5-60秒，默认0
      intervalHonkingTime: {
        get() {
          return this.sosCfg.intervalHonkingTime * 5 + 5
        },
        set(val) {
          this.sosCfg.intervalHonkingTime = (val - 5) / 5
        },
      },
      // 0=500毫秒，间隔100毫秒，范围500-2000毫秒，默认1
      flashlightTime: {
        get() {
          return this.sosCfg.flashlightTime * 100 + 500
        },
        set(val) {
          this.sosCfg.flashlightTime = (val - 500) / 100
        },
      },
      // 0=3分钟，间隔1分钟，范围3-10分钟，默认0
      highPowerSosTime: {
        get() {
          return this.sosCfg.highPowerSosTime + 3
        },
        set(val) {
          this.sosCfg.highPowerSosTime = val - 3
        },
      },
      // 0=5分钟，间隔1分钟，范围5-15分钟，默认0
      middlePowerSosTime: {
        get() {
          return this.sosCfg.middlePowerSosTime + 5
        },
        set(val) {
          this.sosCfg.middlePowerSosTime = val - 5
        },
      },
      // 0=10分钟，间隔1分钟，范围10-30分钟，默认0
      lowPowerSosTime: {
        get() {
          return this.sosCfg.lowPowerSosTime + 10
        },
        set(val) {
          this.sosCfg.lowPowerSosTime = val - 10
        },
      },

      menuTreeRef() {
        return this.$refs[this.menuTreeId]
      },
      menuTreeBaseNodeKeys() {
        return generateEnumObject({
          1: 'TD910DeviceInfo',
          2: 'IdentityInfo',
          4: 'GeneralSettings',
          5: 'ButtonSettings',
          6: 'ShortMessage',
          7: 'EncryptSettings',
          8: 'EncryptList',
          14: 'MenuSettings',
          15: 'SignalingSystem',
          160: 'AlarmNode',
          16: 'AlertConfig',
          18: 'DigitalAlert',
          19: 'AddressGroup',
          20: 'DigitalAddress',
          21: 'PhoneBook',
          22: 'RxGroup',
          23: 'ZoneRoot',
          24: 'ZoneParent',
          25: 'ZoneLeaf',
          26: 'Channel',
          260: 'ChannelItem',
          27: 'Scan',
          28: 'Roam',
          29: 'PatrolSystem',
          31: 'EmergencyAlarm',
          38: 'SosCfg',
          39: 'SosChData',
        })
      },
      hasChildrenNodeKeys() {
        const keys = this.menuTreeBaseNodeKeys
        return [keys[18], keys[19], keys[23], keys[24], keys[25], keys[26], keys[27], keys[28]]
      },
      menuTreeSource() {
        const keys = this.menuTreeBaseNodeKeys
        return [
          {
            key: keys[1],
            title: this.$t('dialog.deviceInfo'),
            icon: false,
            folder: true,
          },
          {
            key: keys[4],
            title: this.$t('dialog.generalSetting'),
            icon: false,
            folder: true,
          },
          {
            key: keys[5],
            title: this.$t('dialog.buttonDefinition'),
            icon: false,
            folder: true,
          },
          {
            key: keys[6],
            title: this.$t('dialog.smsContent'),
            icon: false,
            folder: true,
          },
          {
            key: keys[7],
            title: this.$t('writeFreq.encryptionConfig'),
            icon: false,
            folder: true,
          },
          {
            key: keys[14],
            title: this.$t('dialog.menuSettings'),
            icon: false,
            folder: true,
          },
          {
            key: keys[15],
            title: this.$t('writeFreq.signalingSystem'),
            icon: false,
            folder: true,
          },
          {
            key: keys[160],
            title: this.$t('writeFreq.theAlarm'),
            icon: false,
            folder: true,
            expanded: true,
            children: [
              {
                key: keys[16],
                title: this.$t('writeFreq.alarmSetting'),
                icon: false,
                folder: true,
              },
              {
                key: keys[18],
                title: this.$t('writeFreq.digitalEmergencyAlarm'),
                icon: false,
                folder: true,
                expanded: true,
              },
            ],
          },
          {
            key: keys[20],
            title: this.$t('dialog.addressBook'),
            icon: false,
            folder: true,
          },
          {
            key: keys[19],
            title: this.$t('writeFreq.addressBookGroup'),
            icon: false,
            folder: true,
            expanded: true,
          },
          {
            key: keys[21],
            title: this.$t('dialog.phoneBook'),
            icon: false,
            folder: true,
          },
          {
            key: keys[22],
            title: this.$t('dialog.rxGroup'),
            icon: false,
            folder: true,
          },
          {
            key: keys[26],
            title: this.$t('dialog.channelSetting'),
            icon: false,
            folder: true,
            expanded: true,
          },
          {
            key: keys[27],
            title: this.$t('writeFreq.scan'),
            icon: false,
            folder: true,
            expanded: true,
          },
          {
            key: keys[28],
            title: this.$t('writeFreq.roaming'),
            icon: false,
            folder: true,
            expanded: true,
          },
          {
            key: keys[29],
            title: this.$t('dialog.patrolSystem'),
            icon: false,
            folder: true,
          },
          {
            key: keys[38],
            title: this.$t('writeFreq.sosRescueCfg'),
            icon: false,
            folder: false,
          },
          // {
          //   key: keys[39],
          //   title: this.$t('writeFreq.sosRescueChannel'),
          //   icon: false,
          //   folder: true,
          //   expanded: true,
          //   children: [
          //     {
          //       key: `${keys[39]}:0`,
          //       title: this.defaultChannelNames[0],
          //       icon: false,
          //       folder: true,
          //     },
          //     {
          //       key: `${keys[39]}:1`,
          //       title: this.defaultChannelNames[1],
          //       icon: false,
          //       folder: true,
          //       expanded: true,
          //     },
          //   ],
          // },
        ]
      },
      menuTreeOpts() {
        return {
          source: this.menuTreeSource,
          checkbox: false,
          click: this.menuTreeNodeClick,
        }
      },
      menuTreeContextmenu() {
        return [
          {
            title: this.$t('dialog.add'),
            cmd: MenuTreeContextmenu.ADD,
          },
          {
            title: this.$t('dialog.delete'),
            cmd: MenuTreeContextmenu.DELETE,
          },
        ]
      },
      menuTreeContextmenuOption() {
        return {
          menu: this.menuTreeContextmenu,
          beforeOpen: this.contextmenuBeforeOpen,
          select: this.contextmenuSelect,
        }
      },

      hasFreqRange() {
        // TD910有多个频率，frequencyRange为数组，[{},{}...]
        return this.deviceWriteInfo.frequencyRange.some(frequency => {
          return frequency.max > 0 && frequency.min > 0
        })
      },
      disWriteBtn() {
        return this.disReadBtn || !this.selectedDeviceDmrId || !this.hasFreqRange || this.selectedChannels.length === 0
      },
      disReadBtn() {
        return this.noQWebServer || this.noDevice || this.isWriting || this.isReading
      },
      noQWebServer() {
        return !this.QWebServer
      },

      showTD910DeviceInfo() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[1]
      },
      showGeneralSettings() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[4]
      },
      showButtonSettings() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[5]
      },
      showShortMessage() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[6]
      },
      showEncryptSettings() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[7]
      },
      showMenuSettings() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[14]
      },
      showSignalingSystem() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[15]
      },
      showAlertConfig() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[16]
      },
      showDigitalAlert() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[18]
      },
      showDigitalAddress() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[20]
      },
      showAddressGroup() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[19]
      },
      showPhoneBook() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[21]
      },
      showRxGroup() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[22]
      },
      showZoneRootData() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[26]
      },
      showZoneParentData() {
        return this.selectMenu.startsWith(this.menuTreeBaseNodeKeys[23])
      },
      showZoneLeafData() {
        return this.selectMenu.startsWith(this.menuTreeBaseNodeKeys[24])
      },
      showZoneLeafChannels() {
        return this.selectMenu.startsWith(this.menuTreeBaseNodeKeys[25])
      },
      showChannelItem() {
        return this.selectMenu.startsWith(this.menuTreeBaseNodeKeys[260])
      },
      showScan() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[27]
      },
      showRoam() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[28]
      },
      showPatrolSystem() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[29]
      },
      showSosSettings() {
        return this.selectMenu === this.menuTreeBaseNodeKeys[38]
      },
      showSosChannel() {
        return this.selectMenu.startsWith(this.menuTreeBaseNodeKeys[39])
      },

      addressBookCallTypes() {
        return CallType
      },

      generalSettingsRules() {
        const pwdRule = [
          validateRules.mustLength(['change', 'blur'], 6),
          validateRules.mustNumber(['change', 'blur']),
        ]

        return {
          deviceName: [
            validateRules.required(),
            validateRules.maxLen(['change', 'blur'], 16),
          ],
          powerOnPassword: pwdRule,
          channelConfigPassword: pwdRule,
        }
      },
      signalingSystemRules() {
        return {
          signalingPwd: [
            validateRules.mustLength('blur', 6),
            validateRules.mustNumber('blur'),
          ],
        }
      },
      savePowerModeList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: '1:1',
            value: 1,
          },
          {
            label: '1:2',
            value: 2,
          },
          {
            label: '1:3',
            value: 3,
          },
          {
            label: '1:4',
            value: 4,
          },
        ]
      },
      allowCallInstructionList() {
        // 呼叫允许指示 ,000 无  001 模拟  010 模拟和数字 011 数字
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('dialog.analog'),
            value: 1,
          },
          {
            label: this.$t('dialog.analogAndDigital'),
            value: 2,
          },
          {
            label: this.$t('dialog.digital'),
            value: 3,
          },
        ]
      },
      recordCompressionRatioList() {
        // 录音压缩比 0 不压缩, 1 3.5倍  当录音使能==0时不可用
        return [
          {
            label: this.$t('dialog.nonCompacting'),
            value: 0,
          },
          {
            label: '3.5',
            value: 1,
          },
        ]
      },
      muteAll() {
        return this.generalSettings.soundSettings.muteAll
      },
      sosEnable() {
        return this.sosCfg.config.enable
      },
      syncTime() {
        return this.generalSettings.syncTime
      },
      dmrIdLabel() {
        if (this.generalSettings.intDmrId === 0) {
          return ''
        }
        const dmrId = bfutil.int32Dmrid2Hex(this.generalSettings.intDmrId)
        return dmrId ? ` ${dmrId} / ${this.generalSettings.intDmrId}` : ''
      },
      month: {
        get() {
          return this.generalSettings.month + 1
        },
        set(val) {
          this.generalSettings.month = val - 1
        },
      },
      hours: {
        get() {
          return this.generalSettings.hour + this.generalSettings.timeZoneHour
        },
        set(val) {
          this.generalSettings.hour = val - this.generalSettings.timeZoneHour
        },
      },
      maxDate() {
        const dates = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
        return dates[this.generalSettings.month]
      },
      powerTransferEnable() {
        return !this.generalSettings.powerTransfer.enable
      },

      deviceInfoLabelPosition() {
        return this.isMobile ? 'top' : 'left'
      },
      buttonDefineLabelWidth() {
        return this.isEn ? '170px' : '130px'
      },
      softKeyFuncDefine() {
        return Object.keys(ButtonKeys)
          .filter((key) => {
            return this.$te(`writeFreq.softKeyFuncDefine.${key}`)
          })
          .map((key) => {
            return {
              label: this.$t(`writeFreq.softKeyFuncDefine.${key}`),
              value: ButtonKeys[key],
            }
          })
      },
      SoftKeyCallType() {
        return SoftKeyCallType
      },
      buttonDefineAddressList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFFFF,
          },
        ]
        return def.concat(this.selectedAddressBook
          .filter(address => address.dmrId !== bfglob.fullCallDmrId)
          .map(address => {
            return {
              label: address.name,
              value: address.id,
            }
          }),
        )
      },
      buttonDefineZoneRootList() {
        const defaultList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFFFF,
          },
        ]
        return defaultList.concat(this.zoneRootDataList.map(item => {
          return {
            label: item.zoneName,
            value: item.zoneId,
          }
        }))
      },
      buttonDefineZoneParentList() {
        return this.zoneParentDataList.map(item => {
          return {
            label: item.zoneName,
            value: item.zoneId,
            parent: item.rootId,
          }
        })
      },
      buttonDefineZoneLeafList() {
        return this.zoneLeafDataList.map(item => {
          return {
            label: item.zoneName,
            value: item.zoneId,
            parent: item.parentId,
          }
        })
      },
      buttonDefineChannelList() {
        return this.channelDataList.map(item => {
          return {
            label: item.chName,
            value: item.chId,
          }
        })
      },

      encryptListActiveWidth() { return this.isFR ? '120px' : '100px' },
      encryptEnable() {
        return this.encryptConfig.config.encryptEnable
      },
      menuHangTimeLabelWidth() {
        return this.isEn ? '160px' : '120px'
      },
      freqDisplayList() {
        // 信道显示模式，0 频率显示，1 信道显示，2 频率+信道显示
        return [
          {
            label: this.$t('dialog.freqDisplay'),
            value: 0,
          },
          {
            label: this.$t('dialog.chDisplay'),
            value: 1,
          },
          {
            label: this.$t('dialog.freqAndChDisplay'),
            value: 2,
          },
        ]
      },

      digitalAlertComp() {
        return this.$refs.digitalAlert
      },
      addressBookGroupComp() {
        return this.$refs.addressBookGroup
      },
      addressBookComp() {
        return this.$refs[this.addrBookTreeId]
      },
      phoneBookComp() {
        return this.$refs[this.phoneBookTreeId]
      },
      receiveGroupComp() {
        return this.$refs[this.refReceiveGroup]
      },
      scanGroupComp() {
        return this.$refs.scanGroup
      },
      roamGroupComp() {
        return this.$refs.roamGroup
      },
      rootZoneComp() {
        return this.$refs.rootZone
      },
      parentZoneComp() {
        return this.$refs.parentZone
      },
      leafZoneComp() {
        return this.$refs.leafZone
      },
      zoneLeafChannelComp() {
        return this.$refs.zoneLeafChannel
      },

      // 列表类型数据索引
      encryptListIndex() {
        return this.createDataListIndex(this.encryptList, 'keyId')
      },
      digitalAlertListIndex() {
        return this.createDataListIndex(this.digitalAlertList)
      },
      addressBookGroupIndex() {
        return this.createDataListIndex(this.addressBookGroup)
      },
      zoneRootDataIndex() {
        return this.createDataListIndex(this.zoneRootDataList, 'zoneId')
      },
      zoneParentDataIndex() {
        return this.createDataListIndex(this.zoneParentDataList, 'zoneId')
      },
      zoneLeafDataIndex() {
        return this.createDataListIndex(this.zoneLeafDataList, 'zoneId')
      },
      channelsIndex() {
        return this.createDataListIndex(this.channelDataList, 'chId')
      },
      scanGroupIndex() {
        return this.createDataListIndex(this.scanGroup, 'scanId')
      },
      roamGroupIndex() {
        return this.createDataListIndex(this.roamGroup, 'roamId')
      },

      isDChannel() {
        return this.oneChannel.chType === 0
      },
      isAChannel() {
        return this.oneChannel.chType === 1
      },
      isConnectNetworking() {
        const data = this.oneChannel.subChannelData
        if (!data || !data.networkConfig) {
          return false
        }

        if (this.isAChannel) {
          return false
        }
        return data.networkConfig.networking
      },
      sameFreq() {
        return this.oneChannel.rxFreq === this.oneChannel.txFreq
      },
      throughEnable() {
        return this.oneChannel.subChannelData?.timeSlotConfig?.throughEnable ?? false
      },
      onlyReceive() {
        return this.oneChannel.scanConfig.onlyReceive
      },
      disAutoScan() {
        return this.isConnectNetworking || this.oneChannel.scanConfig.ipSiteConnect || this.oneChannel.scanList === 0xFF
      },
      disAutoRoam() {
        return this.isConnectNetworking || !this.oneChannel.scanConfig.ipSiteConnect || this.oneChannel.scanList ===
          0xFF
      },
      disEncryptConfigEnable() {
        return !this.encryptConfig.config.encryptEnable
      },
      disEncryption() {
        return this.disEncryptConfigEnable || !this.oneChannel.subChannelData.encryptConfig.enable
      },
      disPlosive() {
        return this.onlyReceive || this.oneChannel.subChannelData.subsonicEncode === 0xFFFF
      },
      freqOffsetRules() {
        return []
      },
      channelRules() {
        // const defFreq = this.defaultFrequency
        // const minFrequency = this.deviceWriteInfo.minFrequency
        // const maxFrequency = this.deviceWriteInfo.maxFrequency
        // const min = minFrequency ? bfutil.frequencyMhz2Hz(minFrequency) : defFreq.value
        // const max = maxFrequency ? bfutil.frequencyMhz2Hz(maxFrequency) : defFreq.value + 80000000
        // const smg = `${minFrequency || bfutil.frequencyHz2Mhz(defFreq.value) || 0}~${maxFrequency ||
        // bfutil.frequencyHz2Mhz(defFreq.value + 80000000)}`
        // const frequency = [
        //   validateRules.required(['blur']),
        //   validateRules.range(['blur'], min, max, smg)
        // ]
        return {
          chName: [
            validateRules.required(['blur']),
          ],
          // rxFreq: frequency,
          // txFreq: frequency
        }
      },
      chTypeList() {
        // 暂时不支持兼容信道
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0,
          },
          {
            label: this.$t('dialog.analogChannel'),
            value: 1,
          },
          // { label: this.$t('writeFreq.digitalCompatibleAnalog'), value: 2 },
          // { label: this.$t('writeFreq.analogCompatibleDigital'), value: 3 }
        ]
      },
      bandwidthFlagList() {
        return [
          {
            label: this.$t('writeFreq.broadBand'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.narrowBand'),
            value: 1,
          },
        ]
      },
      chScanRoamGroupList() {
        const list = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
        ]
        if (this.oneChannel.scanConfig.ipSiteConnect) {
          return list.concat(this.roamGroup.map(item => {
            return {
              label: item.name,
              value: item.roamId,
            }
          }))
        }

        return list.concat(this.scanGroup.map((item) => {
          return {
            label: item.name,
            value: item.scanId,
          }
        }))
      },
      chTimeSlotCalList() {
        return [
          {
            label: this.$t('dataTable.fail'),
            value: 0,
          },
          {
            label: this.$t('dataTable.pass'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.firstChoice'),
            value: 2,
          },
        ]
      },
      slotModeList() {
        return [
          {
            label: '1',
            value: 0,
          },
          {
            label: '2',
            value: 1,
          },
          {
            label: this.$t('dialog.virtualCluster'),
            value: 2,
          },
        ]
      },
      virtualTimeSlotList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: '1',
            value: 1,
          },
          {
            label: '2',
            value: 2,
          },
        ]
      },
      preEmphasisList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.deEmphasisAndPreEmphasis'),
            value: 1,
          },
        ]
      },
      algorithmList() {
        // 0 异或(默认值 0) 1 增强异或 2 ARC4 3 AES256
        return [
          {
            label: this.$t('writeFreq.xor'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.enhancedXor'),
            value: 1,
          },
          {
            label: 'ARC4',
            value: 2,
          },
          {
            label: 'AES256',
            value: 3,
          },
        ]
      },
      encryptKeyList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
        ].concat(this.encryptList.map(item => {
          return {
            label: item.keyName,
            value: item.keyId,
          }
        }))
      },
      subtoneCodeDataList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFFFF,
          },
        ]
        const getList = (codes) => {
          return Object.keys(codes)
            .map((key) => {
              return {
                label: key,
                value: codes[key],
              }
            })
        }

        switch (this.oneChannel.chType) {
          case 0:
            return def
          case 1:
            return def.concat(getList(AnalogCodeData))
          default:
            return getList(AnalogDigitalCodeData)
        }
      },
      tailToneList() {
        return [
          {
            label: this.$t('writeFreq.noSubtone'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.standardPhase'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.nonstandardPhase'),
            value: 2,
          },
        ]
      },
      receiveGroupList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
        ]
        const rxGroupList = this.rxGroupList.map((item) => {
          return {
            label: item.groupName,
            value: item.groupId,
          }
        })
        return def.concat(rxGroupList)
      },
      defaultAddressList() {
        const cache = []
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFFFF,
          },
        ].concat(
          this.selectedAddressBook.concat(this.originAddressBook)
            .filter((address) => {
              if (cache.includes(address.id)) {
                return false
              }
              cache.push(address.id)
              return true
            })
            .map((address) => {
              return {
                label: address.name,
                value: address.id,
              }
            }),
        )
      },
      emergencySysIdList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xFF,
          },
        ]
        const digitAlarmList = this.digitalAlertList.map((item) => {
          return {
            label: item.name,
            value: item.id,
          }
        })
        return def.concat(digitAlarmList)
      },
      txPowerTypes() {
        // 0 低功率 1 高功率 2 中功率
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.mid'),
            value: 2,
          },
          {
            label: this.$t('dialog.high'),
            value: 1,
          },
        ]
      },
      permissionConditionsList() {
        // 0 可用彩色码 1 始终 2 信道空闲
        return [
          {
            label: this.$t('dialog.availableColorCode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.always'),
            value: 1,
          },
          {
            label: this.$t('dialog.channelIdle'),
            value: 2,
          },
        ]
      },
      busyChannelLockList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.carrier'),
            value: 1,
          },
          {
            label: 'CTCSS/CDCSS',
            value: 2,
          },
        ]
      },
      channelSettingComp() {
        return this.$refs.channelSetting
      },
      patrolConfigComp() {
        return this.$refs.patrolConfig
      },
      emergencyAlarmComp() {
        return this.$refs.emergencyAlarm
      },
      directMode() {
        return this.generalSettings.baseSettings.directMode
      },
      contactsDisabled() {
        return !this.menuSettings.contactConfig.contacts
      },
      phoneConfigDisabled() {
        return !this.menuSettings.phoneConfig.enable
      },
      scanConfigDisabled() {
        return !this.menuSettings.scanConfig.enable
      },
      roamConfigDisabled() {
        return !this.menuSettings.scanConfig.roamEnable
      },
      callConfigDisabled() {
        return !this.menuSettings.callConfig.callHistory
      },
      deviceConfigUnable() {
        return !this.menuSettings.deviceConfig.setting
      },
      deviceConfigSettingUnable() {
        return !this.menuSettings.deviceConfig.setting || !this.menuSettings.deviceConfig.deviceSetting
      },

      workAloneUnEnable() {
        return !this.alertConfig.aloneWorkEnable
      },
      aloneWorkOptList() {
        return [
          {
            label: this.$t('writeFreq.button'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.voiceLaunch'),
            value: 1,
          },
        ]
      },
      writeDataOption() {
        return [
          {
            type: 4,
            failedMsg: this.$t('msgbox.writeRegularSettingsFailed'),
          },
          {
            type: 5,
            failedMsg: this.$t('msgbox.writeDeySettingsFailed'),
          },
          {
            type: 6,
            failedMsg: this.$t('msgbox.writeSMSFailed'),
            option: { limit: 1 },
          },
          {
            type: 7,
            failedMsg: this.$t('msgbox.writeEncryptConfigFailed'),
          },
          {
            type: 8,
            failedMsg: this.$t('msgbox.writeEncryptKeyFailed'),
          },
          {
            type: 14,
            failedMsg: this.$t('msgbox.writeMenuFailed'),
          },
          {
            type: 15,
            failedMsg: this.$t('msgbox.writeSignalingSystemFailed'),
          },
          {
            type: 16,
            failedMsg: this.$t('msgbox.writeAlarmConfigFailed'),
          },
          {
            type: 18,
            failedMsg: this.$t('msgbox.writeDigitalAlarmFailed'),
          },
          {
            type: 19,
            failedMsg: this.$t('msgbox.writeAddressBookGroupFailed'),
          },
          {
            type: 20,
            failedMsg: this.$t('msgbox.writeAddressBookFailed'),
          },
          {
            type: 21,
            failedMsg: this.$t('msgbox.writePhoneBookFailed'),
          },
          {
            type: 22,
            failedMsg: this.$t('msgbox.writeReceivingGroupFailed'),
          },
          {
            type: 23,
            failedMsg: this.$t('msgbox.writeLevel1ZoneFailed'),
          },
          {
            type: 24,
            failedMsg: this.$t('msgbox.writeLevel2ZoneFailed'),
          },
          {
            type: 25,
            failedMsg: this.$t('msgbox.writeLevel3ZoneFailed'),
          },
          {
            type: 26,
            failedMsg: this.$t('msgbox.writeChannelDataFailed'),
            option: { limit: 2 },
          },
          {
            type: 27,
            failedMsg: this.$t('msgbox.writeScanListFailed'),
          },
          {
            type: 28,
            failedMsg: this.$t('msgbox.writeRoamListFailed'),
          },
          {
            type: 29,
            failedMsg: this.$t('msgbox.writePatrolSystemConfigFailed'),
          },
          {
            type: 31,
            failedMsg: this.$t('msgbox.writeEmergencyAlarmConfigFailed'),
          },
          {
            type: TableIndex.SosCfg,
            failedMsg: this.$t('msgbox.writeRescueAndSOSConfigFailed'),
          },
          {
            type: TableIndex.SosChData,
            failedMsg: this.$t('msgbox.writeRescueAndSOSChannelFailed'),
          },
          // 最后写入编程密码
          {
            type: 3,
            failedMsg: this.$t('msgbox.writeProgrammingPwdFailed'),
          },
        ]
      },
      getClassInstance() {
        return getClassInstance
      },
      Model() {
        return Model
      },
      notSettable() {
        return true
      },
    },
    watch: {
      sosChannelId(val) {
        this.sosChannel = this.sosChannelList[val]
      },
      smsContent: {
        deep: true,
        handler(data) {
          this.$nextTick(() => {
            // 短信内容变化时，检测按键定义中单键呼叫功能的设置
            this.detectButtonDefinedFromSmsChange()
          })
        },
      },
      // mixins计算属性
      selectDeviceData(data) {
        this.clearPrivateConfig()
        this.$nextTick(() => {
          if (data) {
            // 将选中的设备中关于信道等数据同步到界面中
            this.syncDeviceDataIntoConfig(data)
          }
        })
      },
      '$i18n.locale'(val) {
        // 切换语言后，重新更新菜单树节点名称
        this.updateNodeTitleFromLocaleChange()
        this.treeLoaded()
      },
      // 'generalSettings.syncTime'(val) {
      //   if (val) {
      //     this.generalSettings = Object.assign(this.generalSettings, {
      //       // ...getDateOption(),
      //       ...getTimeZoneOffsetObject()
      //     })
      //   }
      // },
      // 'generalSettings.month'(val) {
      //   if (this.generalSettings.day > this.maxDate) {
      //     this.generalSettings.day = this.maxDate
      //   }
      // },
      'buttonDefined.singleKeyCall': {
        deep: true,
        handler(val) {
          if (!val) {
            return
          }
          const DefSmsId = 0xFF
          for (let i = 0; i < val.length; i++) {
            const item = val[i]
            if (item.callId === 0xFFFF) {
              item.callType = SoftKeyCallType.GROUP
              item.smsId = DefSmsId
              continue
            }

            if (item.callType === SoftKeyCallType.MSG && item.smsId === DefSmsId) {
              item.smsId = this.smsContent[0].msgId
              continue
            }

            if (item.callType !== SoftKeyCallType.MSG) {
              const address = this.getSelectedAddress(item.callId)
              if (!address) {
                item.callId = 0xFFFF
                item.callType = SoftKeyCallType.GROUP
                item.smsId = DefSmsId
                continue
              }

              if (address.callType === SoftKeyCallType.GROUP) {
                item.callType = SoftKeyCallType.GROUP
              }
            }
          }
        },
      },
      disEncryptConfigEnable(val) {
        if (val) {
          if (!this.isAChannel && this.oneChannel.subChannelData.encryptConfig) {
            this.oneChannel.subChannelData.encryptConfig.enable = false
          }
        }
      },
      isConnectNetworking(val) {
        if (val) {
          this.oneChannel.scanConfig.autoScan = false
          this.oneChannel.scanConfig.autoRoam = false
          this.oneChannel.scanList = 0xFF
          if (this.oneChannel.subChannelData.alertConfig) {
            this.oneChannel.subChannelData.alertConfig.emergencyAlertTip = false
            this.oneChannel.subChannelData.alertConfig.emergencyAlertConfirm = false
            this.oneChannel.subChannelData.alertConfig.emergencyCallTip = false
          }
          this.oneChannel.subChannelData.emergencySysId = 0xFF
        } else {
          if (this.oneChannel.subChannelData.networkConfig) {
            this.oneChannel.subChannelData.networkConfig.localCall = false
          }
        }
      },
      onlyReceive(val) {
        if (val) {
          this.oneChannel.scanConfig.ipSiteConnect = false
        }
      },
      'oneChannel.subChannelData.timeSlotConfig.timeSlot'(val) {
        if (!this.isAChannel) {
          if (val !== 2 && this.oneChannel.subChannelData.timeSlotConfig) {
            this.oneChannel.subChannelData.timeSlotConfig.virtualTimeSlot = 0
          }
        }
      },
      'oneChannel.scanConfig.ipSiteConnect'(val) {
        // IP站点连接未勾选，则扫描列表不可能选择漫游组和自动漫游配置
        if (!val) {
          this.oneChannel.scanList = 0xFF
          this.oneChannel.scanConfig.autoRoam = false
        }
      },
      'oneChannel.subChannelData.timeSlotConfig.throughEnable'(val) {
        if (!val) {
          if (this.oneChannel.subChannelData?.timeSlotConfig) {
            this.oneChannel.subChannelData.timeSlotConfig.chSlotAdjust = 0
          }
        } else {
          // TDMA直通模式开启后，彩色码最大只能为14，如果超出则需修正
          if (this.oneChannel.subChannelData.colorCode > 14) {
            this.oneChannel.subChannelData.colorCode = 0
          }
        }
      },
      sameFreq(val) {
        if (val) {
          this.oneChannel.scanConfig.ipSiteConnect = false
          this.oneChannel.scanConfig.allowOfflineSign = false
        } else {
          if (this.oneChannel.subChannelData.timeSlotConfig) {
            this.oneChannel.subChannelData.timeSlotConfig.throughEnable = false
          }
        }
      },
      deviceConfigUnable(val) {
        if (val) {
          this.menuSettings.deviceInfo.info = false
          this.menuSettings.deviceConfig.deviceSetting = false
        } else {
          this.menuSettings.deviceInfo.info = true
          this.menuSettings.deviceConfig.deviceSetting = true
        }
      },
      'alertConfig.aloneWorkTime'(val) {
        const aloneWorkTime = val * 60
        if (this.alertConfig.aloneWorkRemindTime > aloneWorkTime) {
          this.alertConfig.aloneWorkRemindTime = aloneWorkTime
        }
      },
    },
    components: {
      WriteFreqFooter,
      selectDevice,
      deviceInfo,
      TableTree,
      bfInputNumber: defineAsyncComponent(() => import('@/components/common/bfInputNumber')),
      addressBook: defineAsyncComponent(() => import('@/components/interphoneWf/common/addressBook.vue')),
      phoneBook: defineAsyncComponent(() => import('@/components/interphoneWf/common/phoneBook.vue')),
      digitalAlert: defineAsyncComponent(() => import('@/components/interphoneWf/common/digitalAlert')),
      frequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
      shortMessage: defineAsyncComponent(() => import('@/components/interphoneWf/common/shortMessage')),
      receiveGroup: defineAsyncComponent(() => import('@/components/interphoneWf/common/receiveGroup')),
      patrolConfig: defineAsyncComponent(() => import('@/components/interphoneWf/common/patrolConfig')),
      emergencyAlarmConfig: defineAsyncComponent(() => import('@/components/interphoneWf/common/emergencyAlarmConfig')),
      addressBookGroup: defineAsyncComponent(() => import('@/components/interphoneWf/common/addressBookGroup')),
      scanGroup: defineAsyncComponent(() => import('@/components/interphoneWf/common/scanGroup')),
      roamGroup: defineAsyncComponent(() => import('@/components/interphoneWf/common/roamGroup')),
      multistageZone: defineAsyncComponent(() => import('@/components/interphoneWf/common/multistageZone')),
      zoneLeafTable: defineAsyncComponent(() => import('@/components/interphoneWf/common/zoneLeafTable')),
      freqMapOffset: defineAsyncComponent(() => import('@/components/interphoneWf/common/freqMapOffset')),
    },
    mounted() {
      bfglob.on('wf:redrawTree', this.redrawViewport)

      // 常规配置的语言选项，根据语言选择重置默认值
      if (this.generalSettings.baseSettings.locale !== undefined) {
        const localeList = this.localeList ?? []
        const localeOption = localeList.find(opt => opt.value === this.generalSettings.baseSettings.locale)
        this.generalSettings.baseSettings.locale = localeOption?.value ?? localeList[0]?.value ?? 0
      }
    },
    beforeUnmount() {
      bfglob.off('wf:redrawTree', this.redrawViewport)
    },
    beforeMount() {
      // this.initSosChannelList()
    },
  }
</script>

<style lang='scss'>
  @use "@/css/interphoneWf/button-define.scss" as *;
  @use "@/css/interphoneWf/tree-card-layout.scss" as *;
</style>
