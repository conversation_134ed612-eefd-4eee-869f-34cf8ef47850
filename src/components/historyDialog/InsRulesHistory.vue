<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :user="queryProps.user"
    :device="queryProps.device"
    :rule="queryProps.rule"
    :line="queryProps.line"
    :lineRid="lineRid"
    :ruleRid="ruleRid"
    :label-width="labelWidth"
    :head="dthead"
    :name="dataTable.name"
    :detailHead="dtdetailHead"
    :detailBodyName="dataTable.detailBodyName"
    :exportNamePrefix="dlgTitle"
    :parse-request-data="parseRequestdata"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item
        :label="$t('dialog.ruleName')"
        prop="ruleRid"
      >
        <el-select
          v-model="ruleRid"
          filterable
          clearable
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="item in ruleRids"
            :key="item.rid"
            :label="item.label"
            :value="item.rid"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :class="(isFR || isEN) && isMobile ? 'MobileLongLabel' : ''"
        :label="$t('dialog.insLine')"
        prop="lineRid"
      >
        <el-select
          v-model="lineRid"
          filterable
          clearable
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="item in lineRids"
            :key="item.rid"
            :label="item.label"
            :value="item.rid"
          />
        </el-select>
      </el-form-item>
    </template>
  </historyCommon>
</template>

<script>
  import bfutil from '@/utils/bfutil'

  import bfTime from '@/utils/time'
  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'

  export default {
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'rule_statistic_result',
          cmd: 52,
          user: false,
          device: false,
          rule: true,
          line: true,
        },
        dataTable: {
          name: 'InsRulesHistoryTable',
          detailBodyName: 'checkItems',
        },
        lineRid: '',
        ruleRid: '',
        ruleRids: bfglob.gruleMaster.getList(),
        lineRids: bfglob.glineMaster.getList(),
      }
    },
    methods: {
      removeDataTableData() {
        this.lineRid = ''
        this.ruleRid = ''
      },
      parseRequestdata(item) {
        var rule_item = bfglob.gruleMaster.get(item.ruleRid)
        if (typeof rule_item === 'undefined') {
          bfglob.console.error('没有此统计规则', item.ruleRid)
          return
        }
        var line_item = bfglob.glineMaster.get(item.lineRid)
        if (typeof line_item === 'undefined') {
          bfglob.console.error('没有此统计线路', item.lineRid)
          return
        }
        item.no = item.no + 1

        var get_assign_days = (date) => {
          if (date === '' || typeof date === 'undefined') {
            date = new Date()
          }
          var n = new Date(date).getDay()
          var _day = this.$t('dialog.Sun')
          switch (n) {
            case 0:
              _day = this.$t('dialog.Sun')
              break
            case 1:
              _day = this.$t('dialog.Mon')
              break
            case 2:
              _day = this.$t('dialog.Tues')
              break
            case 3:
              _day = this.$t('dialog.Wed')
              break
            case 4:
              _day = this.$t('dialog.Thur')
              break
            case 5:
              _day = this.$t('dialog.Fri')
              break
            case 6:
              _day = this.$t('dialog.Sat')
              break
          }
          return _day
        }
        item.week = get_assign_days(item.timeStart)

        item.ruleName = rule_item.ruleName
        item.lineName = line_item.lineName
        item.FPY = (item.pointOk / item.pointTotal * 100).toFixed(2) + '%'
        item.checkDate = bfTime.getLocalTimeString(item.timeStart, 'YYYY-MM-DD')

        // 将对应的巡查点数据添加到详细表中，以便添加子表和导出数据
        for (var k in item.checkItems) {
          let __item = item.checkItems[k]
          const point = bfglob.glinePoints.get(__item.pointRid, true)
          const __point = point ? bfutil.cloneObj(point) : {}
          __item = Object.assign(__item, __point)
          __item.startTime = bfTime.getLocalTimeString(__item.checkTimeEarly, 'HH:mm')
          __item.endTime = bfTime.getLocalTimeString(__item.checkTimeEnd, 'HH:mm')

          __item._checkTime = __item.checkTime
            ? bfTime.getLocalTimeString(__item.checkTime, 'HH:mm:ss') : ''

          var device = bfglob.gdevices.get(__item.deviceRid)
          __item.deviceSelfId = device ? device.selfId : ''
          __item.checkUserName = bfglob.guserData.getUserNameByKey(__item.checkPersonRid)

          let result = ''
          let cls = ''
          switch (__item.checkResult) {
            case 0:
              result = 'OK'
              cls = 'success'
              break
            case 1:
              result = this.$t('dialog.early')
              cls = 'warning'
              break
            case 2:
              result = this.$t('dialog.late')
              cls = 'warning'
              break
            case 3:
              result = this.$t('dialog.miss')
              cls = 'danger'
              break
          }

          __item._checkResult = `<span class='${cls}'>${result}</span>`
        }

        return item
      },
      // queryResult(db_data, opt) {
      //   if (opt === '0,end' && db_data.no === -1) {
      //     bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
      //     return
      //   }
      //   if (!this.processReqMaximum && (this.bodyCache.length || this.dataTable.body.length) >= bfglob.reqMaximum) {
      //     this.processReqMaximum = true
      //     bfNotify.reqMaximumPrompt()
      //   } else {
      //     this.parseRequestdata(db_data)
      //   }
      //
      //   if (opt.includes('end')) {
      //     this.dataTable.body = Object.assign([], this.bodyCache)
      //     this.bodyCache = []
      //     this.processReqMaximum = false
      //     if (this.dataTable.body.length > 0) {
      //       this.tabsValue = 'data'
      //     } else {
      //       this.tabsValue = 'query'
      //       bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
      //     }
      //   }
      // },
      // startQuery() {
      //   this.dataTable.body = []
      // },
    },
    components: {
      historyCommon
    },
    computed: {
      contentClass() {
        return (this.isMobile ? 'is-mobile ' : '')
      },
      labelWidth() { return this.isFR ? '120px' : '100px' },
      dthead() {
        return [
          {
            title: this.$t('dataTable.weeks'),
            data: 'week',
            width: '80px',
          },
          {
            title: this.$t('dialog.ruleName'),
            data: 'ruleName',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.insLine'),
            data: 'lineName',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dataTable.checkCount'),
            data: 'pointTotal',
            width: '80px',
          },
          {
            title: this.$t('dataTable.pass'),
            data: 'pointOk',
            width: '80px',
          },
          {
            title: this.$t('dataTable.fail'),
            data: 'pointBad',
            width: this.isFR ? '100px' : '80px',
          },
          {
            title: this.$t('dataTable.PTY'),
            data: 'FPY',
            width: this.isFR ? '120px' : '80px',
          },
          {
            title: this.$t('dialog.checkDate'),
            data: 'checkDate',
            width: this.isFR ? '120px' : '80px',
          },
        ]
      },
      dtdetailHead() {
        return [
          {
            title: this.$t('dialog.index'),
            data: 'no',
          },
          {
            title: this.$t('dataTable.pointNo'),
            data: 'pointId',
          },
          {
            title: this.$t('dialog.pointName'),
            data: 'pointName',
          },
          {
            title: this.$t('dataTable.checkTimeEarly'),
            data: 'startTime',
          },
          {
            title: this.$t('dataTable.checkTimeEnd'),
            data: 'endTime',
          },
          {
            title: this.$t('dataTable.checkTime'),
            data: '_checkTime',
          },
          {
            title: this.$t('dataTable.checkDevice'),
            data: 'deviceSelfId',
          },
          {
            title: this.$t('dataTable.checkUser'),
            data: 'checkUserName',
          },
          {
            title: this.$t('dataTable.checkResult'),
            data: '_checkResult',
          },
        ]
      },
      dlgTitle() {
        return this.$t('dialog.insRules')
      },
    },
  }
</script>

<style lang="scss">
  .success,
  .warning,
  .danger {
    font-size: 1.3em;
    font-weight: bold;
  }

  .success {
    color: #13CE66;
  }

  .warning {
    color: #FF9900;
  }

  .danger {
    color: #FF4949;
  }

  .MobileLongLabel {
    .el-form-item__label {
      height: 32px;
      line-height: 1;
    }
  }
</style>
