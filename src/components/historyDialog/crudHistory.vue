<template>
  <div>
    <historyCommon
      id="bfcurdHistory"
      ref="hisCom"
      :dbListName="queryProps.dbListName"
      :cmd="queryProps.cmd"
      :label-width="labelWidth"
      :userRid="userRid"
      :orrgRid="orgRid"
      :head="dthead"
      :name="dataTableName"
      :exportNamePrefix="dlgTitle"
      @query-finish-proxy-curd-detail="proxyDeteail"
      @remove-data-table-data="removeDataTableData"
    >
      <template #optionsFormItem>
        <el-form-item
          :label="$t('dialog.orgShortName')"
          prop="orgRid"
        >
          <el-select
            v-model="orgRid"
            filterable
            clearable
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            @change="orgChanged"
          >
            <el-option
              v-for="item in orgRids"
              :key="item.rid"
              :label="item.label"
              :value="item.rid"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('dialog.userName')"
          prop="userRid"
        >
          <el-select
            v-model="userRid"
            filterable
            clearable
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="item in userRids"
              :key="item.rid"
              :label="item.label"
              :value="item.rid"
            />
          </el-select>
        </el-form-item>
      </template>
    </historyCommon>
    <el-dialog
      v-model="isShow"
      :title="$t('dialog.details')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      class="header-border crud-log-details"
      append-to-body
    >
      <div
        v-for="li in optDetails"
        :key="li"
        class="text item"
      >
        {{ li }}
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { DbOrgIsVirtual } from '@/utils/dynamicGroup/api'
  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'
  import bfNotify from '@/utils/notify'

  const DbOrgIsVirtualList = [DbOrgIsVirtual.TaskGroup, DbOrgIsVirtual.TempGroup]

  export default {
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_crud_log_list',
          cmd: 58,
        },
        dataTableName: 'crudHistoryTable',
        isShow: false,
        optDetails: [],
        userRid: '',
        orgRid: '',
        orgRids: bfglob.gorgData.getList(),
        allUData: bfglob.guserData.getList(),
      }
    },
    methods: {
      removeDataTableData() {
        this.userRid = ''
        this.orgRid = ''
      },
      orgChanged() {
        this.userRid = ''
      },
      operations(operation) {
        if (!operation) { return '' }
        return this.$t(`operations.${operation}`)
      },
      show_crud_log_details(data) {
        this.optDetails = []
        this.isShow = true
        const message = JSON.parse(data)
        this.optDetails = Object.keys(message).map(k => {
          return k + ': ' + message[k]
        })
      },
      proxyDeteail() {
        if (this.$refs.hisCom.dataTableBody.length > 0) {
          setTimeout(() => {
            // 代理详情按钮事件
            const that = this
            const proxyDetailClick = function() {
              // this指向为被点击的元素
              that.show_crud_log_details(this.dataset.detail, this)
            }
            $('#bfcurdHistory .bfdataTable tbody').off('click', '.show-details-btn')
            $('#bfcurdHistory .bfdataTable tbody').on('click', '.show-details-btn', proxyDetailClick)
          }, 0)
        } else {
          bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
        }
      }
    },
    components: {
      historyCommon
    },
    computed: {
      labelWidth() { return this.isFR ? '120px' : this.isEN ? '100px' : '90px' },
      userRids() {
        const data = this.allUData
        return Object.keys(data).map(k => data[k]).filter((item) => {
          if (!this.orgRid) { return true }
          return item.orgRid === this.orgRid
        })
      },
      contentClass() {
        return (this.isMobile ? 'is-mobile ' : '')
      },
      allUserData() {
        const allUserData = bfglob.guserData.getList()
        return Object.keys(allUserData).map(k => allUserData[k])
      },
      allOrgData() {
        const allOrgData = bfglob.gorgData.getList()
        return Object.keys(allOrgData).map(k => allOrgData[k])
      },
      dthead() {
        return [
          {
            title: this.$t('dataTable.times'),
            data: 'updateAt',
            width: '140px',
          },
          {
            title: this.$t('dataTable.userName'),
            data: 'userRid',
            width: '130px',
            render: (data, type, row, meta) => {
              return this.allUserData.find(item => item.rid === data)?.label ?? '?'
            },
          },
          {
            title: this.$t('dataTable.operation'),
            data: 'operation',
            width: '150px',
            render: (data, type, row, meta) => {
              let [detail, opt] = (data || '.').split('.', 2)
              // 如果是db_org，则要判断是单位还是动态组
              if (detail === 'db_org') {
                // 从req中解析出db_org的数据，根据orgIsVirtual属性判断是否为动态组
                try {
                  const dbOrg = JSON.parse(row.req) || {}
                  if (DbOrgIsVirtualList.includes(dbOrg.orgIsVirtual)) {
                    detail = 'db_org_dynamic_group'
                  }
                } catch (e) {
                  // no-empty
                }
              }
              return this.operations(opt) + ': ' + this.operations(detail)
            },
          },
          {
            title: this.$t('dataTable.userOrg'),
            data: 'orgRid',
            width: '140px',
            render: (data, type, row, meta) => {
              return this.allOrgData.find(item => item.rid === data)?.label ?? '?'
            },
          },
          {
            title: this.$t('dataTable.IPAddress'),
            data: 'ipInfo',
            width: '140px',
          },
          {
            title: this.$t('dialog.details'),
            data: null,
            width: '60px',
            render: (data, type, row, meta) => {
              const action = `<button class='show-details-btn' data-detail='${data.req}'>` + this.$t('dialog.details') + '</button>'
              return action
            },
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.crudHistory')
      },
    },
  }
</script>

<style lang="scss">
  .show-details-btn {
    background: #13CE66;
    color: #fff;
    border: none;
    border-radius: 4px;
    text-align: center;
    padding: 4px 10px;
    font-size: 12px;
  }

  .el-dialog.crud-log-details {
    &>* {
      padding: 12px;
    }
  }
</style>
