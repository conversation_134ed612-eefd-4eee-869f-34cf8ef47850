<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :user="queryProps.user"
    :device="queryProps.device"
    :ctrlDmrId="ctrlDmrId"
    :parse-request-data="parseRequestdata"
    :head="dthead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item
        :class="isFR && isMobile ? 'MobileLongLabel' : ''"
        :label="$t('dialog.deviceName')"
        prop="ctrlDmrId"
      >
        <el-select
          v-model="ctrlDmrId"
          filterable
          clearable
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="item in ctrlDmrIds"
            :key="item.rid"
            :label="item.label"
            :value="item.dmrId"
          />
        </el-select>
      </el-form-item>
    </template>
  </historyCommon>
</template>

<script>
  import bfutil, { formatDmrIdLabel } from '@/utils/bfutil'
  import vueMixin from '@/utils/vueMixin'
  import HistoryCommon from '@/components/common/historyCommon.vue'
  import $ from 'jquery'

  export default {
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_controller_online_history_list',
          cmd: 54,
          user: false,
          device: false,
          ctrl: true,
        },
        dataTableName: 'ctrlonlineHistoryTable',
        ctrlDmrId: '',
        ctrlDmrIds: bfglob.gcontrollers.getList(),
      }
    },
    methods: {
      removeDataTableData() {
        this.ctrlDmrId = ''
      },
      parseRequestdata(item) {
        var ctrlItem = bfglob.gcontrollers.getDataByIndex(item.controllerDmrId)
        item.orgShortName = ctrlItem ? ctrlItem.orgShortName : ''
        item.selfId = ctrlItem ? ctrlItem.selfId : ''

        return item
      },
      /**
       * 处理188或185指令中的异常信息文本
       * @param jsonNote{string}  文本json对应的字符串
       * @returns {string}
       */
      formatCmdErrText(jsonNote) {
        const exceptionTextArray = []
        try {
          const exceptionJson = JSON.parse(jsonNote)
          if (exceptionJson.tmpErr === 1) {
            exceptionTextArray.push(this.$t('dataTable.overHeat'))
          }
          if (exceptionJson.antErr === 1) {
            exceptionTextArray.push(this.$t('dataTable.standingWaveException'))
          }
          if (exceptionJson.gpsErr === 0) {
            exceptionTextArray.push(this.$t('dataTable.gpsNotInstalled'))
          } else if (exceptionJson.gpsErr === 1) {
            exceptionTextArray.push(this.$t('dataTable.gpsNotSynchronous'))
          }
          if (exceptionJson.volErr === 1) {
            exceptionTextArray.push(this.$t('dataTable.overVol'))
          } else if (exceptionJson.volErr === 2) {
            exceptionTextArray.push(this.$t('dataTable.lowVol'))
          }
          if (exceptionJson.rxPllErr === 1) {
            exceptionTextArray.push(this.$t('dataTable.pllReceiveException'))
          }
          if (exceptionJson.txPllErr === 1) {
            exceptionTextArray.push(this.$t('dataTable.pllTxException'))
          }
          if (exceptionJson.fanErr === 1) {
            exceptionTextArray.push(this.$t('dataTable.fanExeception'))
          }
          if (exceptionJson.signal === 1) {
            exceptionTextArray.push(this.$t('dataTable.signalInterference'))
          }
          return exceptionTextArray.join(',')
        } catch (e) {
          return jsonNote
        }
      },
      /**
       * format185指令信息文本
       * @param jsonNote{string}  文本json对应的字符串
       * @returns {string}
       */
      format185StatusText(jsonNote) {
        let stateObj = {}
        try {
          stateObj = JSON.parse(jsonNote)
        } catch (e) {
          // no-empty
        }
        const ipAddress = `${stateObj.ipAddr >>> 24}.${(stateObj.ipAddr & 0xFF0000) >>> 16}.${(stateObj.ipAddr & 0xFF00) >>> 8}.${stateObj.ipAddr & 0xFF}`
        const channelId = stateObj.channelId ? stateObj.channelId + 1 : 1
        const tmpValue = stateObj.tmpValue ? stateObj.tmpValue / 10 : 0
        const volValue = stateObj.volValue ? stateObj.volValue / 1000 : 0
        const rxFrequency = stateObj.rxFrequency ? bfutil.frequencyHz2Mhz(stateObj.rxFrequency) : 0
        const txFrequency = stateObj.rxFrequency ? bfutil.frequencyHz2Mhz(stateObj.txFrequency) : 0
        const powerValue = stateObj.powerValue ?? 0
        let stateText = `${this.$t('dialog.channel')} : ${this.$t('dialog.channel')} ${channelId},
        ${this.$t('dialog.rxFrequency')}:${rxFrequency},
        ${this.$t('dialog.txFrequency')}:${txFrequency},
        ${this.$t('dialog.power')}:${powerValue}W,
        ${this.$t('msgbox.ipAddress')}:${ipAddress},
        ${this.$t('repeaterStatus.voltage')}:${volValue}V,
        ${this.$t('repeaterStatus.temperature')}:${tmpValue}℃`
        const errText = this.formatCmdErrText(jsonNote)
        if (errText !== '') {
          stateText += '\n' + errText
        }
        return stateText
      },
      // onMouseenter(el, e) {
      //   try {
      //     // 组件对象已经存在, 则显示数据即可
      //     if (el.elPopover) {
      //       el.elPopover.showPopover = true
      //       return
      //     }

      //     // 查找popover组件挂载dom元素
      //     const container = el.parentNode.querySelector('.popover-container')
      //     if (!container) return

      //     const row = JSON.parse(e.target.dataset.row)
      //     const defaultStatus = {
      //       antErr: 0,
      //       channelId: 0,
      //       deviceDmrid: 0,
      //       fanErr: 0,
      //       gpsErr: 0,
      //       ipAddr: 0,
      //       powerValue: 0,
      //       rxFrequency: 0,
      //       rxPllErr: 0,
      //       signal: 0,
      //       tmpErr: 0,
      //       tmpValue: 0,
      //       txFrequency: 0,
      //       txPllErr: 0,
      //       volErr: 0,
      //       volValue: 0,
      //     }
      //     // 通过Vue定义组件模块,渲染el-popover组件
      //     const popoverTpl = Vue.extend({
      //       template: `
      //         <el-popover
      //           placement="top-start"
      //           trigger="click"
      //           popper-class='repeater-185-status-popover'
      //           v-model="showPopover"
      //         >
      //           <dataStatusPopover :showBasicStatus="showBasicStatus"
      //                              :repeaterStatus="repeaterStatus"></dataStatusPopover>
      //         </el-popover>`,
      //       components: {
      //         dataStatusPopover: defineAsyncComponent(() => import('@/components/common/dataStatusPopover.vue'))
      //       },
      //     })

      //     // 挂载组件,并缓存到dom元素上
      //     el.elPopover = new popoverTpl({
      //       data() {
      //         return {
      //           showPopover: true,
      //           showBasicStatus: (row.actionCode === 185),
      //           repeaterStatus: { ...defaultStatus, ...JSON.parse(row.note) },
      //         }
      //       },
      //       i18n,
      //     })
      //     el.elPopover.$mount(container)
      //   } catch (e) {
      //     // no-empty
      //   }
      // },
      // onMouseleave(el/*, e*/) {
      //   // 不需要销毁, 关闭popover
      //   if (el.elPopover) {
      //     el.elPopover.showPopover = false
      //   }
      // },
      // // 初始化中继状态监控展示按钮的代理事件
      // initRepeaterShowStatusEvent() {
      //   this.$nextTick(() => {
      //     // 必须使用事件代理,表格是虚拟滚动,可以未渲染dom元素
      //     const tableBody = document.querySelector('.event-history-data-table > tbody ')
      //     const self = this
      //     $(tableBody).on('mouseenter', '.show-repeater-status-btn', function (e) {
      //       self.onMouseenter(this, e)
      //     })
      //     $(tableBody).on('mouseleave', '.show-repeater-status-btn', function (e) {
      //       self.onMouseleave(this, e)
      //     })
      //   })
      // },
    },
    components: {
      HistoryCommon,
    },
    computed: {
      contentClass() {
        return (this.isMobile ? 'is-mobile ' : '')
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '120px',
          },
          {
            title: this.$t('dialog.deviceName'),
            data: 'selfId',
            width: this.isFR ? '150px' : '100px',
          },
          {
            title: this.$t('dialog.ctrlDMRID'),
            data: 'controllerDmrId',
            width: '140px',
            render: (data) => {
              return formatDmrIdLabel(data)
            },
          },
          {
            title: this.$t('dataTable.eventType'),
            data: null,
            width: this.isFR ? '100px' : '80px',
            render: (data, type, row, meta) => {
              let onlineType = ''
              switch (data.actionCode) {
                case 1:
                  onlineType = this.$t('dataTable.online')
                  break
                case 2:
                  onlineType = this.$t('dataTable.offline')
                  break
                case 3:
                  onlineType = this.$t('dataTable.ctrlAlarm')
                  break
                case 188:
                  onlineType = this.$t('dataTable.statusException')
                  // return `<div class="repeater-monitor-ctrl">${onlineType}<button type="button" class="el-button el-button--primary el-button--mini is-circle show-repeater-status-btn" data-toggle="popover" data-row='${JSON.stringify(row)}'><i class="mdi mdi-share-variant show-repeater-status-btn--icon"></i></button><div class="popover-container"></div></div>`
                  break
                case 185:
                  onlineType = this.$t('dataTable.statusReport')
                  // return `<div class="repeater-monitor-ctrl">${onlineType}<button type="button" class="el-button el-button--primary el-button--mini is-circle show-repeater-status-btn" data-toggle="popover" data-row='${JSON.stringify(row)}'><i class="mdi mdi-share-variant show-repeater-status-btn--icon"></i></button><div class="popover-container"></div></div>`
                  break
                default:
                  onlineType = ''
              }
              return onlineType
            },
          },
          {
            title: this.$t('dataTable.ctrlDataTime'),
            data: 'actionTime',
            width: '140px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '140px',
            render: (data, type, row, meta) => {
              //188指令异常状态判定
              if (row.actionCode === 188) {
                return this.formatCmdErrText(data)
              }
              if (row.actionCode === 185) {
                return this.format185StatusText(data)
              }
              return data
            }
          },
        ]
      },
      dlgTitle() {
        return this.$t('dialog.ctrlHistory')
      },
    },
    beforeUnmount() {
      // 注销事件
      const tableBody = document.querySelector('.event-history-data-table > tbody ')
      if (!tableBody) return
      $(tableBody).off('mouseenter', '.show-repeater-status-btn')
      $(tableBody).off('mouseleave', '.show-repeater-status-btn')
    }
  }
</script>

<style lang="scss">
  .event-history-data-table {
    .repeater-monitor-ctrl .show-repeater-status-btn {
      margin-left: 4px;
    }
  }

  .MobileLongLabel {
    .el-form-item__label {
      height: 32px;
      line-height: 1;
    }
  }
</style>
