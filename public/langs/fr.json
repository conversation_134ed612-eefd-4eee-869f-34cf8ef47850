{"auth": {"applyAuth": "Demander une licence", "authDescription": "Description de licence", "authExpiredAlert": "Les informations relatives à la licence ont expiré, veuillez refaire une demande de licence.", "authFileError": "Erreur dans le fichier de licence", "authorizedContent": "Contenu de licence", "authorizedModules": "<PERSON><PERSON><PERSON>", "authorizedNumber": "Nombre", "authorizedTime": "Heure autorisée", "expireTime": "Heure d'expiration", "importAuthFile": "Importer un fichier de licence", "logout": "Déconnexion", "modules": {"controller": "Contrôleurs", "device": "<PERSON><PERSON><PERSON><PERSON>", "max-controllers": "Nombre de contrôleurs", "max-devices": "Nombre de terminaux", "max-users": "Nombre d'utilisateurs", "mod-dispatch": "Ré<PERSON><PERSON><PERSON>", "mod-phone-gateway": "Passerelle téléphonique", "mod-record": "Fonction d'enregistrement", "mod-rfid": "Fonction patrouille", "mod-svt": "Accès au cluster virtuel", "mod-traditional-dmr": "Accès au terminal DMR conventionnel", "user": "utilisateru"}, "noAuthAlert": "Pas d'information sur la licence, veuillez d'abord demander une licence!", "noReminderToday": "Pas de rappel aujourd'hui", "noSpecifiedModuleAuth": "Pas d'info sur la licence ({module}), veuillez d'abord demander une licence!", "permanent": "Licence permanente", "projName": "Nom du projet", "queryAuthFailed": "Échec de la demande d'informations sur la licence", "reapplyAuthTips": "Il reste {days} jours avant l'heure d'expiration de l'autorisation, voulez-vous refaire une demande d'autorisation ?", "remainingExpirationTime": "({days} jours)", "requestAuthSuccess": "Demande de licence réussie", "unlimited": "illimité"}, "dataTable": {"BSGroupCall_2": "Appareils de niveau 2", "BSGroupCall_23": "Appareils de niveau 2&3", "BSGroupCall_3": "Appareils de niveau 3", "IPAddress": "IP Adresse", "PTY": "<PERSON><PERSON>", "acPowerOffAlarm": "Alarme de coupure de courant AC", "alarmConditions": "Statut d'alarme reçu", "alarmDevice": "Appareil d'alarme", "alarmTime": "<PERSON><PERSON> d'alarme", "alarmType": "Type d'alarme", "alarmUserName": "Utilisateurs d'alarme", "allChannel": "<PERSON><PERSON>", "allDevices": "Tous les appareils", "allSys": "Appel tous du système", "antiDismantlingAlarm": "Alarme anti-démontage", "backToWorkPrompt": "Signal du retour au travail", "batteryOn": "Batterie ON", "callChannel": "Canal d'appel", "callTarget": "Cible d'appel", "callTime": "Temps d'appel", "checkCount": "Nombre à vérifier", "checkDevice": "Appareils de vérification", "checkResult": "Résultats de vérification", "checkTime": "L'heure de vérification", "checkTimeEarly": "L'heure de vérification la plus tôt", "checkTimeEnd": "L'heure de vérification la plus tard", "checkUser": "Utilisateur de vérification", "confirmTime": "Confirmer l'heure", "ctrlAlarm": "Défaut", "ctrlDataTime": "Temps de data", "departureAlarm": "Alarme <PERSON>", "deviceParentName": "Unité du terminal", "direction": "Direction", "dispatchTarget": "Cible de dispaching", "dispatchTime": "Heure de dispaching", "dispatchTypeName": "Type de dispaching", "duration": "<PERSON><PERSON><PERSON>(secondes)", "emergency": "Alarmes d'urgence", "endRoomOrgName": "Unité qui terminent la salle de réunion", "endRoomPerson": "<PERSON><PERSON> qui terminent la salle de réunion", "endWork": "Hors service", "eventType": "Type d’événement", "exportExcel": "Exporter Excel", "fail": "Non qualifié", "fanExeception": "Le ventilateur est anormal", "gpsAlarm": "Alarme de défaut GPS", "gpsNotInstalled": "GPS n’est pas installé", "gpsNotSynchronous": "GPS n’est pas synchronisé", "gpsTime": "Temps de GPS", "groupCall_12L": "Tous les appareils de niveau 1 et 2", "groupCall_1L": "Tous les appareils de niveau 1", "groupCall_2L": "Tous les appareils de niveau 2", "inboundAlarm": "Alarme entrante dans la limite", "infraredSensorAlarm": "Alarme capteur infrarouge", "inputcontent": "Veuillez entrer le contenu", "inspector": "Inspecteur", "lowVol": "La tension est trop faible", "lowVoltageAlarm": "Alarme de faible voltage", "mobileMonitoringStayAlarm": "Alarme de séjour de surveillance mobile", "noLocationAlarm": "Alarme d'urgence(pas de localisation)", "offNetworkAlarm": "<PERSON><PERSON><PERSON> ho<PERSON>", "offline": "<PERSON><PERSON> ligne", "online": "en ligne", "onlineType": "Type en ligne", "operation": "Opération", "outboundsAlarm": "Alarme sortant de la limite", "overHeat": "La température est trop élevée", "overVol": "La tension est trop élevée", "pass": "Qualifié", "patrolTime": "<PERSON><PERSON> patrouille", "pllReceiveException": "Exception de réception PLL", "pllTxException": "PLL envoie une exception", "pointNo": "Point No.", "powerOff": "Désactiver", "powerOn": "Activer", "processContext": "Résultats du traitement", "processTime": " Temps de traitement", "processor": "Opérateur", "punchInWork": "Pointer en service", "readTime": "Temps de lecture de la carte", "readUser": "Utilisateur de la carte", "receiveName": "<PERSON><PERSON><PERSON>", "receiveTime": "<PERSON><PERSON> de réception", "receiver": "<PERSON><PERSON><PERSON> de <PERSON>", "registered": "Enregistré", "removeSimulcastSystem": "Supprimer du système de diffusion simultanée", "repeater": "<PERSON><PERSON><PERSON> de <PERSON>", "resetOn": "Activer par la batterie faible", "selAlarmNote": "Interroger les journaux d'alarmes", "selDetailNote": "Interroger les journaux de patrouille", "selGpsNote": "Interroger les journaux de trace GPS", "selOndutyNote": "Requête des enregistrements de quart", "selSoundNote": "Interroger les journaux d'enregistrements des appels", "selSwitchNote": "Interroger les journaux d'activation et de désactivation de l'appareil", "senderName": "Expéditeur", "signalInterference": "Interférences de signal", "smsNo": "SMS No.", "soundFileName": "Nom du fichier d'enregistrement", "soundSensorAlarm": "Alarme du capteur sonore", "sourceController": "Contrôleur source", "speed": "Vitesse", "sponsor": "Initiateur", "standingWaveException": "Anomalies d’ondes stationnaires d’antenne", "startRoomOrgName": "Initiateur de l'unité de la réunion", "startRoomPerson": "Initiateur de la réunion", "startWork": "En service", "statusException": "anormal", "statusReport": "Rapports d’étape", "syncCall": "Appel de groupe dynamique", "sysCenter": "Centre", "targetChannel": "Canal cible", "times": "Temps", "turnOffAlertMode": "Désactiver le mode d'alerte", "turnOnAlertMode": "Activer le mode d'alerte", "type": "Type", "userName": "Nom d'utilisateur", "userOrg": "Unité de l'utilisateur", "walkingMobileMonitorPrompt": "Alertes de surveillance mobile", "weeks": "<PERSON><PERSON><PERSON>"}, "dialog": {"1month": "1mois", "1week": "1<PERSON><PERSON><PERSON>", "1year": "1 an", "3day": "3 jours", "BaseStationPoint": "Points de patrouille des stations de base", "DMRDevice": "Station de véhicule", "Fri": "<PERSON><PERSON><PERSON><PERSON>", "Friday": "<PERSON><PERSON><PERSON><PERSON>", "Gsource": "Points virtuels", "Hsource": "Points actives", "IKnow": "Ok, j'ai compris", "Mon": "<PERSON><PERSON>", "Monday": "<PERSON><PERSON>", "Nsource": "Points passives", "PowerOnPwdErrorThreshold": "Seuil d'erreur du mot de passe de démarrage", "Quieting": "<PERSON><PERSON>", "Sat": "<PERSON><PERSON>", "Saturday": "<PERSON><PERSON>", "Sun": "<PERSON><PERSON><PERSON>", "Sunday": "<PERSON><PERSON><PERSON>", "Thur": "<PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Tues": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wed": "<PERSON><PERSON><PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON>", "action": "Action", "activeRFID": "RFID active", "add": "Ajouter", "addMapPoint": "Ajouter points sur la carte", "addPreMadeSms": "Ajouter un message", "addStrangeNumberAuto": "Ajouter automatiquement des inconnus au carnet d'adresses", "addressBook": "Liste des Contacts", "addressBookList": "Liste des contacts", "addressBookSettings": "Réglage de la liste des contacts", "advancedDynamicencryption": "Cryptage dynamique avan<PERSON>", "agree": "d'accord", "aheadTime": "Heure d'arrivée le plus tôt", "alarmSet": "Contrôle d'alarme", "alarmSt": "Statut d'alarme", "alertTitle": "<PERSON><PERSON><PERSON>", "allContacts": "Tout contacts", "allowGeneralDMROnlineCall": "Autoriser l'accès régulier au terminal DMR", "allowLoginManage": "Autoriser la connexion pour planifier la gestion", "allowOffNetwork": "Permettre off réseau", "allowOnlineCalls": "Autoriser les appels en ligne", "alreadyApplied": "DéjàAppliqué", "alreadyAuth": "Déjà autorisé", "always": "Toujou<PERSON>", "analog": "Analogique", "analogAndDigital": "Analogique et numérique", "analogChannel": "Canal analogique", "analogGatewayTerminal": "Terminal de gateway analogique", "analogHangTime": "Temps de suspension des appels analogiques({unit})", "annex": "Annexe", "answer": "Réponse", "answeredCall": "<PERSON><PERSON><PERSON> r<PERSON>us", "area": "Zone", "areaData": "Zone data", "areaDown": "Zone down", "areaName": "Nom de la zone", "areaNumber": "Zone numéro", "areaSearch": "Recherche de zone", "areaUp": "Zone up", "authDevices": "Terminal d'autorisation", "authorizationName": "Nom de licence", "autoCardReading": "Lecture automatique des cartes", "autoListeningTime": "Temps de monitoring automatique(s)", "autoPlaySms": "Play SMS automatique", "autoPositioningTime": "Temps de positionnement automatique(s)", "autoSignalDurtion": "Temps d'envoi automatique des signaux({unit})", "autoSignalInterval": "Intervalle d'envoi automatique des signaux({unit})", "automode": "Mode automatique", "availableColorCode": "Codes de couleur disponibles", "back": "retour", "backlight": "Ré<PERSON>é<PERSON>lairage", "bandpassFilter": "Filtre passe-bande", "baseGroupCallScheduling": "Appel en groupe d'une station de base", "baseMobileRadio": "Station de base mobile radio", "baseStation": "Station de base", "basicConfig": "Configuration de base", "batchSetting": "Copier par lot", "batchSettingChannel": "Copier canal par lot", "batteryVoltage": "Voltage de la batterie", "bdGateId": "BDS gateway ID", "bdsSwitch": "BDS switch", "beidouAddressBook": "Contacts BDS", "beidouContact": "Contact BDS", "beidouNav": "BDS navigation", "beidouNumber": "Numéro de BDS", "beidouPositionInfo": "Info de positionnement de BDS", "beidouService": "Service BDS", "beidouSetting": "Réglage BDS", "beidouSms": "BDS SMS", "beidouSwitch": "Switch BDS", "blackList": "Liste noire", "blackWhiteList": "Liste noire et blanche", "bootInterface": "Interface d'activer", "buttonDefinition": "Définition des buttons", "callBackTarget": "Cible de rappel", "callPermissionIndication": "Indication de la permission d'appel", "callRecord": "Journal des appels", "callReminder": "<PERSON><PERSON><PERSON> d'appel", "callTarget": "Cibles d'appel", "callTransfer": "Transfert de l'appel", "callType": "Type d'appel", "calledDevice": "Terminal d'appel", "calledTarget": "Cible d'appel", "callingContact": "Appel contact", "canTalk": "<PERSON><PERSON><PERSON><PERSON> parler", "cancel": "Annuler", "cancelCenterCHCtrl": "Annuler la commutation de canal", "cancelEGChannel": "Annuler le dispatching des canaux d'urgence", "cancelListen": "<PERSON><PERSON><PERSON>r moniteur", "cancelPending": "Annuler en attente", "cancelSetting": "Ann<PERSON>r le réglage", "centralCtrlLocate": "Positionnement surveillé", "chConfigSwitch": "Switch de configuration des canaux", "chDisplay": "Affichage du canal", "chDisplayMode": "Mode d'affichage du canal", "chDownSwitch": "Switch du canal down", "chHangTime": "Temps de suspension du canal({unit})", "chId": "canal ID", "chName": "Nom du canal", "chType": "Type de canal", "chUpSwitch": "Switch du canal up", "channel": "Canal", "channelBandwidth": "Largeur de bande du canal", "channelConfigPassword": "Mot de passe pour la configuration du canal", "channelCtrl": "Dispatch", "channelDown": "Canal down", "channelGroupCallSD": "Appel en groupe de canal", "channelIdle": "Canal disponible", "channelIdleIndication": "Indication du canal disponible", "channelList": "Liste des canaux", "channelManager": "Gestion des canaux", "channelSetting": "Réglage des canaux", "channelShiftLevel": "Niveau de service du canal", "channelUp": "Canal up", "checkCount": "Vérifier de fois", "checkDate": "Vérifier la date", "checkLineName": "Nom d'itinéraire de patrouille", "checkStartTime": "<PERSON><PERSON> d<PERSON>", "childRowIsEmpty": "Aucune information détaillée pour la ligne actuelle", "chipAutoResMechanism": "Mécanisme automatique de réponse à la puce", "chooseDate": "<PERSON><PERSON> la date", "cleanLogs": "Effacer logs", "clearAlarm": "Supprimer l'alarme", "clearChannelConfig": "Effacer la configuration des canaux", "clearCheckedImg": "Supprimer l'image sélectionnée", "clearOnceIn10Minutes": "Effacer chaque 10mins", "clickCheckImg": "Cliquez pour sélectionner l'image", "close": "<PERSON><PERSON><PERSON>", "closeMenuButton": "<PERSON><PERSON><PERSON> le menu", "cmdOpts": "Commande", "colorCodes": "Code couleur", "colorMapPoint": "Points de couleur", "commandAgent": "Siège de commandement", "commander": "Radio de Commande", "common": "commun", "configInfo": "Configuration", "configure": "Configuration", "confirm": "Comfirmer", "confirmDelBtn": "Confimation de supprimer", "connect": "Connecté", "contact": "Contact", "contactGrouping": "Regroupement des contacts", "contactList": "Liste des contacts", "controlTelephoneGateway": "Passerelle TG810", "controllerPointJumpTip": "Le centre de la carte a sauté au point de repère d'équipement actuel, veuillez cliquer sur la page d'accueil pour voir", "copy": "<PERSON><PERSON><PERSON>", "copyChannel": "<PERSON><PERSON><PERSON> en double", "cpVersion": "Version CP", "crossTOT": "Traverse limite(mins)", "ctrlDMRID": "Équipement DMRID", "ctrlHistory": "Historique des événements de l’appareil", "curChannelInfo": "Info du canal actuel", "currentBattery": "<PERSON><PERSON>ie", "currentChannel": "Canal actuel", "currentDevice": "Ternimal accutel", "customImg": "Image personnalisée", "customPiggybackPowerLeve": "Personnaliser la puissance de manpack", "customPower": "Puissance personnalisée", "customVehiclePlv": "Personnalisation du niveau de puissance du véhicule", "customize": "Personnalisation", "data": "Data", "dataChannel": "Canal du data {num}", "dataTrsCmdRes": "Réponse à la commande de transmission numérique", "defCommunicationAddress": "Adresse par défaut", "defWorkingTimeSlot": "Timeslot de travail par défaut", "default": "default", "defaultChannel": "Canal par défaut", "defaultFromArea": "Zone subordonné par défaut", "defaultMainArea": "Zone principale par défaut", "defaultUserArea": "Zone d'utilisateur par défaut", "delAllPrivilegeDevice": "Annuler toutes les autorisations de localisation sur le terminal", "delPrivilegeDevice": "Annuler l'autorisation de localisation du terminal", "delPrivilegeDeviceFail": "Échec de l'annulation de l'autorisation de localisation du terminal", "delPrivilegeDeviceSuccess": "Annulation réussie de l'autorisation de localisation du terminal", "delayTime": "Heure d'arrivée le plus tard", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteContact": "Supprimer contact", "deletePrompt": "Cette opération va supprimer définitivement les données qui peut contenir d'informations importantes, continuez？", "detailOfLineMaster": "Tableau des détails des intinéraires", "details": "Détails", "devAllStatus": "Tout status", "devDisAuthCode": "Code d'authentification de la désactivation des appareils", "devEnAuthCode": "Code d'authentification de l'activation des appareils", "devRemoteCtrl": "Activation/désactivation à distance des appareils", "deviceActive": "Activer appareil", "deviceDMRID": "DMRID des terminaux", "deviceDataTitle": "Gestion des terminaux", "deviceDetect": "Détecter appareil", "deviceDmrIdIsExisted": "Le DMRID existe déjà", "deviceInfo": "Info d'appareil", "deviceName": "Nom de l'équipement", "devicePointJumpTip": "Le centre de la carte a sauté au point de repère actuel du terminal, veuillez cliquer sur la page d'accueil pour voir", "deviceRemoteDeath": "Tuer à distance", "deviceSettings": "Réglage de appareil", "deviceTime": "Temps d'appareil", "deviceToWriteFrequency": "Appareil pour programmer", "deviceType": "Type de terminal", "dialInList": "Liste d'appels", "dialOutList": "Liste des appels sortants", "digital": "Numérique", "digitalAnalogChannel": "Canal numérique et analogique", "digitalChannel": "Canal numérique", "digitalGatewayTerminal": "Terminal de gateway numérique", "disListen": "Verrouillage de réception", "disSL": "Verrouillage de réception et de transmission", "disSend": "Verrouillage de transmission", "disabledAllLed": "Désactiver tout LED", "disconnect": " Déconnecté", "dispatch": "Logs de dispaching", "displayFrequency": "Afficher la fréquence", "displaysCallIdAndAlias": "Afficher ID et alias", "dmrAddressBook": "DMR contacts", "dmrContact": "DMR contacts", "dmrIdNumber": "No. DMRID", "dmrIdResult": "DMRID Hexadecimal", "dmrService": "Service DMR", "dmrSms": "DMR SMS", "dnsServerIp": "IP DNS Serveur", "download": "DownLoad", "draftBox": "<PERSON><PERSON><PERSON>", "duplexSwitch": "<PERSON><PERSON> de duplexeur", "dutyMachine": "Appareil en service", "dynamicGroupCallSD": "Appel en groupe dynamique", "dynamicOwnGroup": "Groupe propriétaire dynamique", "dynamicencryption": "Cryptage dynamique", "early": "Arriver tôt", "edit": "Modifier", "editContacts": "Modifier la liste des contacts", "editContacts2": "Modifier contacts", "editData": "Données modifiables", "editScanList": "Modifier la liste de scanner", "editUserPerm": "Modifier les autorisations de l'utilisateur", "editZone": "Éditer zone", "effectEndTime": "Date de fin", "effectStartTime": "Date de début", "effectType": "Type d'effet", "effectiveDate": "Date expiré", "electricFence": "GPS Clôture", "electricalAdjustmentFilter": "Filtre de réglage électrique", "emerHangTime": "Temps de suspension des appels d'urgence({unit})", "emergency": "Alarme d'urgence", "emergencyAlarm": "Alarme d'urgence s'est produite", "emergencyDispatch": "Dispaching d'urgence tout appel activé", "emission": "Transmission", "emissionNumber": "@:dialog.emission {num}(MHz)", "empty": "Vider", "enable": "Activer", "enableAutoMonitoring": "Activer auto monitoring", "enableEGChannel": "Activer le dispatching des canaux d'urgence", "enableEmergencyAlarm": "Alarme d'urgence activée", "enableInAndOut": "Activer la surveillance transfrontalière", "enableInCacnelOut": "Activer la surveillance entrante", "enableListen": "Activer moniteur", "enableOutCacnelIn": "Activer la surveillance hors limites", "enablePopUpBlacklist": "Activer la liste noire d'appels sortants", "enablePopUpWhitelist": "Activer la liste blanche d'appels sortants", "enableStackingBlacklist": "Activer la liste noire d'appels", "enableStackingWhitelist": "Activer la liste blanche d'appels", "endTime": "Heure de fin", "errorTestFire": "Test d'erreur-transmettre", "errorTestReceive": "Test d'erreur - Recevoire", "exclusiveUser": "Utilisa<PERSON><PERSON> d<PERSON> ", "exit": "Sortir", "export": "Exporter data", "exportConfig": "Exporter la configuration", "findUsbDevice": "Rechercher USB", "firmwareVersion": "Version de firmware", "firstLevelArea": "Zone de niveau 1", "fixedLineNumber": "Numéro de ligne fixe", "fixedLineNumberHelp": "0xx-xxxxxxx-xxxx", "flag": "Marque", "forciblyOutNetworkAlarm": "Alarme hors ligne forcée", "freqAndChDisplay": "Affichage de la fréquence et du canal", "freqDisplay": "Affichage de la fréquence", "frequencyRange": "Gamme de fréquences(MHz)", "frequentContacts": "Contacts fréquents", "fullCall": "Tout appel", "fullCallPerm": "Autorisation d'appeler tout", "gatewayId": "Gateway ID", "gatewayIp": "IP Gateway", "gatewayName": "Nom du gateway", "gatewayPermission": "Autorisation du terminal téléphonique", "gatewayRule": "<PERSON><PERSON><PERSON>", "ge": "pi<PERSON><PERSON>", "generalDmr": "Terminal DMR conventionnel", "generalSetting": "Réglages généraux", "getLngLat": "Capturer sur la carte", "gpsAlarm": "GPS Alarme", "gpsCardNo": "GPS carte numéro", "gpsRadius": "GPS Rayon(m)", "gpsVirtualSet": "Point de patrouille virtuel", "grantUser": "Utilisateur autorisé", "groupCall": "Appel de groupe", "groupCallDialing": "Appler de groupe", "groupCallMapping": "Mapping des appels de groupe", "groupHangTime": "Temps de suspension des appels de groupe({unit})", "groupName": "Nom du groupe", "handmode": "Mode manuel", "hangTime": "Temps de suspension(ms)", "hangup": "Couper", "hasBeenDisListen": "Reception interdite", "hasBeenDisSend": "Transmission interdite", "hasBeenRoaming": "Roaming", "hasEGAlarmAndLocate": "Alarme d'urgence a été localisée automatiquement", "hasFixedGroupSD": "Appel en groupe fixé", "hasGpsAutoCtrlLocate": "GPS automatique", "hasLevelGroupCallSD": "Appels en groupe de niveau", "hasNetworkRoamingSD": "Roaming dispaching", "hasOffNetwork": "<PERSON><PERSON> ligne", "haveruntime": "<PERSON>ule fois(m)", "high": "haut", "highAndLowPowerSwitch": "  Commutation haute et faible puissance", "highFrequency": "Haute fréquence(MHz)", "idShownMinLen": "Chiffres d'affichage minimum de l'ID", "imageMapPoint": "Points d'icônes", "imbeSn": "Vocoder SN", "importConfig": "Importer la configuration", "importExcel": "Importer des données", "inCtrlAlarm": "Alarme de surveillance entrante", "inbox": "<PERSON><PERSON><PERSON>", "incomingCall": "A<PERSON> entrant", "index": "No.", "initTransferTargetTree": "Veuillez sélectionner la cible du transfert", "insLine": "Itinéraires des patrouilles", "insPoints": "Points de patrouille", "insRules": "Statistiques des patrouilles", "installLocation": "<PERSON><PERSON><PERSON> de l'installation", "interPhoneWfPerm": "Radio Programmation", "interfacePos": "Location d'interface", "internetGateway": "Terminal de gateway du réseau", "interphone": "Talkie-walkie", "ipSettingMode": "Mode IP réglage", "isGroupCall": "Appel de groupe ou non", "join": "<PERSON><PERSON><PERSON>", "joinBlackList": "Ajouter dans la liste noire", "joinWhiteList": "A<PERSON>ter dans la liste blanche", "keepAdding": "Continuer à ajouter", "keepSending": "Envoyer non-stop", "key01": "key01 ({type})", "key02": "key02 ({type})", "key03": "key03 ({type})", "key04": "key04 ({type})", "keyboardLock": "Verrouillage du clavier", "km_h": "km/h", "languageSettings": "<PERSON><PERSON>", "languageType": "Type de langue", "lastCheckDevice": "<PERSON><PERSON> appareil de pat<PERSON>", "lastCheckTime": "Dernier temps de patrouille", "lastCheckUser": "<PERSON><PERSON> utilisateur de patrouille", "lastDataTime": "Heure des dernières données", "lat": "Latitude", "latDif": "Différence de latitude", "late": "Arriver en retard", "launchContact": "Contact de lancement", "leaveTOT": "<PERSON><PERSON>te limite(mins)", "ledIndication": "Indication LED", "ledIndicator": "Indicateur LED", "level": "Niveau", "lineName": "Nom d'itinéraire", "linePointTitle": "Points de patrouille", "linePointType": "Type", "lineTitle": "Itinéraires", "listenChannel": "Canal d'écoute", "listenGroup": "Groupe écouté", "listenTime": "Durée d'écoute(secs)", "lngLat": "Lng & Lat", "local2net": "Les appels locaux sont considérés comme des appels Internet", "localCall": "Appel local", "localEditRxGroup": "Autoriser la modification locale des groupes de réception", "localName": "Nom local", "localNumber": "Numéro local", "localPort": "Port local", "locateCount": "Rapport fois de GPS", "locateCtrl": "Contrôle GPS", "locateSpacing": "Intervalle de GPS(secs)", "locateTime": "Temps de localisation(secs)", "lockedDev": "Verrouillage de la machine", "lockedStatus": "Statut verrouillé", "lon": "Longitude", "lonDif": "Différence de longitude", "longPress": "Presse longue", "longPressDuration": "Durée de presse longe(ms)", "lookGpsPointData": "Afficher les data des points de patrouille virtuel", "low": "Faible", "lowFrequency": "Basse fréquence(MHz)", "mainZone": "Zones de niveau 1 {id}", "manualDialing": "Composer <PERSON><PERSON>", "mapDisplayName": "Nom sur la carte", "mapMarker": "Points de repère de la carte", "mapMarkerH": "<PERSON><PERSON>", "mapMarkerW": "<PERSON><PERSON>", "mapPointJumpTip": "Le centre de la carte a sauté au point actuellement marqué, veuillez cliquer sur la page d'accueil pour voir", "mapPointType": "Type de point de patrouille", "mappingTarget": "Cibles correspondantes", "markerSize": "<PERSON><PERSON>rqueur", "maxLat": "<PERSON>", "maxLon": "<PERSON>", "maxSpeakTime": "Appel temps limit (s)", "memberList": "Liste des membres", "menuHangTime": "Temps de suspension du menu(s)", "menuSettings": "<PERSON><PERSON>", "meshDevice": "<PERSON><PERSON> MC-N", "meshGateway": "Passerelle MC-N", "meshGatewayDevice": "Terminal de passerelle maillé", "mi_h": "mi/h", "mid": "moyen", "minLat": "<PERSON>", "minLon": "<PERSON>", "minute": "Minute", "miss": "Manquer", "missedCall": "<PERSON><PERSON><PERSON> manq<PERSON>", "mobile": "station véhiculaire", "mobileCtrl": "Contrôle de mouvement", "mobileCtrlAlarm": "Alarme surveillance mobile", "mobileDevReqDevGps": "Le terminal d'interphonie réseau demande des informations de localisation du terminal", "mobileDevice": "Terminal mobile", "mobilePhoneNumber": "Numéro de téléphone", "mobilePhoneNumberHelp": "1xxxxxxxxxx", "mobileTerminalType": "Type de terminal：{type}", "mode": "Mode", "model": "<PERSON><PERSON><PERSON><PERSON>", "monitorSwitch": "Moniteur on/off", "moreLinePointHty": "Plus d'enregistrements de patrouille", "moveDn": "DOWN", "moveUp": "UP", "msgcontentplaceholder": "<PERSON><PERSON> le message", "msglimit": "SMS limite: 10", "muteAll": "<PERSON><PERSON> muet", "muteTone": "Son muet", "name": "Nom", "neglect": "négligence", "networkIp": "<PERSON> Réseau", "networkMask": "Masque de sous-rés<PERSON>", "networkService": "Services de réseau", "networkSetting": "Réglage du réseau", "networkSpeaking": "A<PERSON>s en réseau", "networkSwitch": "Switch du réseau", "networkTimeSlot": "Canal de mise en réseau", "new": "Nouveau", "newContact": "Nouveau contact", "newSmsMessage": "Nouveau SMS", "nmi_h": "nmi/h", "no": "NON", "noFunction": "Aucune fonction", "noMatchText": "Pas de données correspondantes", "nonCompacting": "Non compressé", "nonStandard": "Non standard", "none": "None", "noneLevel": "Non niveau", "notFoundGpsData": "Au<PERSON>ne donnée de point de patrouille virtuel", "notSendSmsData": "SMS non reconnu", "notes": "Note", "nothing": "None", "numKey": "Touche du nombre ({num})", "number": "<PERSON><PERSON><PERSON><PERSON>", "occurUnderVoltageAlarm": "Alarme de sous-tension", "off": "Off", "offNetwork": "<PERSON><PERSON>", "offNetworkGroupCallHangTime": "Temps de suspension d'un appel de groupe hors réseau(ms)", "offNetworkSingleCallHangTime": "Temps de suspension d'un appel unique hors réseau(ms)", "offlineRepeater": "<PERSON><PERSON><PERSON> offline", "offlineSign": "<PERSON><PERSON> ho<PERSON>", "on": "On", "onOff": "Switch", "oneLevel": "Niveau 1", "onlineRepeater": "Relais online", "openCenterCHCtrl": "Activer la commutation de canal", "openListenFn": "Activer moniteur", "openMobileCtrl": "Activer surveillance mobile", "openOutCtrl": "Activer la surveillance hors limites", "openPeripheralFn": "Activer les fonctions périphériques", "openSchedulingFn": "Activer dispatching", "openSentryCtrl": "Activer la surveillance de sentinelle", "opentInCtrl": "Activer la surveillance entrante", "orgDMRID": "Appel de groupe DMRID", "orgFullName": "Nom complet", "orgNoMapPointMsg": "Il n'y a actuellement aucun marqueur de carte pour l'unité actuelle, veuillez d'abord l'ajouter", "orgPointJumpTip": "Le centre de la carte a sauté au point de repère de l'unité actuelle, veuillez cliquer sur la page d'accueil pour voir", "orgShortName": "Nom court", "orgTitle": "Gestion des unités", "orgsAddMapPointMsg": "S'il est coché et ajouté avec succès, il passera à la page des marqueurs de carte pour ajouter des points de marqueur.", "outCtrlAlarm": "Alarme de surveillance hors limites", "outbox": "Boîte d'envoi", "outgoingCall": "Appels sortants", "parentController": "Contrôleur de niveau supérieur", "parentOrg": "Unité", "parentOrgName": "Supérieure", "parentZone": "Zone supérieure", "passThroughMode": "Mode direct", "patrolSystem": "Système de patrouille", "penNo": "N° de clôture", "permissionConditions": "Condition de permission", "phoneBlackWhiteList": "Liste noire et blanche de téléphone", "phoneBook": "Liste des Contacts", "phoneNoInputHelp": " Instructions pour la saisie du numéro de téléphone", "picSize": "Taille de l'image est 120*40", "picture": "Photo", "playTrack": "Afficher la trace", "plosive": "Plosive", "pocDevice": "Terminal POC", "pocDeviceManage": "Configuration du terminal POC", "pocFormErrorTip": "Veuillez remplir les informations complètes du terminal POC", "pocTreeTitle": "Veuillez sélectionner un carnet d'adresses", "pointCount": "Nombre des points", "pointIndex": "Point index", "pointName": "Nom point de patrouille", "pointRfid": "Point de patrouille RFID", "pointSerialNo": "Point série", "pollingTimeSlice": "Tranche horaire de vote(s)", "popUpBlacklist": "Liste noire d'appels sortants", "popUpWhitelist": "Liste blanche d'appels sortants", "postFence": "Poste de Clôture", "postName": "Titres des postes", "postTitle": "Gestion des postes", "power": "Puissance", "powerLevel": "Niveau de puissance", "powerOnPwd": "Mot de passe de démarrage", "powerSavingMode": "Mode économie d'énergie", "powerSwitch": "Switch puissance", "praviteHangTime": "Temps de suspension de des appels uniques({unit})", "preMadeSms": "SMS préétabli", "predefinedPhoneBook": "Liste des contacts prédéfini", "primaryAreaID": "ID de Zone niveau 1", "priority": "Priorité", "privelege": "Privilège", "privilegeDevice": "Autorisation de localisation du terminal", "prochatDevice": "Terminal de Prochat", "prochatDeviceAssociatedName": "Terminaux Prochat associés", "prochatDeviceList": "Liste des terminaux Prochat", "prochatDomain": "Nom du domaine de Prochat ou IP", "prochatGateway": "Gateway Prochat", "prochatGatewayConfig": "Configuration de la passerelle Prochat Fusion", "prochatGatewayDevice": "<PERSON><PERSON> passerelle <PERSON>", "prochatNo": "Compte de passerelle Prochat Fusion", "prochatPassword": "Mot de passe Prochat", "prochatPort": "Prochat Port", "prochatUserNo": "compte d'utilisateur", "programmingPwd": "Code de programmation", "query": "<PERSON><PERSON><PERSON>", "queryAllChannel": "Vérifier tous les canaux", "queryNotSentSMS": "Rechercher SMS non comfirmé", "queryRepeaterInfo": "Vérifier relais info", "querySentSMS": "Rechercher l'historique des SMS", "querySetting": "Réglage de la requête", "quickDailEnable": "Numérotation rapide sur l'interface principale", "readData": "Lire data", "readFromDevice": "Copier la configuration du canal", "readRfidFormDev": "Lisez RFID,et sélectionnez dans la liste déroulante", "realOrg": "<PERSON><PERSON>", "realplayer": "DownLoad Player", "receive": "Ré<PERSON>", "receiveDevice": "A<PERSON><PERSON>il de ré<PERSON>", "receiveFrequency": "<PERSON><PERSON><PERSON> r<PERSON>", "receiveGroup": "Groupe de réception", "receiveGroupList": "Liste des groupes de réception", "receiveLowPowerPromptInterval": "Intervalle de réception de rappel de batterie faible (s)", "receiveNumber": "@:dialog.receive {num}(MHz)", "receiveOnly": "Recevoir seulement", "receivingAnalogSubtoneCode": "Recevoir code subtone analogique", "receivingDigitalSubtoneCode": "Recevoir code subtone numérique", "receivingList": "Liste de recevoir", "receivingSubtoneType": "Type de la réception de subtone", "recordCompRatio": "Taux de compression de l'enregistrement", "recordEnable": "Activer enregistrement", "recording": "Enregistrement", "records": "Enregistrements", "refuse": "rejeter", "registCode": "Code d'enregistrement", "registerToSystem": "S'inscrire au système", "rejectingStrangeCalls": "Rejeter les appels non familiers", "remoteCtrlPwd": "Mot de passe de l'interface de la télécommande", "remoteDeviceDisabled": "Désactiver l'appareil distant", "remoteDeviceEnable": "Activer l'appareil distant", "remoteMonitor": "Moniteur à distance", "removeFenceAllCtrl": "<PERSON><PERSON><PERSON> le moniteur de clôture", "repeater": "<PERSON><PERSON><PERSON>", "repeaterId": "Rélais ID", "repeaterInfo": "Infos sur les relais", "repeaterName": "Nom de relais", "repeaterNumber": "Num<PERSON><PERSON>", "repeaterPluginHangTime": "Temps de suspension de brancher le relais", "repeaterTimeSlot": "<PERSON><PERSON> du relais", "repeaterVirtualTerminal": "Terminaux <PERSON>ls de relais", "repeaterWfPerm": "Relais Programmation", "repowerOnAndRestart": "Réalimenter et redémarrer du relais", "reqDevice": "Demande de terminal", "requiredRule": "Ce champ est obligatoire", "reset": "Reset", "responseTimeout": "Réponse timeout(s)", "responseTimeout1": "Réponse en retard(ms)", "restartRepeater": "<PERSON><PERSON><PERSON><PERSON> le relais", "restoreDefaultIPAddress": "Restaurer l'adresse IP par défaut", "reverseSubtoneDigital": "Inverser subtone numérique", "rgpsMode": "Transmission de positionnement fiable", "roamInterface": " Interface de roaming", "rootDirList": "Liste des répertoires racine", "rpgsModeInfo": "Une fois cette fonction activée, si le terminal télécharge l'emplacement mais ne parvient pas à obtenir la confirmation du système, il sera automatiquement enregistré localement sur le terminal et automatiquement téléchargé une fois le terminal reconnecté au système.", "rssiThreshold": "RSSI Seuil(dBm)", "ruleName": "Nom de la règle", "ruleTarget": "Cibles de la règle", "ruleTitle": "<PERSON><PERSON><PERSON>", "runNotesTitle": "Notes de déroulement", "rxFrequency": "Fréquence RX(MHz)", "rxGroup": "Groupe RX", "rxGroupsSetting": "Recevoir les paramètres du groupe", "salerId": "Distributeur ID", "satellitePosition": "Localisation par satellite", "satellitePositionService": "Services de positionnement par satellite", "satellitePositionSetting": "Réglages de positionnement par satellite", "save": "Enregistrer", "savePenNo": "Numéro enregistré de l'appareil", "scanEnable": "Switch de scanner", "schedulingSt": "Statut de dispaching", "secondaryArea": "Zone de niveau 2", "secondaryAreaID": "ID de Zone niveau 2", "selComputer": "Sélectionnez du local", "selImgPrompt": "Seuls les fichiers .jpg/.png moins de 100kb sont autorisés", "select": "<PERSON><PERSON>", "selectCallTarget": "Veuillez sélectionner la cible de l'appel", "selectColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> couleur", "selectImage": "Sélectionner image", "selectListenGroup": " Veuillez sélectionner un groupe de réception", "selectPoint": "Sélectionner point", "selectZone": "Veuillez sélectionner la zone du canal", "selectZoneCh": "Spécifier la zone/le canal de démarrage", "sendCmdHistory": "Logs des commandes d'envoi", "sendCmdTitle": "Dispatching", "sendCommand": "<PERSON><PERSON>t envoyer des commandes", "sendCount": "Fois de transmission", "sendGroup": " Groupe de transmission par défaut", "sendMessages": "Envoyer SMS", "sendPreambleDuration": "Durée de préambule de transmission(ms)", "sendTime": "Heure d'envoie", "sendTimeLimiter": "Limiteur d'envoie(s)", "sendedSmsData": "Historique de SMS", "senderDevice": "Appareil de transmission", "sentinelRadius": "<PERSON><PERSON> sentinelle (m)", "sentryCtrlAlarm": "  Alarme de surveillance de sentinelle", "serialNo": "Série No.", "serialNumber": "Série NO. ", "serverAddress": "<PERSON><PERSON><PERSON>", "serverPort": "Port du serveur", "serverSetting": "<PERSON><PERSON><PERSON><PERSON> du serveur", "setReceivingGroup": "Configurer les abonnements fixes", "settingSt": "Statut de réglage", "setupPhoneInfo": "Gestion du terminal du gateway", "shortNumber": "Numéro court", "shortNumberMapping": "Mapping de numéro court du gateway de téléphonie", "shortPress": "Presse courte", "shortestDistance": "Distance plus courte(m)", "showDeviceNameDisplay": "Nom du périphérique d'affichage en veille", "showLevel": "Niveau d'affichage", "silent": "Silent", "simulation": "Simulation", "simulcastController": "Contrôleur simulcast", "simulcastRepeater": "Relais simulcast", "simulcastSystem": "Système simulcast", "singleCall": "<PERSON><PERSON> privé", "singleCallConfirm": "Confirmer appel unique", "singleCallDialing": "<PERSON><PERSON><PERSON> privé", "singleCallMapping": "Mapping de l'appel unique", "sipDomain": "Nom du domaine de SIP ou IP", "sipGateway": "Gateway SIP", "sipGatewayConfig": "Configuration de gateway SIP", "sipGatewayDevice": "Borne passerelle SIP", "sipNo": "Compte SIP", "sipPassword": "Mot de passe SIP", "sipPort": "SIP Port", "sipProtocolDevice": "Terminal de protocole SIP", "sipProtocolDevicePassword": "Mot de passe du protocole SIP", "sipProxyGateway": "Si vous voulez exécuter ce gateway en arrière-plan", "sipServerGateway": "Gateway de serveur SIP", "sipServerGatewayConfig": "Configuration de la passerelle de serveur SIP", "sipServerGatewayDomain": "Nom de domaine ou IP du serveur SIP public", "sipServerGatewayListenPort": "Port de serveur SIP écoute", "sipServerGatewayRTPPortRange": "Port de serveur SIP RTP portée", "sipSoundLocale": "Langue des invites vocales SIP", "size": "Distances TX minimum(m)", "slotMode": "Mode de slot", "sms": "SMS", "smsContent": "Contenu SMS", "smsNotification": "Notification de SMS", "smsType": "Type SMS", "sortValue": "<PERSON><PERSON>", "soundTip": "Sonnerie on/off", "sourceDevice": "Terminal de source", "speakDevice": "Appareil d'appel", "speakPrompt": "Pressez F2/Espace pour parler", "speedUnit": "Unités de vitesse", "squelchCalibration": "Calibrage du squelch", "squelchLevel": "<PERSON><PERSON><PERSON> de squelch", "squelchSwitch": "Switch de squelch", "stackingBlacklist": "Liste noire d'appels", "stackingWhitelist": "Liste blanche d'appels", "standard": "standard", "standardEnableAndMute": "Standard ouvert,Standard silencieux", "standbyInterface": "Interface standby", "startCopy": "Commencer à copier", "startSetting": "Commence<PERSON> <PERSON> ré<PERSON>", "startTime": "<PERSON><PERSON> d<PERSON>", "starting": "Activer", "staticEncryption": "Cryptage statique", "status": "Statuts", "stopAlarmSound": "<PERSON><PERSON><PERSON><PERSON> le son de l'alarme", "stopTOT": "<PERSON><PERSON><PERSON><PERSON> limite(min)", "subAudio": "Subaudio", "subZone": "Zones de niveau 2 {id}", "subaudioSetting": "Réglage de subaudio", "subtoneDigital": "Subtone numérique", "suspendTime": "Temps de suspension", "switchCh": "Changer de canal", "switchChannel": "Switch canal", "switchTransmitPower": "Switch puissance d'émission", "systemCenter": "Centre du système", "tagName": "Nom du repère", "tailCancellation": "Annulation de la note finale", "tailSelection": "<PERSON><PERSON> de la note finale", "targetDevice": "terminal cible", "telecontrol": "Contrôler à distance", "telephoneDevice": "Téléphone", "telephoneGateway": "Téléphone Gateway", "telephoneNo": "Numéro de téléphone", "terminalAlias": "alias du <PERSON>", "terminalLevel": "Niveau du terminal", "terminalName": "Nom du terminal", "tertiaryAreaID": "ID de Zone niveau 3", "testMachineSignalDuration": "Tester la durée du signal de la machine({unit})", "testMachineSignalInterval": "Intervalle de signal de la machine d'essai({unit})", "textImportant": "SMS important", "textInfo": "SMS général", "textMsg": "SMS", "threeLevel": "Niveau 3", "timeSegment": "Temps valide", "timeSetting": "Réglage de l'heure", "timeSlot": "Timeslot {num}", "timeSlots": "Timeslot", "timeZone": "Fuseaux horaires", "timeZoneHours": "Heures du fuseau horaire", "timeZoneMinutes": "Minutes du fuseau horaire", "timeZoneSet": "Réglage du fuseau horaire", "toneOrTip": "Sonerie/Alerte", "totPwdUpdateDelay": "TOT code update en retard(s)", "tr925HighPower": "Haute puissance--25W(véhicule)/15W(manpack)", "tr925LowPower": "Faible puissance--5W(véhicule)/3W(manpack)", "tr925MediumPower": "Puissance moyenne--10W(véhicule)/6W(manpack)", "trailCtrl": "Contrôle de trace", "trailSpacing": "Trace Intervals (secs)", "transFrequency": "Fréquence de transmission", "transTimeLimit": "<PERSON><PERSON>lai <PERSON>", "transmitAnalogSubtoneCodes": "Transmettre code subtone analogique", "transmitDigitalSubtoneCodes": "Transmettre code subtone numérique", "transmitSubtoneDigital": "Transmission du type de subtone numérique", "triggerCardReading": "Lecture des cartes déclenchée par le bouton-poussoir", "turnOn": "Allumer", "twoLevel": "Niveau 2", "txFrequency": "Fréquence TX(MHz)", "txPower": "Fréquence TX", "type": "Type", "uDiskMode": "Mode UDisk", "uDiskModePassword": "Mot de passe du mode Udisk", "unListenGroup": "Groupe non écouté", "unencrypted": "Non crypté", "update": "Actualiser", "updateMapPoint": "Mettre à jour des points", "useDevice": "Appareil en service", "useUnit": "Unité d'utilisation", "userLoginName": "<PERSON><PERSON><PERSON>", "userLoginPass": "Mot de passe", "userName": "Nom d'utilisateur", "userPhone": "Tel", "userRfid": "No. RFID", "userZone": "Zones de niveau 3 {id}", "usersDataTitle": "Gestion des utilisateurs", "usersPrivelegeSet": "Réglages du privilège", "vcController": "Contrôleur de trunking virtuel", "vcRepeater": "Relais de trunking virtuel", "version": "Version", "versusEnableAndMute": "Versus activer, muet standard", "versusEnableOrMute": "Versus activer, ou muet", "viewContacts": "voir contacts", "vioAuto": "Automatic(Non jouer le son)", "vioPlay": "<PERSON><PERSON> le son", "virOrg": "<PERSON><PERSON>", "virtualCluster": "Trunking virtuel", "virtualClusterDevice": "Talkie-walkie de <PERSON>l", "virtualTimeSlot": "Timeslot de trunking virtuel", "vocalRule": "<PERSON><PERSON><PERSON> vocales", "voiceControl": "<PERSON><PERSON><PERSON><PERSON> du son", "voiceCryptoKey": "Clé de cryptage de la voix", "voiceCryptoType": "Type de crytage de la voix", "voiceCtrl": "Contrôle de voix", "voiceDelay": "Re<PERSON>d de la voix(ms)", "voiceFrameRelayCheck": "Vérification du transit des trames vocales", "voiceIndication": "Indication vocale", "voiceLevel": "Niveau de la voix(VOX)", "voicePrompt": "Signal vocal", "volumeDown": "Volume down", "volumeUp": "Volume up", "whiteList": "Liste blanche", "workingSt": "Statut de travail", "workspaceAcrossBorders": "La zone de travail a franchi la limite", "writeIn": "Sai<PERSON><PERSON>", "yes": "OUI"}, "dtable": {"all": "tous", "buttons": {"colvis": "Afficher/masquer les colonnes"}, "emptyTable": "Aucune donnée disponible dans le tableau", "info": "Afficher _TOTAL_ entrées", "infoEmpty": "Info vide", "infoFiltered": "(filtrer à partir de _MAX_ entrées totales)", "lengthMenu": "Afficher les entrées _MENU_", "loadingRecords": "Chargement...", "paginate": {"first": "<span class='bf-icon gw-first'></span>", "last": "<span class='bf-icon gw-last'></span>", "next": "<span class='el-icon-caret-right'></span>", "previous": "<span class='el-icon-caret-left'></span>"}, "processing": "Traitement en cours...", "search": "<span class='el-icon-search'></span>", "zeroRecords": "Aucun enregistrement correspondant trouvé"}, "dynamicGroup": {"addMember": "Ajouter un membre", "alreadyJoined": "{member} a rejoint {group}", "answeredExit": "<PERSON><PERSON><PERSON>", "cannotModifyQuickTempGroup": "Impossible de modifier les membres du groupe temporaire rapide", "deleteDynamicGroupTips": "Si supprimer le groupe dynamique?", "device": "Terminal", "dynamicGroupMembers": "Membres du groupe dynamique", "dynamicGroupPermission": "Autorisation de groupe dynamique", "exitWithoutAnswer": "Sortie sans réponse", "expired": "Expiré", "expiredDynamicGroup": "Le groupe temporaire ou le groupe de tâches a expiré", "fast": "rapide", "fastDynamicGroupNoModify": "Groupe temporaire rapide, aucune modification autorisée", "groupCall": "Appel de groupe", "groupCallDevice": "Appareil d'appel de groupe", "groupMemberUpperLimit": "Le nombre de membres du groupe a atteint la limite supérieure", "groupMembersLimit": "Le nombre de membres du groupe dépasse la limite", "groupName": "Nom du groupe", "invalidTaskGroup": "Le groupe de tâches a expiré", "isForceDelete": "Supprimer forcé?", "joinWithoutAnswer": "Rejoindre sans réponse", "jumpToNetworkCall": "Si sauter à l'appel en réseau?", "member": "membre", "memberDetails": "<PERSON>é<PERSON> du membre", "memberName": "Nom du membre", "memberOrg": "Unité de membre", "noDynamicGroupDmrId": "Aucun DMRID de groupe dynamique disponible", "noGroupMembers": "Aucun membre du groupe sélectionné", "noSuchDynamicGroup": "Le groupe dynamique est introuvable", "noUpdates": "Aucun changement de membre, aucune mise à jour requise", "normal": "Normal", "notFoundDbHandler": "Impossible de trouver la base de données correspondante, veuillez vous reconnecter", "notFoundDynamicGroup": "Le groupe dynamique n'a pas été trouvé, veuil<PERSON><PERSON> réessayer", "notFoundMemberInDynamicGroup": "Le membre est introuvable dans le groupe dynamique", "pcDeviceExitTaskGroup": "L'agent de commande{pcDevice}a quitté le groupe de tâches{dynamicGroup}", "pcDeviceJoinedTaskGroup": "L'agent de commande{pcDevice}a rejoint le groupe de tâches{dynamicGroup}", "permDied": "Aucune autorisation d'opération", "preempted": "<PERSON><PERSON><PERSON><PERSON>", "removeMember": "Supprimer un membre", "repeatGroupName": "Nom du groupe dynamique répété", "taskGroup": "Groupe de tâches", "taskGroupExpiredWithUpdate": "Le groupe de tâches a expiré, l'opération a échoué！", "tempGroup": "Groupe temporaire", "tempGroupInvalidAndDelete": "Supprimer automatiquement après invalidation？", "title": "Groupe dynamique", "updateFailedDetail": "Détails des membres mis à jour échoués", "updateGroupMembers": "Mettre à jour les membres du groupe dynamique ({group})"}, "error404": {"goBack": "Retour", "goHome": "Go Home", "notFoundPage": "La page visitée n'existe pas..."}, "header": {"CN": "中文", "EGAlarmOnMapCenterTips": "Centré sur la carte en cas d'alarme d'urgence", "EN": "English", "FR": "Français", "InsOnMapTips": "Afficher sur la carte pendant la patrouille", "InspectionNotice": "Notification de patrouille", "Korean": "<PERSON><PERSON><PERSON>", "Russian": "<PERSON><PERSON>", "accountManager": "Gestion des comptes", "callingTips": "Notification des appels", "checkChannelListenGroup": "Vérifier la configuration du groupe de réception du terminal", "clearCache": "Rechargement", "detectNewUiTips": "Une nouvelle version de la page a été détectée. Voulez-vous changer ?", "emergencyAlarmTips": "Alerte d'urgence", "fancytreeSortType": "Trier l'arborescence de la liste : terminaux devant les unités", "fullScreen": "Passer en plein écran", "language": "<PERSON><PERSON>", "logo": "Logo", "logoName": "Belfone Système de dispatching intelligent IP connect", "moveText": "<PERSON><PERSON><PERSON>", "moveTitle": "Belfone Système de dispatching intelligent IP connect", "navBtn": "Navigation", "newDevOrCtrlData": "Afficher des informations sur les équipements non enregistrés", "newPwd": "Nouveau mot de passe", "newPwd2": "Confirmez mot de passe", "oldPwd": "Mot de passe actuel", "onAndOffTips": "Notification on/off", "onMapDisplayLinePointName": "Noms des points de patrouille sur la carte", "openEGAlarmSound": " Jouer un son en cas d'alarme d'urgence", "orgList": "Liste d'unités", "scrollMoveTitle": "Est-ce que le titre défile", "serverTitle": "Le serveur est connecté", "setting": "Réglages", "showActivePointBattery": "Signaler la faible batterie du point actif", "showCrossBorderAlarm": "Afficher des alarmes hors limites", "showCtrlMarker": " Afficher des marqueurs d'équipement sur la carte", "showCtrollerStats": "Signaler lorsque l'équipement est déconnecté", "showDispatchInfo": "Afficher l'information de dispatching", "showIotDeviceMarkers": "<PERSON><PERSON> montrant les marqueurs des terminaux d'Internet des objets(IdO)", "showOnlyLocateTerminals": "Seuls les marqueurs de terminaux de localisation valides sont affichés sur la carte", "showOnlyOnlineTerminals": "Seuls les marqueurs de terminaux en ligne sont indiqués sur la carte", "showSmsResNotify": "Afficher la notification de SMS reçue par le terminal", "siteTitle": "Belfone Système de dispatching intelligent IP connect", "startAndEndWorkTips": "Notification de démarrer/finir les travaux", "switchNewUi": "Passer à une nouvelle page", "switchOldUi": "Passer à l'ancienne version de la page", "switchOldUiTips": "Voulez-vous passer à l’ancienne version de la page ?", "switchnewUiTips": "Voulez-vous passer à la nouvelle version de la page ?", "sysSet": "Réglage du système", "userName": "Nom d'utilisateur", "userSet": "Réglage de l'utilisateur"}, "iot": {"acOff": "AC Off", "acOn": "AC On", "devId": "Terminal ID", "duplicateID": "ID répété", "energySavingLamps": "Lampes à économie d'énergie", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "iotInspectDevice": "Terminal de patrouille IdO", "lastCmd": "Dernière commande", "normalClockIn": "Pointer normal", "orgIdError": "Les données de l'unité sont incorrectes", "receiveUnknownIotDevice": "Reçu des données de terminal IdO non enregistrées", "smokeDetector": "Détecteur de fumée", "tempDetector": "Détecteur de température/humidité", "tempReport": "Rapport de température/humidité", "temperature": "Température", "terminal": "Terminal IdO", "thingsCard": "<PERSON><PERSON>"}, "iotDevHistory": {"antiFoldAlarm": "Alarme anti-démontage", "cmdHex": "Commande", "cmdTime": "<PERSON><PERSON> de la commande", "emergency": "Alarme d'urgence", "heartbeat": "battement de coeur", "lowPower": "Alarme de batterie faible", "overTemperatureAlarm": "Al<PERSON><PERSON><PERSON>", "recvTime": "<PERSON><PERSON> de réception", "remove": "Alarme mobile", "report": "Pointer"}, "loginDlg": {"account": "<PERSON><PERSON><PERSON>", "connected": "<PERSON><PERSON><PERSON> connect<PERSON>", "connecting": "Serveur connecte en cours", "disconnect": "<PERSON><PERSON>ur <PERSON>", "loading": "Chargement en cours...", "logging": "Se connecter en cours", "login": "Se connecter", "loginSuccess": "Connecté avec succès", "loginTimeout": "<PERSON><PERSON>lai de connexion dépassé, veuillez vous connecter à nouveau", "name": "Nom d'utilisateur", "noHasUser": "Utilisateur n'existe pas, veuillez vous connecter à nouveau", "noLoginPermission": "Aucun droit de gestion de la planification des connexions", "oldSession": "La session de connexion a expiré, veuillez vous connecter avec votre mot de passe", "password": "Mot de passe", "passwordError": "Mot de passe incorrect, ve<PERSON><PERSON><PERSON> le saisir à nouveau", "remember": "<PERSON><PERSON><PERSON><PERSON>", "system": "SYSTEME ID", "upgradeServerTip": "La version actuelle du serveur est trop basse ({currentServerVersion}), veuil<PERSON><PERSON> passer à la version ({latestServerVersion}) !"}, "map": {"DIST": "Disdance", "E": "Est", "EN": "Nord-Est", "ES": "Sud-Est", "EbyN": "Est vers Nord", "EbyS": "Est vers Sud", "N": "Nord", "NbyE": "Nord vers Est", "NbyW": "Nord vers Ouest", "S": "Sud", "SbyE": "Sud vers Est", "SbyW": "Sud vers Ouest", "W": "Ouest", "WN": "Nord-Ouest", "WS": "Sud-Ouest", "WbyN": "Ouest vers Nord", "WbyS": "Ouest vers Sud", "clickMapGetCoordinates": "Veuillez cliquer sur la carte pour obtenir les coordonnées", "degrees": "<PERSON><PERSON><PERSON>", "distances": "Distance", "fastForward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapLevel": "Zoom", "pased": "Suspendu", "play": "Play", "quitMapMsg": "La lecture de la piste est terminée", "resetBearingToNorth": "Réinitialiser vers le Nord", "satellite": "Satellite", "selectCoordinates": "Sélectionnez les coordonnées", "selectCoordinatesTips": "Veuillez d'abord activer le contrôle de sélection de coordonnées {iconEl}, puis appuyez sur la souris et faites glisser pour sélectionner la plage, puis cliquez sur la plage sélectionnée pour confirmer le résultat.", "slowDown": "<PERSON><PERSON><PERSON>", "stop": "Stopper", "streets": "Rues", "zoomIn": "<PERSON>mer", "zoomOut": "Dézoomer"}, "msgbox": {"cannotSetAsSubOrg": "Une unité parent ne peut pas être définie comme sa propre unité enfant", "FailedReadSome": "Échec de la lecture du fichier {fileName}", "ScheduleStartTime": "Heure de début de dispaching", "ScheduleTargetChannel": "Dispatching le canal cible", "UsbServerConnectError": "Erreur de connexion du serveur terminal USB", "UsbServerConnectSuccess": "Connexion au serveur terminal USB réussie", "UsbServerNotConnected": "Le serveur de terminal USB n'est pas connecté", "addError": "Échec de l'ajout", "addSuccess": "Ajouté avec succès", "addToSql": "<PERSON><PERSON><PERSON><PERSON>r la fenêtre et ajouter un nouvel appareil", "alarmSetSendOk": "Commande de réglage de l'alarme envoyée avec succès", "allChannel": "<PERSON><PERSON>", "alreadyFirst": "Première ligne déj<PERSON>", "alreadyLast": "Dernière ligne dé<PERSON>", "areInspections": "Inspection en cours", "areaResult": "Résultats de la recherche par zone", "arrivalTimes": "Veuillez définir l'heure d'arrivée", "associatedProchatDeviceNotSet": "Aucun terminal Prochat associé n'est défini", "batchSettingChannelFailed": "Le canal de copie par lot a échoué. Veuillez réessayer plus tard", "batchSettingChannelSuccess": "Canal de copie par lot réussi", "bdNumberUnique": "Le numéro ne peut pas être répété", "bdUserNameUnique": "Le nom du contact ne peut pas être répété", "being": "En cours", "canNotDeleteProchatGatewayDevice": "Impossible de supprimer les données de l'appareil de passerelle de prochat", "canNotDeleteProchatUser": "Impossible de supprimer les données de l'utilisateur de prochat", "canNotDeleteRootUserData": "Impossible de supprimer les données du superutilisateur", "canNotDeliveredByGroup": "La commande ne peut pas être livrée par groupe", "canNotEditProchatGatewayDeviceDmrId": "Impossible de modifier l'ID DMR du périphérique de passerelle Prochat", "canNotEditProchatGatewayDeviceType": "Impossible de modifier le type d'appareil de passerelle Prochat", "canNotEditProchatGatewayType": "Impossible de modifier le type d'appareil de passerelle Prochat", "canNotEditProchatUserSeIfId": "Impossible de modifier les Série No de l'utilisateur de prochat", "canNotEditRootUserData": "Impossible de modifier les données du superutilisateur", "canNotEditSipServerGatewayType": "Impossible de modifier le type de gateway SIP", "cancelAlarmCmd": "Annuler la commande du réglage de l'alarme", "cancelEGChannelCmd": "Annuler le dispaching d'urgence", "cancelGPSVirCmd": "Annuler la commande du GPS virtuel", "cancelGpsInsSet": "Point de patrouille GPS annulé", "cancelMoveCtrlCmd": "Annuler la commande de surveillance mobile", "cancelSchedule": "Annuler le dispaching", "cancelSentinelCmd": "Ordre d'annulation de poste de sentinelle", "cancelTrailCmd": "Annuler la commande de suivi", "cancelVioCmd": "Annuler la commande de monitoring vocal", "cannotInputChinese": "Impossible de saisir le chinois", "cannotSendCb08Cmd": "USB Connecteur non connecté, pas de surveillance vocale", "cardNo": "No. de la carte", "centerChannelCtrl": "Commande de commutation de canal envoyée avec succès", "channel": "Canal", "channelBusy": "Canal occupé", "channelCtrlSendOk": "Commande de canal dispaching envoyée avec succès", "channelNotSetListenGroup": "{channel}Le canal n'est pas configuré avec un groupe de réception et,les appels ne peuvent pas être programmés correctement", "character": "<PERSON><PERSON><PERSON>", "checkADevice": "Veuillez sélectionner un appareil pour importer les données GPS originales", "clearAlarmOk": "Désactiver l'alarme réussi", "clearEGAlarm": "Désactiver l'alarme d'urgence", "clearSendListenSt": "<PERSON><PERSON><PERSON> le verrouillage", "configSuccess": "La configuration est réussie", "confirmAgainToDel": "Veuillez vérifier à nouveau pour la suppression", "confirmGpsInsSet": "Point de patrouille GPS activé", "controllerGatewayUniqueDevice": "Le dispositif de gateway téléphonique ne peut associer qu'un seul relais", "controllerGatewayUniquePhonePos": "Une interface de téléphone ne peut être associée qu'à un seul gateway téléphonique", "ctrlData": "Appareil data", "ctrlDisConnect": "Appareil est déconnecté", "ctrollerFailureAlarm": "<PERSON><PERSON><PERSON> d'appareil", "currentCanInput": "Entrées actuellement disponibles", "currentRfidTime": "Ce temps de lecture de carte", "currnetLocate": "Position actuelle", "dc01NotFoundLinePoint": "Aucune donnée de point de patrouille trouvée, numéro de carte de point de patrouille：{rfid}", "dc01UserDataNotFound": "Données utilisateur non trouvées, numéro de carte RFID :{rfid}", "delError": "Échec de la suppression", "delLinePointError": "Le point de patrouille est utilisé par l'itinéraire de patrouille, veuillez d'abord supprimer l'itinéraire de patrouille", "delOutsidePermissions": "Impossible de supprimer des données en dehors des permissions", "delSuccess": "Supprimé avec succès", "deleteChannelFailed": "La suppression a échoué, il n'est pas possible de supprimer le canal actuel", "deviceData": "Appareils data", "deviceFault": "Défaut de l'appareil", "deviceModelError": "<PERSON><PERSON><PERSON> de mod<PERSON>", "deviceNotReturnData": "Le terminal n'a pas retourné de données", "deviceTerminateReadOpt": "Terminal a terminé l'opération de lecture", "disDelOrg": "Le point de patrouille sous cette unité est référencé par d'autre itinéraire, veuillez d'abord supprimer l'itinéraire de patrouille", "disconnectDevice": "Impossible de connecter l'appareil", "downloadFailed": "Download é<PERSON><PERSON>", "downloading": "téléchargement en cours", "dui": "O<PERSON>", "duplicateName": "Nom répété，veuillez le ressaisir", "emptyDMRID": "DMRID Ne peut pas être vide", "emptyText": "<PERSON><PERSON><PERSON> donnée disponible", "enableAlarmCmd": "Activer la commande de réglage de l'alarme", "enableEGChannelCmd": "Activer le dispaching d'urgence", "enableGPSVirCmd": "<PERSON><PERSON><PERSON> la commande du GPS virtuel", "enableInAndOut": "Activer la commande pour les limites d'entrée et de sortie", "enableInCtrlCmd": "Activer la commande pour entrer dans la limite", "enableMoveCtrlCmd": "Démarrer la commande de surveillance mobile", "enableOutCtrlCmd": "Activer la commande pour sortir de la limite", "enableSentinelCmd": "Activer la commande de poste de sentinelle", "enableTrailCmd": "Activer la commande de suivi", "enableVioCmd": "Activer la commande de monitoring vocal", "endCall": "<PERSON><PERSON> terminé", "enterCorrectDMRID": "Veuillez entrer le DMRID correct", "enterCorrectLandlinePhoneNumber": "Veuillez saisir le numéro de téléphone fixe", "enterCorrectMobilePhoneNumber": "Veuillez saisir le numéro de téléphone portable correct", "enterCorrectPhoneNumber": "Veuillez saisir le numéro de téléphone correct", "example": "Exemple", "exportContacts": "Les données des contacts exportées(unité,appareil),Veuillez importer dans la programmation！", "exportError": "L'exportation des données a échoué", "exportSuccess": "<PERSON><PERSON><PERSON>", "fence": "Cl<PERSON><PERSON>", "fenceCmdSendOk": "Envoyer la commande de E-clôture avec succès", "firstConnectTC918": "Veuillez d'abord connecter au terminal ou au programme TC918", "fixLastRfidTimeWarning": "L'heure de lecture de la dernière carte est anormale, est-elle corrigée à l'heure de lecture actuelle de la carte?", "fixed16NumbersOrUppercaseLetters": "Fixe 16 chiffres ou lettres majuscules", "getCtrlDataError": "Échec de l'obtention des données des appareils,veuillez vous reconnecter", "getDevDataError": "Échec de l'obtention des données des appareils, veuillez vous reconnecter", "getIotDataError": "Échec de l'obtention des données des terminaux IdO,ve<PERSON><PERSON><PERSON> vous reconnecter", "getLineDataError": "Échec de l'obtention des données des itinéraires de patrouille,veuille<PERSON> vous reconnecter", "getLinePointDataError": "Échec de l'obtention des données des points de patrouille,veuil<PERSON><PERSON> vous reconnecter", "getMapPointDataError": "Échec de l'obtention des données des points,veuillez vous reconnecter", "getOrgDataError": "Échec de 'obtention des données des unités, veuillez vous reconnecter", "getRuleDataError": "Échec de l'obtention des données des règles de patrouille,veuil<PERSON><PERSON> vous reconnecter", "getUserDataError": "Échec de l'obtention des données des utilisateurs, veuillez vous reconnecter", "getUserTitleDataError": "Échec de l'obtention des données des postes，veuillez vous reconnecter", "gotNoRegisterCtrlData": "Données sur les nouveaux équipements non enregistrés reçues", "gotNoRegisterDevData": "Données sur les nouveaux terminaux non enregistrés reçues", "gotRepeatCmd": "<PERSON><PERSON><PERSON> répé<PERSON> de la commande", "gpsVirSendOk": "Commande de points de patrouille virtuels envoyée avec succès", "handRoam": "Roaming manuel", "highFrequency": "Fréquence trop haut", "importSuccess": "<PERSON><PERSON><PERSON><PERSON>", "importValidJsonFile": "Veuillez importer un fichier de format valide json.", "inTheRange": "Dans la limite", "inputErrAndSeeHelpInfo": "<PERSON><PERSON><PERSON> <PERSON>, ve<PERSON><PERSON><PERSON> consulter les informations d'aide", "inputNotValid": "Entrée DMRID non valide", "insLineNotHasPoint": "Veuillez mettre à jour l'itinéraire car le point de patrouille a été supprimé", "insPointsCar": "<PERSON><PERSON>", "intoAlarm": "Alarme dans la limite", "intoGang": "Dans la limite et remise en service", "invalidIdentityInfo": "Information d'identification non valide", "invalidLngLat": "Longitude et latitude invalides", "ipAddress": "Addresse IP", "isClockIn": "Se pointer au travail", "isPunchShift": "Se pointer pendant un quart de travail", "lastData": "Data final", "lastLocate": "Positionnement final", "lastRfidTime": "<PERSON><PERSON> <PERSON> dernière lecture", "licenceIsFull": "Nombre maximal de licences atteint", "lineData": "Itinéraires de patrouille data", "linePointData": "Points de patrouille data", "locateCmdSendOk": "Envoyer la commande de positionnement", "locked": "<PERSON><PERSON><PERSON><PERSON>", "loginExpiredAndLoginAgain": " La session de connexion a expiré, veuillez vous connecter à nouveau !!", "loginNameUnique": "Comptes de connexion répété", "loginNetwork": "Se connecter au réseau", "loginSessionExpired": "La session de connexion a expiré, veuillez vous reconnecter ou attendre la connexion automatique", "lowFrequency": "<PERSON><PERSON><PERSON> trop basse", "mapPointData": "Points data", "matchAnyNumber": "Correspond à n'importe quel nombre arbitraire", "matchArbitraryNo": "Correspond à un nombre arbitraire", "maxLen": "Longueur max", "maxSelTime": "Les données ne sont disponibles que pour un maximum de trois mois", "minLen": "Longueur minimale", "moveCtrlSendOk": "Commande de surveillance mobile envoyée avec succès", "mustBe10Len": "La clé doit avoir une longueur de 10", "mustBeHex": "Doit être un nombre hexadécimal", "mustBeHexChar": "Doit être un caractère hexadécimal", "mustIp": "Doit être une ip adresse", "mustIpOrDomain": "Doit être une IP ou un domaine", "mustLength": "Caractères{len}doivent être saisis", "mustNumber": "Chiffres uniquement", "nameCannotRepeated": "Le nom ne peut pas être répété", "newCard": "Nouvelle carte", "nextTimeEffect": "Le réglage sera valide lors de la prochaine vérification!", "noDMRID": "No ID DMR,Veuillez définir d'abord", "noRecords": "Aucun enregistrement", "noResponseSatellitePositioningSwitchCommand": "Aucune réponse à la commande du commutateur de positionnement par satellite", "noTarget": "La cible n'existe pas", "noTerminalQuotaAvailable": "Pas de quota de terminaux disponible", "notDynamicGroupPerm": "Pas d'autorisation de groupes dynamiques", "notEdit": "Aucune autorisation de modification des données！", "notEditSefl": "Vous ne pouvez pas modifier vos propres données！", "notEditUserPerm": "Pas d'autorisation de modifier les données d'autorisation de l'utilisateur", "notHasLinePoint": "Il n'y a pas de points de patrouille sur l'itinéraire, ve<PERSON><PERSON><PERSON> mettre à jour", "notHasOrg": "Unité sélectionnée n'existe pas", "notIntercomWf": "Pas d'autorisation de programmer les walkie-talkies", "notLinePoint": "Aucune donnée disponible pour ce point de patrouille", "notRepeaterWf": "Pas d'autorisation de programmer les relais", "notResAlarmCmd": "Aucune réponse aux commandes de réglage des alarmes", "notResAreaCmd": "Pas de réponse à la commande de recherche de zone", "notResCenterChCtrl": "Pas de réponse au changement de canal sur le centre", "notResClearAlarmCmd": "Aucune réponse à la commande d'annulation d'alarme", "notResEGChannelCmd": "Aucune réponse à la commande de dispaching d'urgence", "notResFenceCmd": "Aucune réponse à la commande de E-clôture", "notResGPSVirSet": "Pas de réponse à la commande de GPS virtuel", "notResLockDevCmd": "Aucune réponse à la commande de verrouillage", "notResMoveCtrlCmd": "Aucune réponse aux commandes de surveillance mobile", "notResPositionCmd": "Aucune réponse aux commandes de contrôle du positionnement", "notResPowerOnCmd": "Aucune réponse à la commande de démarrage", "notResSelectCmd": "Aucune réponse à la commande de recherche de l'état du verrouillage", "notResSentinelCmd": "Pas de réponse pour la commande GPS clôture", "notResTextMsgCmd": "Aucune réponse à la commande d'envoi de SMS", "notResTrailCmd": "Pas de réponse pour la commande de suivi", "notResVioCmd": "Aucune réponse aux commandes de moniteur vocal", "notSendCmd": "Pas d'autorisation d'envoyer des commandes！", "notSetNetworkCallAgent": "La configuration de l'agent d'appel réseau n'est pas définie", "notSupportSatellitePositioning": "Ne prend pas en charge le positionnement par satellite", "notVoipService": "Pas d'informations sur le serveur vocal, pas de connexion Internet", "oldPwdError": "L'ancien mot de passe est incorrect", "onCalling": "A<PERSON>er en cours", "openEmergencySchedule": "Activer en urgence le dispaching du réseau des stations de base", "orgData": "Unités data", "orgHasOtherData": "Les données existent déjà sous cette unité et ne peuvent pas être changées en une unité virtuelle", "orgNotHasDev": "Non appareil dans cette unité", "other": "<PERSON><PERSON>", "outAlarm": "Alarme hors limite", "outGang": "Hors limite et hors service", "parentNotIsSelfOrSubUnit": "L'unité supérieure ne peut pas être elle-même ou l'unité subordonnée", "phoneBookNameNo": "Nom rép<PERSON>, veuillez saisir à nouveau", "portLimit": "Le numéro de port ne peut pas dépasser 65535", "postData": "Postes data", "poweredOff": "Power off", "poweredOn": "Power on", "processDataUpperLimitVal": "Veuillez entrer une limite supérieure plus grande!", "processMoreRecords": "Des données supplémentaires doivent-elles être traitées ? Si ce n'est pas le cas, annulez-les. Sinon, saisissez des données limitées.。", "processMoreRecordsErrMsg": "L'entrée est invalide!", "prochatArgsNotSet": "Prochat gateway mal configuré", "prochatDeviceIsExisted": "Ce périphérique prochat existe déjà", "prochatGatewayExist": "Prochat gateway existe déjà", "programmingPwdError": "Mot de passe de programmation incorrect", "queryCtrlHistory": "Vérifiez l'historique en ligne des appareils", "queryFenceCmd": "Vérifier la commande de clôture GPS", "queryHistoryFail": "Le serveur est déconnecté, recherche échoué！", "querySatellitePositioningStatus": "Interroger l'état de positionnement par satellite", "queryTrailCmd": "Vérifier la commande de suivi", "querying": "Vérifier en cours......", "readAbnormal": "Lecture de données anormales", "readDataFailed": "Échec de la lecture des données", "readDeviceInfoFailed": "Échec de la lecture des informations sur l'appareil", "readFileSuccessAndFaile": "<PERSON>chier lu avec succès: {success} fi<PERSON><PERSON>, échec de la lecture du fichier: {fail} fichiers", "readIdentityInfoFailed": "Échec de la lecture des informations d'identité", "readRecordListFailed": "Échec de la lecture de la liste des fichiers d'enregistrement", "readResourceVersionInfoFailed": "Échec de la lecture des informations sur la version de la ressource", "readSuccess": "Lu avec succès", "reading": "<PERSON><PERSON>", "receiveData": "Réception des données", "receiveLocateInfo": "Recevoir les données de position", "receivedSMS": "SMS reçu de{sender}à%{sendTime}", "repeatDMRID": "DMRID répété，veuillez le saisir à nouveau", "repeatDevsName": "Nom de l'appareil r<PERSON>, veuil<PERSON><PERSON> le saisir à nouveau", "repeatNo": "No.r<PERSON><PERSON><PERSON><PERSON>，veuillez le saisir à nouveau", "repeatOrgShortName": "Nom de l'unité répété，veuillez le saisir à nouveau", "repeatPointRfid": "RFID répété，veuillez le saisir à nouveau", "repeatPostName": "Titre du poste répété，veuillez le saisir à nouveau", "repeatSipNo": "No.SIP répété，veuillez le saisir à nouveau", "repeaterBusy": "Canal du relais occupé", "repeaterParametersError": "Il y a une erreur dans les paramètres, veuillez vérifier les réglages", "repeaterQueryFailed": "La cible n'existe pas", "repeaterVirtualDeviceRepeatDmrId": "DMRID de l'interphone virtuel du relais répétée", "repeaterVirtualDeviceRepeatSelfId": "Nom de l'interphone virtuel du relais répété", "repeaterWriteFail": "Défaut de la programmation du relais", "repeaterWriteSuccess": "Programmation du relais réussie,effective après le redémarrage", "repeaterWriteSuccessEffect": "Programmation de relais réussie, entrée en vigueur immédiatement", "resAlarmSetCmd": "<PERSON><PERSON><PERSON><PERSON><PERSON> à la commande de réglage de l'alarme", "resCb09Cmd": "Répondre pour tuer et réactiver à distance", "resCenterChannelCtrl": "Répondre à une commande de commutation de canal", "resChannelCtrlCmd": "Répondre à la commande de canal dispaching", "resClearLockDev": "Ré<PERSON>nd<PERSON> à la commande de déverrouillage", "resDevLockSt": "Répondre aux commandes de tuer et de réactiver à distance", "resDisListen": "<PERSON>é<PERSON><PERSON><PERSON> à la commande de verrouillage de la réception", "resDisSend": "Ré<PERSON>nd<PERSON> à la commande de verrouillage de la transmission", "resDisSendListen": "Répondre à la commande de verrouillage pour recevoir et transmettre", "resFenceCtrlCmd": "<PERSON><PERSON><PERSON><PERSON><PERSON> à la commande de E-clôture", "resGpsVirCmd": "Ré<PERSON>nd<PERSON> à la commande de points de patrouille virtuels", "resLocateCmd": "<PERSON><PERSON><PERSON><PERSON><PERSON> à la commande de positionnement", "resMoveCtrlCmd": "Répondre à la commande de surveillance mobile", "resSentinelCtrlCmd": "<PERSON><PERSON><PERSON><PERSON><PERSON> à la commande de poste de sentinelle avec succès", "resTrailCtrlCmd": "<PERSON><PERSON><PERSON><PERSON><PERSON> à la commande de suivi", "resVioCtrlCmd": "Répondre à la commande de surveillance vocale", "resetPwdError": "Les mots de passe saisi deux fois ne sont pas le même", "resolveDmrId": "Veuillez saisir DMRID，en commençant par 0x en hexadécimal", "resolveDmrIdErr": "DMRID Chiffre erron<PERSON>", "resolveDmrIdFailed": "L'analyse de la DMRID a échoué, veuillez entrer la DMRID correcte", "resolveDmrIdMsg": "Veuillez saisir 8 chiffres DMRID", "resolveDmrIdTitle": "Cliquez pour analyser DMRID", "respond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ruleData": "Règles data", "ruleDateAlert": "L'heure de fin de la date valide ne peut pas être inférieure à l'heure de début", "salerId": "Le système n'est pas confuguré d'une ID de distributeur", "satellitePositioningTurnedOff": "Positionnement par satellite désactivé", "satellitePositioningTurnedOn": "Positionnement par satellite activé", "scheduleModal1": "Dispatching des appels de groupe 4", "scheduleModal2": "Dispatching des appels de groupe 3", "scheduleModal3": "Dispatching des appels de groupe 2", "scheduleModal4": "Dispatching des appels de groupe de canal", "scheduleModal5": "Dispatching des appels de groupe de stations de base", "scheduleModal6": "Niveau 3(3e Admin appareil)Dispatching d'appel de group", "scheduleModal7": "Niveau2、3(2e Admin appareil)Dispatching d'appel de group", "scheduleModal8": "Niveau 3(2e Admin appareil)Dispatching d'appel de group", "scheduleModal9": "Niveau 2(2e Admin appareil)Dispatching d'appel de group", "scheduleModalA": "Niveau1,2(1er <PERSON>min appareil)Dispatching d'appel de group", "scheduleModalB": "Niveau 2(1er <PERSON><PERSON> appareil)Dispatching d'appel de group", "scheduleModalC": "Dispatching dynamique des appels de groupe", "scheduleModalD": "Dispatching des appels de groupe niveau 1", "scheduleModalE": "Dispatching du système tous appels", "scheduleModalF": "Dispatching du réseau des stations de base", "selAlarmCmd": "Rechercher la commande de réglage de l'alarme", "selError": "Vé<PERSON>", "selGPSVirCmd": "Vérifier la commande du GPS virtuel", "selImgError": "Veuillez sélectionner une image plus petite que 100kb", "selLinePoint": "Veuillez sélectionner point de patrouille", "selLockDevSt": "Vérifier l'état du verrouillage", "selMoveCtrlCmd": "Rechercher les commandes de surveillance mobile", "selSentinelCmd": "Rechercher de commande de poste de sentinelle", "selSuccess": "Vérification réussie", "selTimeError": "L'heure n'est pas réglée correctement, veuillez la régler à nouveau", "selectGpsPoint": "Sélectionnez un point de patrouille GPS", "selectLngLat": "Veuillez définir la latitude et la longitude", "selectTarget": "Veuillez sélectionner la destination de la commande", "selectTime": "Veuillez choisir l'heure", "sendAreaCmd": "Envoyer une commande de recherche de zone", "sendClearAlarmCmd": "Envoyer la commande d'annulation d'alarme", "sendError": "Échec de l'envoi de la commande", "sendPositionCmd": "Envoyer commandes de surveillance du positionnement", "sendSuccess": "La commande a été envoyée", "sendTextMsgCmd": "Commande d'envoi de notification par SMS", "sentinelCtrlSendOk": "Envoyer la commande de poste de sentinelle avec succès", "serverReconnect": "Le serveur a redémarré, veuillez actualiser et réessayer", "serverRestartAndConnectFailed": "Déconnecté du serveur, la connexion automatique a échoué, veuillez vous reconnecter", "setDisListenCmd": "<PERSON><PERSON><PERSON> le verrouillage de la réception", "setDisSLCmd": "Verrouiller de la transmission et de la réception", "setDisSendCmd": "<PERSON><PERSON><PERSON> le verrouillage de la transmission", "setPriPrompt": "Confirmez ou annulez d'abord le réglage de l'autorité", "setupListenGroupLimit": "Jusqu'à {limit}groupes de réception", "shortNoRepeated": "Le numéro court ne peut être répété", "showActivePointAlert": "Le point actif est de faible puissance", "sipArgsNotSet": "SIP gateway mal configuré", "sipProtocolDevicePasswordIsNull": "Le mot de passe du terminal protocole SIP ne peut pas être vide.", "sipServerArgsNotSet": "SIP server gateway mal configuré", "sipServerGatewayExist": "SIP gateway existe déjà", "slotlimit": "Ne peut pas supprimer lorsqu'en canal 1", "smsLengthInput": "@:msgbox.smsMaxLengthInput，@:msgbox.currentCanInput{curLen}@:msgbox.character", "smsMaxLengthInput": "@:msgbox.maxLen{len}@:msgbox.character", "smsNotEmpty": "Le contenu SMS ne peut pas être vide", "softwareDownloadFailed": "@:msgbox.downloadFailed,impossible de trouver le logiciel", "sortAdvice": "Sugg<PERSON>rer de trier par 10", "startMove": "Commencez à bouger", "startReading": "Commencer à la lecture les données", "startWriting": "Commencer à écrire les données", "stopMove": "<PERSON><PERSON><PERSON><PERSON><PERSON> bouger", "success": "<PERSON><PERSON>", "superAdminWarning": "Un compte dont l'unité est root ou vide ne peut pas envoyer de commandes. Utilisez un compte d'unité non root ou non vide, s'il vous plaît！", "switchRepeaterChannelFailed": "Échec de la commutation des canaux de relais", "switchRepeaterChannelSuccess": "Commutation des canaux de relais réussie", "switchRepeaterFrequencyFailed": "Échec de la commutation de la fréquence du relais", "switchRepeaterFrequencySuccess": "Commutation de la fréquence du relais réussie", "symbolDescription": "Description du symbole", "sysIdError": "système ID incorrect", "sysSetError": "Réglage du système échoué", "sysSetSuccess": "Réglage du système réussi", "targetInCalling": "La cible dans l'appel", "targetNotLogin": "La cible n'est pas connectée", "targetNotOnline": "La cible n'est pas en ligne", "targetNotSelf": "La cible d'appel ne peuvent pas être pour elles-mêmes", "telephoneNotSetChannelData": "{type}l'équipement n'a pas besoin de définir les données de canal", "theCardInfoOutDate": "Les informations de lecture de cette carte sont obsolètes! <PERSON><PERSON> de la dernière lecture：{lastTime}, <PERSON><PERSON> de la lecture cette fois-ci ：{thisTime}", "timeEmpty": "Le temps ne peut être vide", "trailCtrlCmdOk": "Envoyer la commande de suivi avec succès", "tryAgainQueryChannelData": " <PERSON><PERSON><PERSON>, certaines données de canal ne peuvent pas être reçues, ve<PERSON><PERSON><PERSON> réessayer plus tard", "turnOffSatellitePositioning": "Désactiver le positionnement par satellite", "turnOnSatellitePositioning": "Activer le positionnement par satellite", "unInTheRange": "Hors limite", "unLocked": "Déver<PERSON><PERSON><PERSON>", "unableDeleteRepeaterVirtualDevice": "Impossible de supprimer l'interphone virtuel du relais, ve<PERSON><PERSON><PERSON> réessayer", "unableRequestTerminalQuota": "Impossible de demander le quota de terminaux", "unknownCmd": "Commande inconnue", "upError": "Mise à jour é<PERSON>ué", "upLoadLocateInfo": "Upload les données de positionnement", "upSuccess": "Mise à jour avec succès", "updataClientLogo": "Mettre à jour le Logo du système", "updataClientTitle": "Mettre à jour le titre du défilement du système", "updateDeviceChannelSuccess": "Mise à jour du canal réussie", "updateLineDetailFaile": "Echec de la mise à jour des détails de la ligne", "updatePointType": "Le point de patrouille a été mis à jour,ve<PERSON><PERSON><PERSON> régler!", "updateUsbServerVersion": "Veuillez mettre à jour la version du USB-Connecteur, version minimale:{required}，version actuelle:{curVersion}", "upgradeRepeaterVirtualDeviceFailed": "La mise à jour du terminal virtuel du relais a échoué", "upgradeSipGatewayDeviceFailed": "La mise à jour du terminal de gateway SIP a échoué", "uploadTip": "Seuls les fichiers {type} peuvent être upload, et pas plus de {size}kb", "upperLimit": "Les données ont déjà atteint la limite", "usbDeviceHasBeenOut": "Équipement USB est débranché", "usbDeviceInsertSuccess": "Équipement USB est inséré", "useInRule": "Supprimer échoué，l'itinéraire de patrouille a été référencé par la règle, veuillez d'abord supprimer la règle", "userData": "Utilisateurs data", "userIDCard": "Carte de ID", "validDomain": "Veuillez entrer le nom de domaine correct", "validFileSize": "Veuillez choisir un fichier plus petit que 100k", "validHost": "Veuillez entrer le nom de domaine correct ou IP", "validIp": "Veuillez entrer la correcte IP", "validMaxNumber": "Veuillez entrer un nombre plus grand que le minimum", "validMinNumber": "Veuillez entrer un nombre plus petit que le maximum", "vioCtrlSendOk": "Commande de surveillance vocale envoyée avec succès", "writeActiveRFIDConfigFailed": "Échec de l'écriture dans la configuration RFID active", "writeAddressBookFailed": " Échec de l'écriture dans la liste des contacts", "writeAddressBookGroupFailed": "Échec de l'écriture dans le groupe de la liste des contacts", "writeAlarmConfigFailed": "Échec de l'écriture des données de configuration de l'alarme", "writeAnalogEmergencyAlertFailed": "Échec de l'écriture de l'alerte d'urgence analogique", "writeAttachmentSettingsFailed": "Échec de l'écriture des données de la pièce jointe", "writeBluetoothDataFailed": "Échec de l'écriture des données de configuration Bluetooth", "writeChannelDataFailed": "Échec de l'écriture des données du canal", "writeDeySettingsFailed": "Échec de l'écriture des données de réglage du clavier", "writeDigitalAlarmFailed": "Échec de l'écriture des données numériques de l'alarme d'urgence", "writeDmrBasicConfigFailed": "Échec de l'écriture de la configuration de base DMR", "writeEmergencyAlarmConfigFailed": "Échec de l'écriture dans la configuration de l'alarme d'urgence", "writeEncryptConfigFailed": "Échec de l'écriture des données de configuration cryptées", "writeEncryptKeyFailed": "Échec de l'écriture des données de clés", "writeEncryptionAES256KeyFailed": "Échec de l'écriture des données AES256", "writeEncryptionARC4KeyFailed": "Échec de l'écriture des données ARC4", "writeEncryptionARSKeyFailed": "Échec de l'écriture des données AES", "writeGPIODataFailed": "Échec de l'écriture des données GPIO", "writeGpsDataFailed": "Échec de l'écriture des GPS data", "writeInFail": "Impossible d'écrire les données", "writeInSuccess": "Écrit avec succès", "writeLevel1ZoneFailed": "Echec de l'écriture des données de la zone de niveau 1", "writeLevel2ZoneFailed": "Échec de l'écriture des données de la zone de niveau 2", "writeLevel3ZoneFailed": "Échec de l'écriture des données de la zone de niveau 3", "writeMenuFailed": "Échec de l'écriture des données du menu", "writePatrolSystemConfigFailed": "Échec de l'écriture de configuration du système de patrouille", "writePhoneBookFailed": "Échec de l'écriture des données du répertoire téléphonique", "writeProgrammingPwdFailed": "Échec de l'écriture du mot de passe de programmation", "writeReadPasswordFailed": "L'écriture du mot de passe de lecture de programmation a échoué", "writeReceivingGroupFailed": "Échec de l'écriture dans le groupe de réception", "writeRegularSettingsFailed": "Échec de l'écriture dans les réglages régulier", "writeRescueAndSOSChannelFailed": "Échec de l'écriture du canal Rescue/SOS", "writeRescueAndSOSConfigFailed": "Échec de l'écriture de la configuration Rescue/SOS", "writeRoamConfigFailed": "Échec de l'écriture des données de configuration de roaming", "writeRoamListFailed": " Échec de l'écriture des données de la liste de roaming", "writeSMSFailed": "Échec de l'écriture des données SMS", "writeScanConfigFailed": "Échec de l'écriture des données de configuration du scan", "writeScanListFailed": "Échec de l'écriture des données de la liste de scan", "writeSignalingSystemFailed": "Échec de l'écriture des données dans le système de signalisation", "writeSiteInfoFailed": "Échec de l'écriture des informations sur le site", "writeTraceMonitorConfigFailed": "Échec de l'écriture dans la configuration de la surveillance de trace", "writeUiConfigFailed": "Échec de l'écriture de la configuration de l'interface d'utilisateur", "writeUpsideDownConfigFailed": "Échec de l'écriture de la configuration à l'envers", "writeValidChannelFailed": "Échec de l'écriture  des données de canal valide", "writeVirtualClusterFailed": "Échec de l'écriture dans les données du trunking virtuel", "writeWorkAloneConfigFailed": "L'écriture de la configuration de travailleur isolé a échoué", "writeWritePasswordFailed": "L'écriture du mot de passe en écriture de programmation a échoué", "writeZoneDataFailed": "Échec de l'écriture des données de la zone"}, "nav": {"BFWebsite": "Site officiel de Belfone", "GPSpathHistory": "Traces GPS", "InspectionHistory": "<PERSON><PERSON><PERSON><PERSON>", "InspectionRules": "<PERSON><PERSON><PERSON> patrouille", "activePatrolPointAlarm": "Historique des alarmes des points de patrouille actif", "activePointLowVoltageAlarm": "Alarme de faible voltage du point actif", "alarmHistory": "Alarmes", "authorization": "Informations sur les licences", "baseStationSchedule": "Contrôleur B/S", "clientVersion": "Version client", "command": "Commande", "contacts": "Exporter la liste des contacts", "crudHistory": "Historique des opérations", "ctrlData": "Gestion des équipements", "data": "Data", "enquiry": "Recherche", "help": "Aide", "helpAbout": "À propos", "helpContent": "Documentation du système", "home": "Page de garde", "interphoneWriteFrequency": "Programmation des terminaux", "iotDeviceHistory": "Historique des terminaux IdO", "leadingInGpsData": "Importer de données GPS originales", "lineData": "Itinéraire de patrouille", "linePointData": "Gestion des points de patrouille", "mapPointData": "Points dans la carte", "orgData": "Gestion des unités", "postData": "Gestion des postes", "readerCardHistory": "Horaires de travail", "relatedSoftware": "Software relatif", "repeaterWriteFrequency": "Programmation des relais", "runNotes": "Système Logs", "sendCommand": "Dispatch", "serverVersion": "Version serveur", "smsHistory": "Historique des SMS", "soundHistory": "Enregistrements des voix", "startRuningTime": "Temps de fonctionnement", "switchHistory": "Power on/off", "systemLog": "Journal du système", "userData": "Gestion des utilisateurs", "version": "Version", "versionBuildTime": "Temps de construction", "versionGitTag": "Version Git"}, "operations": {"Add": "Ajouter", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Insert": "<PERSON><PERSON><PERSON><PERSON>", "PUpdate": "Modifier(certaines paramètres)", "Update": "Mettre à jour", "db_base_station": "Stations de base", "db_controller": "contr<PERSON><PERSON><PERSON>", "db_controller_gateway_manage": "Gestion des relations entre les appareils du gateway téléphonique", "db_device": "Interphones", "db_device_channel_zone": "Données de zone de canal terminal", "db_device_power_onoff": "Power on/off de l'appareil", "db_device_register_info": "Infos d'enregistrement des appareils", "db_dynamic_group_detail": "Membre du groupe dynamique", "db_image": "Image de l'utilisateur", "db_iot_device": "Terminaux IdO", "db_line_detail": "<PERSON><PERSON><PERSON> de la ligne d’inspection", "db_line_master": "Patrouille la ligne", "db_line_point": "Points d'itinéraire", "db_map_point": "Points de la carte", "db_org": "Données d'unité", "db_org_dynamic_group": "Groupe dynamique", "db_phone_gateway_filter": "Liste noire du téléphone", "db_phone_gateway_permission": "Autorisation de gateway téléphonique", "db_phone_no_list": "Répertoire de numéros de téléphone prédéfini", "db_phone_short_no": "Numéro court téléphonique", "db_rfid_rule_master": "<PERSON><PERSON><PERSON>", "db_sys_config": "Réglages du système", "db_user": "Utilisateurs", "db_user_privelege": "Données de privilège utilisateur (opération indirecte)", "db_user_session_id": "ID de session utilisateur", "db_user_title": "Titres des utilisateurs"}, "repeaterStatus": {"ant": "<PERSON><PERSON><PERSON>", "antValue": "SWR", "fan": "éventail ", "gps": "GPS ", "gpsStatus": "Statut GPS", "notInstalled": "Pas <PERSON>", "notSynced": "non synchronisé", "repeaterStatus": "état du relais", "rxPll": "Recevoir ", "signalInterference": " interférence de signal", "synced": "synchronisé", "temperature": "température", "thereIsInterference": "Il y a une interférence", "txPll": "Transmettre ", "updateTime": "Temps de mise à jour", "voltage": "Tension"}, "repeaterWriteFreq": {"abnormal": "Anormal", "hasLocate": "Ciblé", "locked": "Fermé à clé", "normal": "Normal", "notDetected": "Non-détecté", "notLocate": "Non ciblé", "notLocked": "Non verrouillé", "repeaterDevice": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "repeaterModel": "<PERSON><PERSON><PERSON><PERSON>", "state": {"antStatus": "Statut de l'antenne", "fanStatus": "Statut du ventilateur", "freeRelay": "Que ce soit pour relayer librement", "gpsStatus": "Statut de synchronisation GPS", "pllStatus": "Statut PLL", "rxStatus": "Statut rx", "sessionSlot1": "Statut de la session de slot 1", "sessionSlot2": "Statut de la session de slot 2", "tempStatus": "Statut de la température", "txStatus": "Statut tx", "volStatus": "Statut de voltage"}, "stateInfo": "Info de l'état", "tooHigh": "Trop haut", "tooLow": "Trop bas"}, "software": {"recordingPlayer": "Player d'enregistrement", "refresh": "Actualiser", "refreshFailed": "Act<PERSON><PERSON><PERSON>", "refreshSuccess": "<PERSON><PERSON><PERSON><PERSON>", "usbTerminalServer": "Serveur de terminaux USB"}, "syncCenter": {"warning": "Avertissement", "orgDeleteAndLogout": "Votre unité a été supprimée et vous serez déconnecté !", "controller": "Données des appareils synchronisées", "controllerGateway": "Terminal de gateway d'appareil synchronisé", "device": "Données des terminaux synchronisées", "lineDetail": "Données des détails des itinéraires de patrouille synchronisées", "lineMaster": "Données des itinéraires de patrouille synchronisées", "linePoint": "Données des points de patrouille synchronisées", "mapPoint": "Données de points de repère synchronisées", "org": "Données des unités synchronisées", "phoneGatewayFilter": "Liste noire et blanche des téléphones synchronisés", "phoneGatewayPermission": "Téléphone synchronisé Licence de la passerelle", "phoneNoList": "Répertoire téléphonique prédéfini synchronisé", "phoneShortNo": "Numéro court du gateway téléphonique synchronisée", "ruleMaster": "Données des règles de patrouille synchronisées", "user": "Données des utilisateurs synchronisées", "userTitle": "Données des titres synchronisées"}, "tree": {"collapseAll": "Tout plier", "deselectAll": "Supp<PERSON>er tout", "displayAllDev": "<PERSON><PERSON><PERSON><PERSON> tout", "expandAll": "<PERSON><PERSON> d<PERSON>", "filter": "<PERSON>uez une recherche", "online": "En ligne", "quickCall": "Appel rapide", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "status": "État des équipements"}, "writeFreq": {"BUSYLineOutputEffectiveLevel": "Niveau actif de sortie de ligne BUSY", "ChannelAndVolume": "Canal et volume", "DefaultGroupCall": "Appel de groupe par défaut", "Expirez": "Expirez", "GMTStandard": "GMT standard", "PTTYLineOutputEffectiveLevel": "Niveau actif de sortie de ligne PTT", "SVTSiteInfo": "SVT info sur site", "TDMAThroughMode": "TDMA mode direct", "aLocateFunc": "A (mandown +alarme de travailleur isolé)", "activeSiteEnable": "Recherche de site active", "activeSiteRoamingEnable": "Activer site roaming", "activeSiteSearch": "Recherche de site active", "activeSiteSearchTiming": "D<PERSON>lai de recherche de site actif (s)", "addArea": "Ajouter zone", "addChannel": "Ajouter canal", "addressBookGroup": "Groupe de contacts", "addressGroup": {"selectedContact": "Contact sélectionné", "ungroupedContact": "Contact non groupé"}, "advanced": "<PERSON><PERSON>", "advancedEncryptionAES256": "Cryptage avancé AES256", "advancedEncryptionARC4": "Cryptage avancé ARC4", "advancedEncryptionARS": "Cryptage avancé AES", "aes256List": "Liste AES256", "airAuthKey": "Clé d'authentification aérienne", "airEncryption": "Cryptage aérien", "alarmAndWhistle": "Al<PERSON><PERSON> et sifflet", "alarmAutoSendGps": "L'alarme envoie automatiquement d'info GPS", "alarmSetting": "Réglage de l'alarme", "alarmSquelchMode": "Mode de squelch d'alarme", "alarmTone": "Son de l'alarme", "alias": "<PERSON><PERSON>", "aliasDisp": "<PERSON><PERSON><PERSON><PERSON> des alias", "aliasEdit": "AliasEdit", "aliasMenu": "<PERSON><PERSON>", "allChannelDetection": "Détection de tous les canaux", "allIndicators": "<PERSON><PERSON> les indicateurs", "allow": "Autoriser", "allowDeleteAllRecord": "Autoriser l'effacement des fichiers d'enregistrement", "allowErasingDevice": "Autoriser l'effacement de l'appareil", "allowShake": "Autoriser les vibrations", "allowedSelfDestruct": "Autoriser l'autodestruction", "always": "toujours", "analogAlert": "Alerte analogique", "analogAllowed": "Analog autorisée", "analogCallHangTime": "Temps de suspension des appels analogiques(s)", "analogChannel": "Canal analogique", "analogCompatibleDigital": "Analogique compatible numérique", "analogEmergencyAlarm": "Alarme d'urgence analogique", "analogEmergencyAlertSystem": "Système d'alerte d'urgence analogique", "arc4List": "Liste ARC4", "areaId": "ID des zones", "areaList": "Liste des zones", "areaNoCannotRepeated": "Le numéro de série sous la même zone ne peut pas être répété", "armAlarmKey": "Alarme microphone d'épaule", "authentication": "Authentification", "authenticationSecretKey": "Clé d'authentification", "auto": "Automatique", "autoBackLightTime": "Heure automatique du rétroéclairage", "autoEmergencyCall": "Appel d'urgence automatique", "autoKeyboardLock": "Verrouillage du clavier automatique", "autoKeyboardLockDelayTime": "Verrouillage automatique du clavier en cas de temporisation", "autoRoamSearchTime": "Durée de recherche en roaming automatique (s)", "autoRoaming": "Roaming automatique", "autoRoamingSearchInterval": "Intervalle de recherche en roaming automatique(s)", "autoScanning": "Scanner automatiquement", "autoSiteSearchTimer": "Minuteur de recherche automatique", "availableChannel": "Canal disponible", "back2Back": "Dos à dos", "back2BackMode": "Mode de transfert dos à dos", "backToBackEnable": "Interrupteur dos à dos", "backgroundPromptTone": "Son de l'invite de fond", "backlight": "Ré<PERSON>é<PERSON>lairage", "backlightSettings": "Réglages de rétroéclairage", "backwardsTriggered": "Backwards dé<PERSON><PERSON><PERSON>", "base": "La base", "baseSettings": "Réglages de base", "basedOnChannelDefaultGroupCall": "Basé sur l'appel de groupe par défaut de la chaîne", "batchDownload": "Téléchargement par lots", "batteryCharge": "Charge de la batterie", "batteryInfo": "informations sur la batterie", "bdsLocate": "Localisation du BDS", "beatFreq": "Battement de fréquence", "beidou": "BDS", "bluetooth": "Bluetooth", "bluetoothAudio": "Audio bluetooth", "bluetoothConnectManagement": "Gestion de la connexion Bluetooth", "bluetoothFunc": "Fnction bluetooth", "bluetoothInfo": "Informations bluetooth", "bluetoothMode": "Mode bluetooth", "bluetoothOption": "Options bluetooth", "bluetoothPTTKeep": "Bluetooth PTT Keep", "bluetoothRecordingChooseOne": "L'option Bluetooth et l'option d'enregistrement ne peuvent en choisir qu'une", "bluetoothSettings": "Paramètres bluetooth", "bluetoothSwitch": "Interrupteur bluetooth", "bootInterfaceDisp": "Affichage de l'interface de démarrage", "broadBand": "Large bande", "busyChannelLock": "Verrouillage de canal occupé", "button": "Bouton", "buttonTone": "<PERSON><PERSON><PERSON> du clavier", "callDir": "Direction vocale", "callDirectionEnable": "Informations sur la direction de l'appel", "callDisplayMode": "Mode d'affichage des appels", "callEmissionPermitConditions": "Conditions du permis d'émission de l'appel", "callHangsLed": "LED de suspension d'appel", "callMode": "Mode d'appel", "callOut": "<PERSON><PERSON><PERSON>", "callOutTone": "<PERSON><PERSON><PERSON> d'appel", "callPrompt": "Sonnerie d'appel", "callPromptTimes": "Fois de l'alerte d'appel", "callToneDecode": "Décodage d'alerte d'appel", "callToneVibration": "Vibration de l'alerte d'appel", "callingEndTone": "Son du fin d'appel", "carrier": "carrier", "carrierSquelchLevel": "Nive<PERSON> de squelch de la porteuse", "cdcss": "CDCSS", "cdcssInvert": "Reverse CDCSS", "chAreaChannelAsScanList": "Canal de zone de canal comme liste de scanner", "chTimeSlotCalibrator": "Calibreur de timeslot de canal", "channelBroadcastSound": "Son de la diffusion du canal", "channelConfig": "Configuration du canal", "channelDataFormValidate": "La vérification des données du canal a échoué", "channelLock": "Verrouillage de chaîne", "channelNotSetArea": "Il y a des zones de canaux non définies", "channelNumber": "Nombre de canaux", "chooseAnnouncer": "Choisissez un annonceur", "clearRecord": "Enregistrement effacé", "clearSms": "Effacer les messages SMS", "closePosition": "<PERSON><PERSON><PERSON> la <PERSON>", "companding": "Companding", "compandingEnable": "Companding Enable", "configName": "Nom de la liste", "confirmedDataSingleCall": "<PERSON><PERSON> privé donn<PERSON>", "connectionTimes": "Fois de connexions", "contactAlias": "Contact Alias", "contactAliasAndId": "Alias ​​et ID de contact", "contactDelete": "Contact Supprimer", "contactGroup": "Groupe de contacts", "contactId": "Contact ID", "containedChannel": "Canal contenu", "controlBusiness": "Contr<PERSON>ler les affaires", "controlCenter": "Centre de contrôle", "correctChDataTip": "Il y a une erreur dans les données du canal sous{name}, veuil<PERSON><PERSON> d'abord la corriger", "ctcss": "CTCSS", "ctcssCdcss": "CTCSS/CDCSS", "currentSlot": "Time slot actuel", "currentVolume": "Volume actuel", "customKey": "Bouton{name}", "dataCallConfirm": "Confirmation de l'appel de données", "dataCompression": "compression des données", "dateTime": {"date": "Date", "hours": "<PERSON><PERSON>", "minutes": "minutes", "month": "<PERSON><PERSON>", "year": "<PERSON><PERSON>"}, "deEmphasisAndPreEmphasis": "Dé-accentuer et pré-accentuer", "decoding": "Décodage", "defaultChannel": {"ch1": "Canal prédéfini 1", "ch2": "Canal prédéfini 2", "ch3": "Canal prédéfini 3", "ch4": "Canal prédéfini 4"}, "defaultKeyboardInput": "<PERSON><PERSON> clavier par défaut", "defaultKnobFunc": "Fonction par défaut du bouton", "defaultPriCall": "Appel privé par défaut", "defaultTxChNetwork": "Réseau de canal de transmission par défaut", "deleteGroup": "Supprimer le groupe", "denoise": "<PERSON><PERSON><PERSON> de bruit", "denoiseEnable": "<PERSON>r la réduction de bruit", "device": "Appareil", "deviceDetectDecode": "Décodage de détection d'interphone", "deviceRemoteDeadDecode": "Décodage de tuer à distance de l'appareil", "deviceRemoteDestroy": "Détruire à distance de l'appareil", "deviceStun": "Étourdir l'interphone", "deviceWokeUp": "Réveiller l'interphone", "digitalAlarmFormValidate": "La vérification des données d'alarme numérique a échoué", "digitalAlert": "Alerte numérique", "digitalAllowed": "Numérique autorisé", "digitalAnalogChannel": "Canal analogique numérique", "digitalChannel": "Canal numérique", "digitalCompatibleAnalog": "Numérique compatible analogique", "digitalEmergencyAlarm": "Alarme d'urgence numérique", "digitalEmergencyAlertSystem": "Système d'alerte d'urgence numérique", "digitalMode": "Mode numérique", "disable": "Désactiver", "displayName": "Nom d'affichage", "displayNameAndNumber": "Afficher le nom et le numéro", "displayNameNumber": "Nom d'affich<PERSON> ou alias", "displayNumber": "<PERSON><PERSON><PERSON><PERSON> le numéro", "dmrBaseSettings": "Réglages de base DMR", "dualTimeSlot": "Dual time slot", "duplex": "Duplex", "duplexMode": "Mode duplex", "dynamicKey": "Clé dynamique", "early": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "editContact": "Modifier le contact", "editGroup": "EditGroup", "editList": "Modifier la liste", "emergencyAlarmAndCall": "Alarme et appel d'urgence", "emergencyAlarmAndVoice": "Alarme d'urgence et voix", "emergencyAlarmConfirm": "Confirmation d'alarme d'urgence", "emergencyAlarmExitDuration": "Du<PERSON>e de sortie de l'alarme d'urgence d'appui long", "emergencyAlarmIndication": "Indication d'alarme d'urgence", "emergencyAlarmSystem": "Système d'alarme d'urgence", "emergencyAlertDuration": "<PERSON><PERSON><PERSON> de l'alerte d'urgence", "emergencyAlertMessage": "Message d'alerte d'urgence", "emergencyAlertSystem": "Système d'alerte d'urgence numérique/analogique", "emergencyAlertTimes": "Fois d'alertes d'urgence", "emergencyCallAlert": "Alerte d'appel d'urgence", "emergencyCallTimes": "Fois d'appel d'urgence", "emergencySystem": "Système d'urgence", "emittingLed": "LED d'émission", "emphasis": "<PERSON><PERSON><PERSON><PERSON>", "enable": "Activer", "enableAuth": "Activer l'authentification", "encoding": "Encodage", "encryptType": "Type de cryptage", "encryptedList": "Liste cryptée", "encryption": "Cryptage", "encryptionAlgorithm": "Algorithme de cryptage", "encryptionConfig": "Configuration du cryptage", "encryptionKey": "Clé de cryptage", "enhancedXor": "<PERSON><PERSON><PERSON><PERSON> exclusif ou", "entryDelay": "<PERSON><PERSON><PERSON>'entrée(s)", "exitDelay": "<PERSON><PERSON><PERSON>(s)", "femaleVoice1": "Voix féminine 1", "femaleVoice2": "Voix féminine 2", "fieldStrengthInfo": "Informations sur l'intensité du champ", "fileName": "nom de fichier", "firstChoice": "Premier choix", "firstPriorityChannel": "Canal premier prioritaire", "flashlightTime": "Intervalle de temps de la lampe de poche (ms)", "followMainSiteSettings": "Su<PERSON>z les paramètres du site principal", "forbid": "Interdire", "forward": "Avant", "freqOffset": "Décalage de fréquence (MHz)", "freqRepeated": "fréquence de répétition", "frequencyRangeError": "<PERSON><PERSON><PERSON> de gamme de fréquence", "fullList": "La liste est pleine, l'ajout a échoué！", "gLocateFunc": "G(fonction de localisation)", "galileo": "<PERSON><PERSON><PERSON><PERSON>", "generalSettingsFormValidate": "La validation des données des paramètres généraux a échoué", "getRecordList": "Obtenir la liste des enregistrements", "girlVoice": "Voix de fille", "glonass": "GLONASS", "gpioSettings": "GPIO", "gpsAndBeidou": "GPS+BDS", "gpsEnable": "GPS ON", "gpsLocate": "Localisation du GPS", "gpsMode": "Mode GPS", "gpsUpdateTime": "Heure de mise à jour du GPS (sec)", "groupCallTone": "Alerte d'appel de groupe", "groupJoinOrExit": "Rejoindre ou quitter le groupe", "groupManagement": "Gestion de groupe", "highLevel": "haut niveau", "highPerformanceMode": "Mode haute performance", "highPowerSosTime": "Intervalle d'envoi SOS à haute puissance(min)", "hourDiff": "Diff<PERSON><PERSON>ce d'horloge", "idAlreadyExits": "L'identifiant existe déjà, veuillez le saisir à nouveau", "inOutNetworkTone": "Son en réseau et hors réseau", "inbound": "appel", "incomingCall": "A<PERSON> entrant", "independentSettings": "paramètres indépendants", "indicationTones": "Sons d'indication", "indicatorSettings": "Réglages de l'indicateur", "interphoneActivation": "Activation de l'interphone", "interphoneConfig": "Configuration de l'interphone", "interphoneDetection": "Détection d'interphone", "interphoneInfo": "Infos sur l'interphone", "interphoneRemoteKill": "Tuer à distance de l'interphone", "intervalHonkingTime": "Intervalle de temps de siffler(s)", "ipSiteConnection": "Connexion au site IP", "key01": "Bouton 01", "key02": "Bouton 02", "keyList": "Liste de clés", "keyP": "Bouton P", "keyboardHangTime": "Durée(s) de suspension du rétroéclairage du clavier", "keyboardLock": "Verrouillage du clavier", "keys": "Bouton{key}", "lLocateFunc": "L(fonction d'enregistrement)", "langEnv": "Environnement de langue", "lastActiveChannel": "Dernier canal actif", "lastActivityChannel": "Le dernier canal d'activité", "late": "Tard", "lcdHangTime": "Durée(s) de suspension du rétroéclairage LCD", "lgLocateFunc": "LG(localisation + enregistrement)", "localCall": "<PERSON><PERSON><PERSON> lo<PERSON>", "localEmergencyAlert": "Alerte d'urgence locale", "localEmergencyHonk": "Klaxon d'urgence local", "locateFunc": "Fonction de localisation", "locateMode": "Mode de localisation", "locationEnable": "Switch de localisation", "locationSystem": "Système de localisation", "lockKeys": {"backKey": "Retour", "channelKey": "Bouton de canal", "confirmKey": "Confirmer", "dialKey": "Touche de numérotation", "downKey": "Down", "f1key": "F1", "f2key": "F2", "f3key": "F3", "f4key": "F4", "f5key": "F5", "func1Key": "Touche de fonction 1", "func2Key": "Touche de fonction 2", "knobKey": "Bouton", "leftKey": "Touche de direction gauche", "offKey": "<PERSON><PERSON>", "onHookKey": "Touche de couper", "orangeKey": "Touche orange", "p1Key": "Touche P1", "p2Key": "Touche P2", "pttKey": "PTT", "rightKey": "Touche de direction droite", "upKey": "Up", "volumeKey": "Bouton de volume"}, "longPressNumCallTable": "Appuyez longuement sur la fonction du clavier numérique pour appeler", "lookOver": "Regarder", "lowBatteryLed": "LED de batterie faible", "lowLevel": "niveau faible", "lowPowerSosTime": "Intervalle d'envoi SOS à faible puissance (min)", "lowVoltageAlarmTone": "Alarme du faible voltage", "maleVoice1": "Voix masculine 1", "maleVoice2": "Voix masculine 2", "manualQuickCallType": "<PERSON><PERSON> r<PERSON>ci manuel", "manualSiteRoam": "Roaming manuel du site", "mapping": "Mapping", "maxChannelLimit": "{zoneTitle}peut configurer jusqu'à{count}canaux", "maydayRescue": "Réglages SOS/sauvetage", "mediumPowerFunc": "puissance moyenne", "memoryInfo": "informations sur la mémoire", "messageTone": "Son SMS", "messageVibration": "Vibration de SMS", "micActiveTime": "Durée d'activation du microphone(s)", "middlePowerSosTime": "Intervalle d'envoi SOS à puissance moyenne(min)", "minuteDiff": "Différence de minutes", "missedCall": "<PERSON><PERSON> manq<PERSON>", "modelDataError": "<PERSON><PERSON><PERSON> de donn<PERSON> de modèle", "monitorSquelchMode": "Mode de squelch de moniteur", "motionDetectionOnly": "Détection de mouvement uniquement", "multiKeyDecryption": "Décryptage multi-clés", "narrowBand": "Bande étroite", "navType": "Mode de navigation", "networking": "<PERSON><PERSON> en r<PERSON>", "networkingMode": "<PERSON> réseau", "newGroup": "Nouveau groupe", "newGroupContact": "Nouvel appel de groupe", "newSecretKey": "Nouvelle clé secrète", "newSingleContact": "Nouvel appel unique", "noSubtone": "Pas de subtone", "nonPriorityChannelDetection": "Détection de canal non prioritaire", "nonstandardPhase": "Phase non standard", "normal": "Normal", "normallyOpen": "Normalement ouvert", "notHaveValidChannel": "L'appareil n'a pas de canal valide, veuillez d'abord configurer les données du canal", "notPoliteRetry": "Réessayer pas poli", "numberKeyFastDial": "Numérotation rapide", "numericKeyboard": "Clavier numérique {key}", "off": "OFF", "on": "ON", "oneTouchCall": "Appel en une touche", "oneTouchKey": "Appel à une touche{key}", "onlyChannel": "Canal seul", "onlyEmergencyVoice": "Uniquement les appels d'urgence", "onlyGroupCall": "Appel de groupe uniquement", "onlySingleCall": "Appel unique uniquement", "onlyStill": "Seulement encore", "onlyTilt": "Inclinaison uniquement", "onlyVolume": "Volume seul", "onlyWhistle": "<PERSON><PERSON> le siffle<PERSON>", "optionalFeatures": "Caractéristiques optionnelles", "optionalLockKey": "Clé de verrouillage en option", "orangeButton": "Bouton orange", "ownGroup": "groupe d'appartenance", "passwordErrorNumber": "Fois d'erreurs de mot de passe", "paste": "<PERSON><PERSON>", "patrol": "<PERSON><PERSON><PERSON>", "patrolClockIn": "Pointage de la patrouille", "patrolRecord": "Registres de patrouille", "patrolSweepCard": "Balayage de la carte de patrouille", "phoneBook": "Répertoire téléphonique", "phoneContact": "Contact téléphonique", "phoneDialer": "Numérotation téléphonique", "politeRetry": "Réessayer poli", "powerAutoConversion": "Conversion automatique de puissance", "powerLevel": "Niveau de puissance", "powerOnPwdRange": "Plage de saisie du mot de passe de démarrage:0-6", "powerSavingMode": "Mode d'économie d'énergie", "poweredTone": "Son du power on/off", "presetChannel": "Canal prédéfini", "priorityChannelDetection": "Détection de canal prioritaire", "priorityChannelTone": "Son du canal prioritaire", "priorityInterrupt": "Interruption prioritaire", "productionInfo": "informations sur la production", "promptTimeBackwards": "Temps d'invite de backwards(s)", "pttAlone": "Si PTT est indépendant", "pttTimes": "Fois de PTT", "queryByCallDir": "Requ<PERSON>te par direction vocale", "queryById": "Requête par ID cible", "queryByTime": "Requête par heure", "queryCommand": "Recherche de commande", "quickSearch": "Recherche rapide", "randomKey": "Clé aléatoire", "randomKeyEncryption": "Cryptage à clé aléatoire", "randomizedAlgorithm": "Algorithme randomisé", "readPassword": "Programmation pour lire les mots de passe", "realTime": "<PERSON><PERSON> réel", "receiveDuration": "<PERSON><PERSON><PERSON> de ré<PERSON>", "receiveLed": "LED de réception", "receiveSquelchMode": "Mode de recevoir Squelch", "receiverAlarmTone": "Son d'alarme du récepteur", "recordFile": "fichier d'enregistrement", "recordId": "Identifiant de l'enregistrement", "recordList": "liste d'enregistrement", "recordPlayback": "Registres Playback", "recordSetting": "paramètre d'enregistrement", "recordSwitch": "Switch d'enregistrement", "recordTime": "<PERSON><PERSON><PERSON>", "recordingFunc": "Fonction d'enregistrement", "recordingOption": "Options d'enregistrement", "rejectStrangerCall": "<PERSON><PERSON><PERSON> l'appel étranger", "remoteAlertCount": "Nombre d'alertes à distance", "remoteDestructionDecode": "Décodage de destruction à distance", "remoteDetectionDecode": "Décodage de détection à distance", "remoteEraseDecodeEnable": "Détruire à distance l'activation du chiffrement de l'interface aérienne", "remoteKillActivateDecode": "<PERSON><PERSON>/ré<PERSON>r le décodage à distance", "remoteKillActivateDecodeAuth": "Authentification de décodage de tuer/réactiver à distance", "remoteMonitorAuth": "Authentification de monitoring à distance", "remoteMonitorDecode": "Décodage de moniteur à distance", "remoteMonitorDecodeEnable": "Activation du chiffrement de l'interface aérienne de surveillance à distance", "remoteMonitorDuration": "Du<PERSON>e du moniteur à distance", "remotePromptDecode": "Décodage d'invite à distance", "remoteShutEncryptEnable": "Activation du cryptage de l'interface radio d'arrêt/réveil à distance du talkie-walkie", "remoteStunDecodeEnable": "Activation du cryptage de l'interface aérienne d'étourdissement/réveil à distance", "remoteStunWakeupDecode": "Décodage d'étourdissement/réveil à distance", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replyChannel": "Canal de réponse", "rescueScanHandUpTime": "Temps de suspension de scan de sauvetage (ms)", "resend": "<PERSON><PERSON><PERSON>", "residenceTime": "Temps de résidence", "resourceVersion": "Informations sur la version de la ressource", "ringToneSettings": "Réglage de la sonnerie", "roamEnable": "Roam on/off", "roamList": "Liste de roaming", "roamLockSite": "Verrouillage du site de roam", "roamManual": "Roam manuel du site", "roamSettings": "Réglages de roaming", "roaming": "Roaming", "roamingGroup": "Groupe roaming", "rssiDetectCycle": "Cycle de détection RSSI (s)", "runBackward": "Revenir en arrière", "runBackwardAlarm": "Al<PERSON><PERSON>", "rxAnalogCdcss": "Recevoir CDCSS analogique", "rxCdcssType": "Recevoir le type CTCSS/CDCSS", "rxDigitalCdcss": "Recevoir le CDCSS numérique", "rxGroupExceedLimit": "Le nombre maximum de groupes de réception est {max}, qui est actuellement supérieur à {n}", "sLocateFunc": "S(fonction complète)", "sameLaunchPermCondition": "Identique aux conditions d'autorisation de lancement", "samePowerPassword": "Même mot de passe à la mise sous tension", "satellitePositionInfo": "Info sur le localisation satellite", "satellitePositionSwitch": "Switch de localisation satellite", "saveModeOnTimeDelay": "Le mode d'économie d'énergie sur la temporisation", "savePowerDelayTime": "<PERSON><PERSON><PERSON> de l'économie d'énergie", "scan": "Scanner", "scanAndRoamList": "Liste de scanner/roaming", "scanEdit": "Modifier le scanner", "scanGroupFormValidate": "La vérification des données du groupe de scanner a échoué", "scanList": "Liste de scanner", "scanOrRoamList": "Liste de numérisation/itinérance", "scanRoamStatusLed": "LED d'état de scanner/roaming", "scanSamplingTime": "Temps d'échantillonnage du scan (ms)", "scanSettings": "Réglages de scan", "scanningEmissionMode": "Mode d'émission de scanner", "scanningGroup": "Groupe de scanner", "scramble": "Brouillage", "secondPriorityChannel": "Canal deuxième prioritaire", "secretKeyName": "Nom de la clé secrète", "secretKeySwitch": "Switch de clé secrète", "secretKeyValue": "Valeur de la clé secrète", "selectTargetModel": "Veuillez sélectionner des modèles à créer", "setDateTime": "<PERSON><PERSON><PERSON> l'heure et la date", "setRealTime": "<PERSON><PERSON><PERSON><PERSON> le temps réel", "shake": "<PERSON><PERSON><PERSON>", "shakeMode": "Mode secouer", "shiNeng": "Activer", "shieldedHeadphones": "Ecouteurs blindés", "shieldedKeyboardLock": "Verrouillage du clavier du bouclier en mode furtif", "shieldedLedLight": "Lampes d'inhibition en mode furtif", "shieldedMicrophone": "Microphone blindé", "showContactContent": "A<PERSON><PERSON><PERSON> le contenu du contact", "showStrangeNumber": "Afficher les nombres étranges", "signalingPassword": "Mot de passe de signalisation", "signalingPwd": "Clé d'interface aérienne", "signalingSystem": "Système de signalisation", "signalingType": "Type de signalisation", "silenceAndVoice": "Silence et voix", "silenceCarryVoice": "Silence porte la voix", "simplex": "Simplex", "singleCallTone": "Alerte d'appel privé", "singleCallVibration": "Vibration d'appel privé", "singleCallVoiceConfirm": "Confirmer d'appel privé", "singleKeyFuncCall": "Appel de fonction à une touche", "siteInfo": "Informations sur le site", "siteLock": "Verrouillage du site", "siteSearchTimer": "Minuteur de recherche de sites(s)", "sms": "SMS", "smsUdpCompress": "Compression d'en-tête SMS UDP", "softKeyCallType": {"GROUP": "Appel de groupe", "MSG": "SMS", "SINGLE": "<PERSON><PERSON> privé", "TIP": "<PERSON><PERSON><PERSON> d'appel"}, "softKeyFuncDefine": {"AGING_FUNC_TEST_ON_OFF": "Test de fonction de vieillissement on/off", "A_D_SWITCH": "Switch analogique numérique", "ActiveRFIDPRead": "Lire la carte de RFID active", "AllMuteOnOff": "Tout mute on/off", "BACKLIGNT": "Rétroéclairage automatique on/off", "BACKLIGNT_SWITCH": "Rétroéclairage ON/OFF", "BACKTOHOME": "Retour à la page d'accueil", "BASEBAND_FIRMWARE": "Mise à niveau du micrologiciel de la bande de base", "BATTERY_CHANGE": "Alerte sur le niveau de la batterie", "BATTERY_CHARGE": "Alerte sur le niveau de la batterie", "BLUETOOTH_SEARCH_AUTO_CONNECT": "Recherche Bluetooth et connexion automatique", "Back2BackForwardingMode": "Mode de transfert dos à dos", "BackToBackEnable": "Interrupteur dos à dos", "BluetoothEnable": "Interrupteur bluetooth", "CALL_RECORDS": "Journaux d'appels", "CALL_TONE": "<PERSON><PERSON><PERSON> d'appel", "CH_DOWN": "Canal down", "CH_LOCK_SW": "Verrou de canal", "CH_PRESET1": "Canal par défaut 1", "CH_PRESET2": "Canal par défaut 2", "CH_PRESET3": "Canal par défaut 3", "CH_PRESET4": "Canal par défaut 4", "CH_UP": "Canal up", "COMMON_CONTACT_LIST": "Liste des contacts fréquemment utilisés", "CONTACTS": "Contacts", "CONTAS_LIST_MENU": "Listes des contacts", "CONTAS_MENU": "Contacts", "DATE_TIME": "Alerte de la date actuelle", "DEL_INVALID_CHL": "Supprimer le canal inutile", "DEL_USELESS_CH": "Suppression de canal inutile", "DEV_ACTIVE": "Activer l'appareil", "DEV_DETECT": "Détection d'interphone", "DEV_DIE": "Tuer à distance de l'appareil", "DEV_RUIN": "détruire à distance de l'interphone", "DEV_STUN": "Étourdir l'interphone", "DEV_WAKEUP": "réveiller l'interphone", "DISCONNECT": "Interruption prioritaire", "DTA": "Conversion numérique/analogique", "DTMF_MENU": "Clavier DTMF", "ENCRYPTION": "Cryptage", "ERRNUMBER_RX": "Test BER-RX", "ERRNUMBER_TX": "Test BER-TX", "EncryptionSwitch": "Commutateur de cryptage", "GLARE_FLASHLIGHT": "Torche lumineuse on/off", "GPS_ON_OFF": "GPS ON/OFF", "GPS_POSITION_SWITCH": "Commutateur de positionnement satellite", "GPS_SWITCH": "Téléchargement des données de positionnement par satellite", "HANG_UP": "Couper", "HIDE_SWITCH": "Mode furtif on/off", "INT_PRI": "Interruption prioritaire", "KEYBOARD_LOCK": "Verrouillage du clavier", "KEYLOCK": "Verrouillage du clavier", "LONGMONI": "Monitoring permanente", "MAIN_MENU": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu principal", "MANUAL_DIAL": "Composer <PERSON><PERSON><PERSON>", "MANUAL_ROAMING_LOCK": "Verrouillage de roaming manuel", "MENU_INTERFACE": "Interface des menus", "MONI": "<PERSON><PERSON><PERSON>", "MONITOR_ON_OFF": "<PERSON><PERSON><PERSON>", "MSG_MENU": "SMS", "NETMODE_CHG": "<PERSON><PERSON><PERSON>/hors ligne", "NONE": "Non défini", "NetworkingOnOff": "Mise en réseau on/off", "OFF_NETWORK_ON_OFF": "<PERSON><PERSON> r<PERSON> ON/OFF", "PHONE_BOOK": "Répertoire téléphonique", "PHONE_LIST_MENU": "Liste des contacts téléphoniques", "PRIORITY_INTERRUPT": "Interruption prioritaire", "PWRMODE_CHG": "Puissance élevée/faible", "PWRMODE_CHG_BP660": "commutation de puissance", "PassiveRFIDRead": "Lire la carte de RFID passive", "RECORDING_ON_OFF": "Enregistrement on/off", "RECORD_SWITCH": "Enregistrement on/off", "RESCUE_SCAN_SWITCH": "Mode de balayage de secours on/off", "RFIDQuery": "Requête de backlog RFID", "RFID_REGISTER": "RFID Enregistrement", "RMT_MONITOR": "Moniteur à distance", "ROAM_MANUAL": "Site roaming manuel", "ROAM_SWITCH": "Roaming ON/OFF", "SCALL_DIAL": "Composition manuelle d'un appel privé", "SCALL_DIAL_MENU": "Interface de composition manuelle", "SCAN": "Scan activé/désactivé", "SCAN_SWITCH": "Scanner ON/OFF", "SITE_LOCK_SWITCH": "Switch de verrouillage du site", "SK_BT_SWITCH": "Bluetooth activé/désactivé", "SK_CH_LOCK": "Verrouillage de chaîne activé/désactivé", "SK_DISABLED_ALL_LED": "Désactivez toutes les LED", "SK_DNS_SWITCH": "Réduction du bruit activée/désactivée", "SK_ENCRYPTION_SWITCH": "Cryptage activé/désactivé", "SK_VIBRATION_SWITCH": "Vibration activée/désactivée", "SK_BT_SEARCH_AUTO_CONNECT": "Recherche Bluetooth et connexion automatique", "SMS": "SMS", "SPDCALL1": "Appel de fonction à touche unique 1", "SPDCALL2": "Appel de fonction à touche unique 2", "SPDCALL3": "Appel de fonction à touche unique 3", "SPDCALL4": "Appel de fonction à touche unique 4", "SPDCALL5": "Appel de fonction à touche unique 5", "SPDCALL6": "Appel de fonction à touche unique 6", "SQUELCH_LEVEL_ADJUST": "Réglage du niveau de squelch", "SQUELCH_OPEN": "Squelch ON", "STANDBY_INTERFACE": "<PERSON><PERSON> de <PERSON>", "STEALTH_MODE_ON_OFF": "Mode furtif on/off", "SystemStatusQuery": "Requête sur l'état du système", "TELEMETRY_BUTTON1": "Bouton de télémétrie 1", "TELEMETRY_BUTTON2": "Bouton de télémétrie 2", "TELEMETRY_BUTTON3": "Bouton de télémétrie 3", "TONE_MUTE": "Commutateur pour tous les sons", "TONE_MUTE_SWITCH": "Son ON/OFF", "TOP_CONTAS_LIST_MENU": "Liste des contacts fréquemment utilisés", "TRANSIENT_MONITOR": "Monitoring transitoire", "TRANSIENT_SQUELCH_OPEN": "squelch transitoire ON", "TRANSMIT_POWER_HIGH_LOW": "Puissance de transmission élevée/faible", "TX_TEST": "Teste TX", "VIBRATION_SETTINGS": "Réglages de vibration", "VOLUME_UP": "Volume up", "VOLUMN_DOWN": "Volume down", "VOX": "Commutateur de commande vocale", "VOX_SWITCH": "VOIX ON/OFF", "WARNING_OFF": "Mode d'urgence désactivé", "WARNING_ON": "Mode d'urgence activé", "WORK_ALONE_SWITCH": "Switch de travailleur isolé", "WORK_DOWN_SWITCH": "Switch à l'envers", "ZONE_DOWN": "Zone down", "ZONE_SWITCH": "Switch de zone", "ZONE_UP": "Zone up"}, "sosInfo": "SOS info", "sosRescue": "SOS/Sauvetage", "sosRescueCfg": "Configuration SOS/Sauvetage", "sosRescueChannel": "Canal SOS/Sauvetage", "specifiedChannel": "Le canal spécifié", "specifyTransmitChannel": "Spécifier le canal de transmission", "specifyTxTimeSlot": "Spécifier timeslot de transmission", "speechRate": "Taux de parole", "standardPhase": "Phase standard", "stealthMode": "Mode furtif", "stealthModeBacklight": "Rétroéclairage en mode furtif", "stealthModeHeadsetMute": "Sourdine du casque en mode furtif", "stealthModeSettings": "Réglages du mode furtif", "stealthModeVibrationShield": "Bouclier de vibration en mode furtif", "strengthen": "Renforcer", "subtoneNotDetected": "Le subtone n'est pas détecté", "subtoneScanningMode": "Mode de scanner de subtone", "svtChannelList": "Liste d'itinérance intersites", "switchChannelSquelchMode": "Switch de mode de Squelch de canal", "switchHighPowerThreshold": "Passer au seuil de puissance élevé(dBm)", "switchLowPowerThreshold": "Passer au seuil de faible puissance(dBm)", "switchMediumPowerThreshold": "Passer au seuil de puissance moyenne(dBm)", "syncTimezone": "Synchroniser le fuseau horaire", "synchronisedTime": "<PERSON>ure synchronisée", "system": "système", "systemFunction": "Fonction du système", "systemInfo": "Système info", "targetId": "ID cible", "terminalType": "Type de terminal", "theAlarm": "Alarme", "theSelected": "Sélectionné", "tiltOrMotionDetection": "Détection d'inclinaison ou de mouvement", "tiltOrStill": "Inclinaison ou arrêt", "timeSlotSelection": "Sélection de la timeslot", "timeZoneId": "ID de fuseau horaire", "transmitAlertAudioDuration": "Durée de transmettre d'alerte audio (s)", "transmitAlertAudioTimes": "Fois de transmettre d'alerte audio", "triggerControl": "Contrôle de déclenchement", "triggerInclination": "Déclencher inclination(degré)", "triggerMode": "Mode de déclenchement", "ttsSpeed": "Vitesse de parole", "ttsTune": "Tonalité", "ttsVolume": "Volume", "txAnalogCdcss": "CDCSS analogique tx", "txCdcssType": "Type CDCSS tx", "txDigitalCdcss": "CDCSS numérique tx", "uiSettings": "Réglages de UI", "uiVersion": "UI version", "unConfirmSingleCall": "SMS d'appel unique non confirmé", "underAlarmRemoteMonitorDecode": "Décodage de moniteur sous alarme d'urgence", "unlimited": "illimité", "unlimited2": "illimité", "upsideDown": "À l'envers", "upsideDownSwitch": "Switch à l'envers", "urgentRemoteMonitorDecode": "Décodage d'urgence du moniteur à distance", "vibration": "Vibration", "vibrationSettings": "Réglages de vibration", "viewRecordingFiles": "Afficher les fichiers d'enregistrement", "virtualCluster": "Trunking virtuel", "virtualClusterFormValidate": "La validation des données du trunking virtuel a échoué", "voiceBroadcast": "Diffusion vocale", "voiceBroadcastSettings": "Réglage de diffusion vocale", "voiceCallEmbedAlias": "Alias d'intégration d'appel vocal", "voiceCallEmbedding": "Appel vocal intégrant les informations de localisation", "voiceDuplex": "Voix Duplex", "voiceEndTone": "Son du fin du voix", "voiceLaunch": "Lancement vocal", "voicePriority": "Priorité à la voix", "voicePrompt": "Invite vocale", "weightingMark": "<PERSON><PERSON>", "workAlone": "Travailleur isolé", "workAloneAlarm": "Alarme de travailler isolé", "workAloneReminderTime": "Temps de rappel de travailleur isolé(s)", "workResOptAlone": "Opération de réponse de travailleur isolé", "workResTimeAlone": "Temps de réponse de travailleur isolé(m)", "writePassword": "Programmation pour écrire des mots de passe", "xor": "<PERSON>clus<PERSON> ou", "zone": "Zone", "zoneConfig": "Nouvelle liste de zones", "zones": {"leaf": "Zone de niveau 3", "parent": "Zone de niveau 2", "root": "Zone de niveau 1"}, "imagePreview": "Aperçu de l'image"}}