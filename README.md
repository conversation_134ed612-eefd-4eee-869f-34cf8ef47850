## bf8100 DMR 应急指挥调度系统

> 2025-02-13，完成 vue-cli 迁移到 vite, vue2+element-ui
> 2025-04-03，完成 vue3+element-plus升级

#### 项目介绍

> BF-8000 系统升级版，取消原有的控制器联网调度功能，改为以组 DMRID 互联通讯，以发挥 dmr 集群优势。
>
> ##### 主要功能
>
> - 对讲机、用户等数据管理
> - 录音记录、定位等数据查询
> - 定位轨迹回放
> - 下发各类功能指令
> - 与对讲机进行联网通话
> - 动态组功能（任务组、临时组）
> - 物联终端监控，心跳、打卡、报警
> - ......

#### 前端与后端的交互流程文档

> [Process.md](Process.md)

#### 项目结构

```
project
|-- build
| |-- webclient-x.x.x      // 项目编译输出目录
|-- doc          // 一些辅助文档目录
|-- public         // Vue项目公共资源目录，会直接拷贝到编译输出目录下
| |-- favicon.ico
| |-- index.html
|-- src          // 项目源码目录
| |-- components       // Vue组件
| | |-- command       // 系统中心下发命令的组件
| |   |-- common       // 自定义封装的全局公共组件
| | |-- dataDialog      // 系统数据菜单下各类数据编辑组件
| | |-- historyDialog     // 系统查询菜单下各类记录查询组件
| | |-- interphoneWf     // 对讲机写频功能组件目录
| | | |-- common      // 写频功能公共组件
| | | |-- views      // 各机型写频入口组件
| | | |-- interphoneWf.vue   // 写频功能入口组件
| | |-- layout       // 项目布局组件
| | |-- repeaterWf      // 中继在线写频功能目录
| | | |-- common      // 中继写频公共组件
| | | |-- modelView     // 中继机型写频入口组件
| |   |   |-- repeaterWriteFrequency.vue // 中继写频入口组件
| | |-- secondary      // 系统设置、版本等辅助组件
| |-- css
| | |-- iconfont      // 字体图标库
| | |-- images       // datatables css样式使用的图片
| |-- images        // 项目中使用的图片库
| |-- modules        // 封装的自定义模块
| | |-- dataManager      // 全局数据管理模块
| | |-- geojson       // 地图的geojson数据文件模块，用于加载.geojson文件
| | |-- i18n       // 封装18n库，加载自定义语言包，执行同步系统网站标题等功能
| | |-- protocol      // proto协议目录
| | | |-- protos      // 保存项目中与服务器交互的协议，协议由后端维护
| | | |-- bf_proto.json    // 使用protobufjs编译的json格式协议
| | | |-- db.pb.cmd.js    // 与服务器交互的指令号模块，由后端根据协议自动生成
| | | |-- index.js     // 协议交互入口模块
| |-- static        // 项目本地静态资源目录，自动拷贝到编译目录下
| | |-- assets       // 项目使用的音频、文档、需要下载的软件
| | |-- config       // 系统前端配置
| | |-- geojson       // 地图本地的.geojson文件
| | |-- langs       // 自定义语言包
| | |-- localMapbox      // 地图本地化数据
| |-- utils        // 项目各功能工具模块
| |-- worker        // Web Worker模块目录
| |-- writingFrequency
| | |-- interphone      // 对讲机写频功能数据编码、解码模块，借鉴google protobuf
| | |-- repeater      // 中继写频功能模块
| | |-- modelInfo.js     // 各机型名称与识别码模块
| |-- App.vue        // 项目Vue根组件
| |-- main.js        // 项目入口模块
| |-- evnConfig.js      // 项目环境变量配置
|-- .gitlab-ci.yml       // gitlab-ci自动化部署配置
|-- clearDeployCache.cjs      // nodejs脚本，自动化部署后执行清除服务器过期的文件
|-- generateModelInfo.cjs     // nodejs脚本，自动生成机型名称与识别码模块
|-- pbjs.sh         // 编译.proto协议，输出json格式协议
|-- README.md
|-- package.json
|-- yarn.lock
```

#### 开发环境

> - node
> - vue-cli 3
> - webpack
> - element
> - websocket
> - protobufjs

#### 前端技术栈

> - [vue](https://github.com/vuejs/vue#readme)
> - [element-plus](https://element-plus.org/zh-CN)
> - [vue-i18n](https://github.com/kazupon/vue-i18n#readme)
> - [protobufjs](https://github.com/protobufjs/protobuf.js#readme)
> - [websocket-nats](https://github.com/isobit/websocket-nats#readme)
> - [datatables](https://datatables.net)
> - [jquery.fancytree](https://github.com/mar10/fancytree#readme)
> - [xlsx](https://github.com/SheetJS/js-xlsx#readme)
> - [mapbox-gl](https://github.com/mapbox/mapbox-gl-js#readme)
> - [qwebchannel](https://www.npmjs.com/package/qwebchannel)

#### 运行项目

> - git clone <http://git.cchdx.com:5280/bfdx/bf8000-web.git> or <https://gitee.com/bfdx_dev/webpack-bf8000.git>
>
> - yarn
> - yarn serve or yarn build

#### 项目部署

> 采用 gitlab-ci 自动化部署， 编辑根目录下 .gitlab-ci.yml，添加或编辑配置项规则

###### GitLab CI 变量说明

- `Enable_License`: `boolean`类型，是否开启授权校验功能，添加到`.env.local`**vue 环境变量**，默认为`true`

#### 项目添加 tag 和 release 发布

- 使用 [semantic-release](https://github.com/semantic-release/semantic-release) 自动化 tag 和 release 发布过程

- git 提交消息格式: (type([optional scope]): description)
  - **breaking**: A big feature/refactor which includes a breaking change
  - **feat**: A new feature
  - **fix**: A bug fix
  - **refactor**: A code change that neither fixes a bug nor adds a feature
  - **config**: Updating configurations of linter, ts, webpack, babel, etc.
  - **test**: Adding missing tests or correcting existing tests
  - **docs**: changing the readme or adding additional documentation
  - **no-release**: a specific type which you can use in specific cases when no release is needed for your change

> 常用的是**fix**, **feat**

###### semantic-release 项目配置

- **settings/access_tokens**: 添加 token，Scopes 权限选择**api**和**write_repository**

- **settings/CI/CD**：**Variables**配置页下添加**GITLAB_TOKEN**或**GL_TOKEN**变量，参数值为上一步骤的 token

#### 多国语言翻译配置，按配置设置可以动态添加语言包

> 默认支持中文、英文、法文。其他语言需要自动添加翻译包，并修改客户根目录下的`config.js`配置文件的`languages`选项
>
> 支持的语言名称须与`element-ui`支持的语言名称一致，详细配置参考：<https://element.eleme.cn/#/zh-CN/component/i18n>

- 配置示例

```js
// 系统语言支持
const languages = {
  // 支持的语言，当前支持"中文(zh-CN)","英文(en)","法文(fr)"
  supports: ['zh-cn', 'en', 'fr'],
  // 切换语言菜单中显示的文本，顺序与supports一致
  displayLabels: ['简体中文', 'English', 'Français'],
  // 默认显示的语言，默认跟随浏览器语言
  default: '',
}
```

#### 登录请求应答，返回了服务器版本，需要验证版本号

- 如果服务器的版本号小于指定的版本号，则提示用户更新版本
- 服务器指定的最小版本号，在`package.json`中指定`minServerVersion`属性

### 授权模块注意事项

- 授权模块信息在 `proto/lic.proto` 文件中定义。
- `lic.proto`在 `bf8000-server` 和 `lic-manager` 项目也存在，需要同时修改这两个项目中的同名文件。
