import globals from "globals"
import pluginJs from "@eslint/js"
import pluginVue from "eslint-plugin-vue"

/** @type {import('eslint').Linter.Config[]} */
export default [
  {
    languageOptions: {
      globals: globals.browser,
    },
  },
  {
    ignores: [
      ".gitignore",
      "build/",
      "coverage/",
      "src/css/iconfont/*",
      "src/writingFrequency/modelInfo.js",
      "src/components/common/tableTree/plugins/*",
      "src/modules/protocol/gen/*",
    ],
  },
  pluginJs.configs.recommended,
  ...pluginVue.configs["flat/recommended"],
  {
    files: ["src/**/*.{js,mjs,cjs,vue}"],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      globals: {
        bfglob: 'writable', // 'readonly' 或 'writable'，取决于是否需要在代码中修改 bfglob
        window: 'writable', // 通常也需要声明 window
        document: 'writable', // 以及 document
        $: 'writable',
        jQuery: 'writable',
        CLIENT_BUILD_TIME: 'readable',
        CLIENT_GIT_TAG: 'readable',

        // 忽略element-plus自动导入的ElMessage等模块引起的'node-undef'错误
        ElMessage: 'readonly',
        ElMessageBox: 'readonly',
        ElNotification: 'readonly',
        ElLoading: 'readonly',
      },
    },
    rules: {
      'no-unused-vars': 'off',
      'no-case-declarations': 'off',

      'vue/no-v-text-v-html-on-component': 'off',
      'vue/no-v-html': 'off',
      'vue/require-default-prop': 'off',
      'vue/order-in-components': 'off',
      'vue/attribute-hyphenation': 'off',
      'vue/require-prop-type-constructor': 'off',
      'vue/one-component-per-file': 'off',
      'vue/attributes-order': 'off',
    },
  },
]
