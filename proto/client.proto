syntax = "proto3";
package bfdx_proto;

import "db.proto";
import "bf_radio.proto";
import "rpc.proto";
import "db_rpc.proto";
import "db.pb.list.proto";
import "lic.proto";

//客户端获取服务器时间
//跟数据库查询一样发往db.sys_id
//rpc_cmd.cmd=1
//回应
//rpc_cmd.opt=utc时间字符串(yyyy-mm-dd HH:MM:SS)

//客户端登录时发送的数据
//rpc_cmd.cmd=11
message client_login_request {
  string sys_id = 1;
  string user_name = 2;
  //取当前时间(utc)格式为: yyyy-mm-dd hh:mm:ss
  string time_str = 3;
  //密码hash,计算方法为base64(sha256(time_str+base64(sha256(user_name+user_pass))))
  string pass_hash = 4;
  //登录系统方法,0:使用密码登录,1:使用sid登录
  int32 login_method = 5;
  //session id
  string sid = 6;

  //登录途径,1:web,2:pc 3:mobile
  int32 login_way = 7;
}

//后台回复客户端登录
message client_login_response {
  //100:成功 0:不成功(未知原因) 1:无此用户 2:密码检验不通过 3:无此sid
  int32 response_code = 1;
  //成功时为新session id
  string sid = 2;
  //user org uuid
  string org_id = 3;
  //user rid
  string user_rid = 4;
  //登录成功的话会有相应的授权信息
  //8100的模块为：mod-rfid:巡查, mod-phone-gateway:电话网关 mod-record:录音 mod-dispatch:联网调度 mod-traditional-dmr:传统dmr手台接入
  lic_response lic = 5;
  //server version
  string server_version = 6;
}

//客户端请求权限内的群组数据
//rpc_cmd.cmd=13
message client_privilege_request {
  //登录成功时后台返回的sid
  string sid = 1;
  //客户自己的rid
  string user_rid = 2;
  //时间格式的时区,默认为0,即utc时间
}
//后台回复客户端请求群组数据
message client_privilege_response {
  //1:无此sid,100:成功
  int32 response_code = 1;
  repeated db_org user_orgs = 2;
}

//客户端请求自己有权限的org_id列表
//rpc_cmd.cmd=14
//body=client_privilege_request

//回应
//rpc_cmd.body=逗号分隔的org_id列表

//请求自己有权限的对讲机设备数据
//rpc_cmd.cmd=15
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_device_list

//请求自己有权限的用户数据
//rpc_cmd.cmd=16
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_user_list

//请求自己有权限的rfid巡查点数据
//rpc_cmd.cmd=17
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_line_point_list

//请求职位数据
//rpc_cmd.cmd=18
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_user_title_list

//请求地图标注点数据
//rpc_cmd.cmd=19
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_map_point_list

//请求巡查线路数据
//rpc_cmd.cmd=20
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_line_master_list

//请求巡查规则数据
//rpc_cmd.cmd=21
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_rfid_rule_master_list

//请求控制器数据
//rpc_cmd.cmd=22
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_controller_list

//请求当前的对讲机状态数据
//rpc_cmd.cmd=23
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_device_last_info_list

//请求当前的控制器状态数据
//rpc_cmd.cmd=24
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_controller_last_info_list

//请求当前的巡查点状态数据
//rpc_cmd.cmd=25
//body=client_privilege_request
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_line_point_latest_info_list

message search_filter {
  string from_time = 1;  //开始时间(utc)
  string to_time = 2;    //结束时间
  string device_rid = 3; //设备rid
  string person_rid = 4; //用户rid
  int32 timezone = 5; //返回的日期的时区(单位为分钟,北京为480)

  string org_id = 6; //只查询此群组的数据

  string opt_point_id = 7; //rfid历史查询里面要用到

  string opt_group_dmr_id = 8; //查询命令发送历史时可能用到

  string opt_rule_rid = 9;  //查询巡查规则统计状况时用到
  string opt_line_rid = 10; //查询巡查规则统计状况时用到

  int32 opt_sms_db_type = 11; //查询短信历史的哪个表，0：已发送，1：未发送
}

//查询开关机历史数据
//rpc_cmd.cmd=45
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_device_power_onoff_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询GPS历史数据
//rpc_cmd.cmd=46
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_gps_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询报警历史数据
//rpc_cmd.cmd=47
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_alarm_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询rfid巡查历史数据
//rpc_cmd.cmd=48
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_rfid_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询用户上下班读卡历史数据
//rpc_cmd.cmd=49
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_user_check_in_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询录音历史数据
//rpc_cmd.cmd=50
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_sound_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询命令发送历史数据
//rpc_cmd.cmd=51
//search_filter.device_rid填写的是dmr_id,不是rid
//opt_group_dmr_id 为发送命令为群组时的群组dmr_id
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_sent_cmd_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

message rule_check_item {
  int32 no = 1;
  string point_rid = 2;
  string check_time = 3;
  string device_rid = 4;
  string check_person_rid = 5;
  int32 check_result = 6;      //0:ok  1:早到, 2:晚到 3:漏查
  string check_time_early = 7; //最早检查时间
  string check_time_end = 8;   //最晚检查时间

  int32 process_code = 14; //给服务器用,客户端不用理会
}
message rule_statistic_result {
  int32 no = 1; //序号
  string rule_rid = 2;
  string line_rid = 3;
  int32 point_total = 4; //总共要检查的点
  int32 point_ok = 5;
  int32 point_bad = 6;
  string time_start = 7;
  string time_end = 8;
  repeated rule_check_item check_items = 9; //详细统计数据
}
//查询巡查规则统计状况
//rpc_cmd.cmd=52
//body=search_filter
//search_filter.opt_rule_rid/opt_line_rid
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=rule_statistic_result
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询通话调度/信道切换历史
//rpc_cmd.cmd=53
//body=search_filter
//search_filter.device_rid填写的是dmr_id,不是rid
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_call_dispatch_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询控制器上下线历史
//rpc_cmd.cmd=54
//body=search_filter
//search_filter.device_rid填写的是控制器dmr_id,不是rid
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_controller_online_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询基站互联历史
//rpc_cmd.cmd=55
//body=search_filter
//rpc_cmd.opt=数据返回的主题,客户端需要先订阅此subject来获得数据库返回的数据
//回应
//rpc_cmd.ref_info="+OK"时
//body=db_conf_dispatch_history_list
//rpc_cmd.opt里面会包含有查询数据说明,逗号分隔, 数据包序号,结束标志(end)

//查询群组有多少个子群组
//rpc_cmd.cmd=79
//rpc_cmd.opt=群组的rid
//回应
//rpc_cmd.opt=子群组的个数

//rpc_cmd.cmd=80 控制器交互指令
//rpc_cmd.body=cc01

//基站互联操作
//rpc_cmd.cmd=81
//rpc_cmd.body=conference_dispatch

//同时中心将基站互联信息从 conf.[sys_id] 主题下分发，命令为81
//rpc_cmd.cmd=81
//rpc_cmd.body=conference_dispatch

//查询服务器地址信息
//rpc_cmd.cmd=82
//rpc_cmd.body=controller_server_info

//收到还没录入数据库的对讲机上来的数据
//rpc_cmd.cmd=1100
//rpc_cmd.body= not_register_device_cmd

//收到重复的命令
//rpc_cmd.command=1101
//rpc_cmd.body= cmd_repeate_received

//收到还没录入数据库的控制器ID
//在主题 sys_id.new_controller下广播
//rpc_cmd.cmd=1102
//rpc_cmd.body=new_controller

//设备dmrid已经改变，清除相应缓存
//rpc_cmd.command=1200
//rpc_cmd.sys_id=sys_id
//rpc_cmd.opt=dmrid

//手台上来的bcxx命令
//rpc.cmd=2000+xx
//如bc00=2000+0
//bc01=2000+1
//bc15=2000+15

//手台上来的dcxx命令
//rpc.cmd=2200+xx
//dc00=2200+0
//dc01=2200+1

//手台上来的ecxx命令
//rpc_cmd.cmd=2400+xx

//客户端对设备下发指令cbxx
//subject=radio.[sys_id]
//如对11号系统下发命令,则subject=radio.11
//rpc_cmd.cmd=3000+xx
//cb01=3000+1
//cb12=3000+12

//基站互联调度
message conference_dispatch {
  //用户所属的org_uuid
  string user_org_id = 11;
  //发起操作的用户uuid
  string user_uuid = 1;
  //操作码
  //0：取消互联调度
  //1：启用互联调度
  //2：增加控制器到会议室
  //3：从会议室删除控制器
  int32 action = 2;
  //会议室号码，启用调度时为空
  string room_no = 3;
  //调度类型
  //1:基站互联，全部通话互通
  //0：仅仅信道互联，通话目标使用发话对讲机的目标id，类似信道补点
  int32 room_type = 4;
  //控制器dmrid列表
  repeated string controllers = 5;
}
//基站调度回应
message conference_dispatch_response {
  //会议室号码
  string conf_no = 1;
  //此次调度的uuid，对应db_conf_dispatch_history中的行 id
  string dispatch_uuid = 2;
  //回应码，0：成功，1：无法分配到会议室
  int32 response = 3;
}

message controller_server_info {
  string logic_host_addr = 1;
  int32 logic_host_port = 2;
  string voip_host_addr = 3;
  int32 voip_host_port = 4;

  //100:get setting ok
  int32 code = 5;
}

// 根据rid list 请求相关数据
message rid_filter {
  //rids
  repeated string rids = 1;
  //这个根据cmd需要填写 1 返回 -1 不返回 是否返回父级的org
  int32 request_code = 2;

  //结果中需要的列,如果为空则返回所有的列
  repeated string result_cols = 3;
}

//终端/群组是否可加入临时组
message member_can_add {
  int32 user_priority = 1;
  db_dynamic_group_detail member = 2;
}

// 对一个临时组 增加/删除 一批设备 群组
message modify_device_list {
  //新建临时组
  db_org dynamic_group = 1;
  int32 user_priority = 2;
  //待入组的设备
  repeated db_dynamic_group_detail devices = 3;
  //待入组的群组
  repeated db_dynamic_group_detail groups = 4;
}

message modify_member_list {
  //新建临时组
  db_org dynamic_group = 1;
  //需要增加的成员
  db_dynamic_group_detail_list add_devices = 3;
  db_dynamic_group_detail_list add_groups = 4;
  //需要删除的成员
  db_dynamic_group_detail_list del_devices = 5;
  db_dynamic_group_detail_list del_groups = 6;
}

// 用户可见的临时组
message temp_group_list {
  //temp group
  repeated db_org dynamic_group = 1;
  //members
  repeated db_dynamic_group_detail members = 2;
}

//查询成员是否可加入临时组
//rpc_cmd.command=1310
//db_dynamic_group_detail.device_dmrid: 加入设备的dmrid
//db_dynamic_group_detail.group_dmrid: 加入群组的dmrid
//db_dynamic_group_detail.dynamic_group_type: 动态组类型
//db_dynamic_group_detail.is_device_group: 成员是设备还是组呼
//db_dynamic_group_detail.creator_priority: 用户优先级
//rpc_cmd.body=member_can_add
//回应
//rpc_cmd.ResInfo +OK 或 抢占临时组的组名 或 Err:错误信息

//新建一个临时组/任务组
//rpc_cmd.command=1311
//db_dynamic_group_detail.device_dmrid: 加入设备的dmrid
//db_dynamic_group_detail.device_rid: 加入设备的rid
//db_dynamic_group_detail.group_dmrid: 加入群组的dmrid
//db_dynamic_group_detail.group_rid: 加入群组的rid
//db_dynamic_group_detail.is_device_group: 成员是设备还是组呼 只用1 2
//db_dynamic_group_detail.dynamic_group_type: 动态组类型
//db_dynamic_group_detail.member_org_id: 成员所属单位
//db_dynamic_group_detail.creator: 创建者RID
//db_dynamic_group_detail.creator_priority: 用户优先级
//rpc_cmd.body=modify_device_list
//回应
//设备/群组加入结果
//rpc_cmd.body=modify_device_list

//删除整个临时组/任务组
//opt="force"标识强制删除整个组
//rpc_cmd.command=1312
//rpc_cmd.body=db_org

//查询可查看的临时组
//rpc_cmd.command=1315
//rpc_cmd.body=db_org_list
//回应
//rpc_cmd.body=temp_group_list

//用户修改自己优先级 通知服务器
//rpc_cmd.command=1316
//回应
//rpc_cmd.res_info="+OK"

//通知客户端动态组成员变更
//rpc_cmd.command=1317
//group.sysid.dynamicGroupDmrid
//其中temp_group_list.DynamicGroup为空
//opt: 1.Insert 2.Update 3.Delete(1 2 3是对成员操作) 4.DeleteGroup（删除整个组）
//rpc_cmd.body=temp_group_list

//通知客户端创建动态组
//rpc_cmd.command=1318
//group.sysid
//其中temp_group_list.DynamicGroup为空
//opt: CreateGroup(创建动态组)
//rpc_cmd.body=temp_group_list

//PC Device 应答加入API (任务组)
//客户端只需要调用API，待服务器更新状态 发布1317命令 才变动客户端状态
//rpc_cmd.command=1319
//rpc_cmd.body=db_dynamic_group_detail
//回应
//rpc_cmd.res_info="+OK"

//PC Device 应答退出API (任务组)
//客户端只需要调用API，待服务器更新状态 发布1317命令 才变动客户端状态
//rpc_cmd.command=1320
//rpc_cmd.body=db_dynamic_group_detail
//回应
//rpc_cmd.res_info="+OK"

//更新/删除动态组成员
//rpc_cmd.command=1321
//rpc_cmd.body=modify_member_list
//回应
//rpc_cmd.res_info="+OK"
//改动结果
//rpc_cmd.body=modify_member_list

//查询动态组当前状态
//rpc_cmd.command=1322
//rpc_cmd.body=db_org
//回应
//rpc_cmd.res_info="+OK"
//改动结果
//rpc_cmd.body=temp_group_list

//通知客户端整个动态组变更
//rpc_cmd.command=1331
//group.sysid.dynamicGroupDmrid
//动态组所有成员
//rpc_cmd.body=temp_group_list

//请求后端生成授权请求
//rpc_cmd.cmd=1430
//rpc_cmd.body=lic_request_file
//只填写proj_name和note
//回应
//rpc_cmd.body=lic_request_file

//查询当前的授权情况
//rpc_cmd.cmd=1431
//回应
//rpc_cmd.body=lic_response
//如果proj_name为空表明没有授权

//导入授权文件
//rpc_cmd.cmd=1432
//rpc_cmd.body=lic_response_file
//回应
//导入成功 rpc_cmd.res_info="+OK"

//dynamic group info
message DynamicGroupInfo {
  //group dmrid
  string group_dmrid = 1;
  //动态组名称
  string group_name = 2;
  //动态组类型 100:临时组 101:任务组 102:自动删除的临时组
  fixed32 group_type = 3;
  //需要添加到动态组的组呼 dmrid
  repeated string group_dmrids = 4;
  //需要添加到动态组的设备 dmrid
  repeated string device_dmrids = 5;
}

// broadcast 使用
message phone_short_no_info {
  //名称
  string short_no = 1;
  string dmrid = 2;
}

//poc setting
message poc_setting {
  //poc default tx group dmrid(HexDmrid)
  string tx_group_dmrid = 1;
  //poc default rx group dmrids(HexDmrid)
  repeated string rx_group_dmrids = 2;
  //poc password
  //password=base64(sha256(time_str+base64(sha256(user_name+user_pass))))
  string password = 3;
  // individual poc contact(HexDmrid)
  repeated string poc_individual_contacts = 4;
  // group poc contact(HexDmrid)
  repeated string poc_group_contacts = 5;
}
