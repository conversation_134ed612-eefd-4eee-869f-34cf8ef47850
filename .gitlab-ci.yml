stages:
  - lint
  - release
  - build
  - deploy

# 设置相关变量
variables:
  BuildDir: ./build
  ServerDir: /bfdx_server
  DeployDir: $ServerDir/webclient
  PkgpushGit: https://pkgpush:$<EMAIL>/bfdx/bf8100pkg.git
  PkgBranch: bf8100_web
  Bf8100pkgFiles: https://git.kicad99.com/api/v4/projects/118/repository/files

cache:
  key: $CI_PROJECT_NAME.$CI_COMMIT_REF_NAME
  paths:
    - node_modules/

.env_script:
  before_script:
    - yarn config set registry https://registry.npmmirror.com/
    - yarn config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
    - yarn config set disturl https://npmmirror.com/mirrors/node/
    - yarn config set sharp_binary_host "https://npmmirror.com/mirrors/sharp"
    - yarn config set sharp_libvips_binary_host "https://npmmirror.com/mirrors/sharp-libvips"
    # - yarn add sharp

lint:
  extends: .env_script
  image: node:18-bullseye
  stage: lint
  tags:
    - docker
  rules:
    # 只有合并请求才进行eslint检查
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
  script:
    - yarn
    - yarn lint

# 自动添加tag和发布release
release:
  stage: release
  image: linfulong/semantic-release:latest
  script:
    - npx semantic-release
  only:
    - main
  tags:
    - docker
  cache: {}
  except:
    - api
  artifacts:
    expire_in: 3 day
    paths:
      - package.json
  retry: 1

deploy-build:
  extends: .env_script
  image: node:18-bullseye
  stage: build
  tags:
    - docker
  only:
    - main
    - web
  script:
    - rm -rf $BuildDir
    # 先设置vue脚手架的环境配置$Enable_License 从GitLab CI上触发Pipeline时设置，默认为true，以开启授权验证
    - ./enableLicense.sh $Enable_License
    - yarn
    - yarn build
  dependencies:
    - release
  artifacts:
    expire_in: 60 day
    name: "webclient"
    paths:
      - $BuildDir
      - package.json
  retry: 1

deploy-web:
  # extends: .env_script
  image: node:18-bullseye
  stage: deploy
  tags:
    - docker
  rules:
    - if: $CI_COMMIT_BRANCH != "main"
      when: never
    - if: $Enable_License == "true"
      when: always
    - when: manual
  script:
    - export version=$(node -p "require('./package.json').version")
    - echo "deploy web version:$version, $(date +"%Y-%m-%d %H:%M:%S")"
    - '[ ! -d $DeployDir ] && mkdir -p $DeployDir && chmod 777 $DeployDir'
    - cp -r -u $BuildDir/webclient-$version/* $DeployDir/
    - node ./clearDeployCache.cjs -s $BuildDir/webclient-$version/ -t $DeployDir/
  dependencies:
    - deploy-build

pkg-push:
  image: node:18-bullseye
  stage: deploy
  tags:
    - docker
  rules:
    - if: $CI_COMMIT_BRANCH != "main"
      when: never
    - if: $Enable_License == "true"
      when: always
    - when: manual
  dependencies:
    - deploy-build
  script:
    - export version=$(node -p "require('./package.json').version")
    - echo "$PkgBranch version:\ $version"
    - cd $BuildDir && mkdir -p $PkgBranch && mv webclient-$version $PkgBranch/$version
    - ls -hl $PkgBranch
    - if [[ $CI_COMMIT_TITLE == *"release"* ]]; then git init && git config user.name "linfl" && git config user.email "<EMAIL>" &&  git remote add origin $PkgpushGit && git branch -m $PkgBranch &&  curl --header "PRIVATE-TOKEN:$automr" "$Bf8100pkgFiles/.gitlab-ci.yml/raw?ref=main" -o .gitlab-ci.yml   && git add . && git commit -m "$version" && git push -f origin $PkgBranch ; fi
  cache: {}

## 新版UI编译
#build-new-ui:
#  extends: .env_script
#  image: node:18-bullseye
#  stage: build
#  tags:
#    - docker
#  only:
#    - 173-8100-ui
#  script:
#    - rm -rf $BuildDir
#    # 先设置vue脚手架的环境配置$Enable_License 从GitLab CI上触发Pipeline时设置，默认为true，以开启授权验证
#    - ./enableLicense.sh $Enable_License
#    - yarn
#    - yarn build
#  artifacts:
#    expire_in: 60 day
#    name: new_ui
#    paths:
#      - $BuildDir
#      - package.json
#  retry: 1
#  dependencies:
#    - release
#
## 新版UI部署
#deploy-new-ui:
#  # extends: .env_script
#  image: node:18-bullseye
#  stage: deploy
#  tags:
#    - docker
#  rules:
#    - if: $CI_COMMIT_BRANCH != "173-8100-ui"
#      when: never
#    - when: always
#  script:
#    - export version=$(node -p "require('./package.json').version")
#    - echo "deploy web version:$version"
#    - rm -rf $DeployDir/new-ui && cp -r -u $BuildDir/webclient-$version $DeployDir/new-ui
#  dependencies:
#    - build-new-ui
